# Honeycomb.io Setup for Devlog

## Overview
We've integrated Honeycomb.io observability to track platform health, performance issues, and errors in real-time. This replaces <PERSON><PERSON>'s rate-limited error tracking with comprehensive observability.

## What Honeycomb Tracks

### Automatic Instrumentation
- **Page Loads**: Complete timing breakdown
- **API Calls**: All Supabase operations with timing
- **User Interactions**: Clicks, form submissions, navigation
- **JavaScript Errors**: With full stack traces and context
- **Core Web Vitals**: LCP, FID, CLS, FCP, TTFB, INP
- **Resource Loading**: CSS, JS, images

### Custom Instrumentation
- **Document Save Operations**: Duration, block count, success/failure
- **Block Rendering**: Slow renders (>50ms) are tracked
- **Authentication**: Login/logout events
- **Memory Usage**: Tracked in development mode
- **Database Queries**: With performance metrics

## Vercel Environment Variable Setup

### Step 1: Get Your Honeycomb API Key
1. Log into your Honeycomb account at https://ui.honeycomb.io/
2. Go to **Account Settings** → **API Keys**
3. Create a new API key with "Send Events" permission
4. Copy the API key

### Step 2: Add to Vercel
1. Go to your Vercel project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add the following variable:

```
Name: VITE_HONEYCOMB_API_KEY
Value: [Your Honeycomb API Key]
Environment: Production, Preview, Development
```

### Step 3: Redeploy
After adding the environment variable, trigger a new deployment:
- Push a commit to GitHub, OR
- Click "Redeploy" in Vercel dashboard

## Viewing Data in Honeycomb

### Dashboards to Create
1. **User Experience Dashboard**
   - Page load times by route
   - Core Web Vitals trends
   - User interaction latency

2. **Error Dashboard**
   - JavaScript errors by type
   - Failed API calls
   - Error rate by user

3. **Performance Dashboard**
   - Slow document saves (>500ms)
   - Slow block renders (>50ms)
   - Memory usage trends

### Creating Queries
Example queries to get started:

**Find Slow Saves:**
```
service_name = "devlog"
name = "supabase.saveDocument"
duration_ms > 500
```

**Track Error Rate:**
```
service_name = "devlog"
error = true
COUNT
```

**Monitor Memory Usage:**
```
service_name = "devlog"
name = "browser.memory_check"
HEATMAP(memory.usage_percentage)
```

## Debugging with Honeycomb

### Finding Root Causes
1. **Failed to execute 'contains' on 'Node' Error**:
   - Query: `error.message contains "contains"`
   - Shows exact component and user action that triggered it

2. **Slow Document Saves**:
   - Query: `name = "supabase.saveDocument" duration_ms > 1000`
   - Shows which documents are slow and why

3. **Memory Leaks**:
   - Query: `name = "browser.memory_check" memory.usage_percentage > 80`
   - Identifies memory growth patterns

### Trace View
Click on any span to see:
- Full trace of the operation
- All related API calls
- Timing breakdown
- User context
- Error details

## Development Mode

In development, Honeycomb:
- Captures 100% of traces (vs 10% in production)
- Logs to console with `[Honeycomb]` prefix
- Shows trace links in console
- Tracks memory usage every 30 seconds

## Testing the Integration

1. **Verify Installation**:
   - Open browser console
   - Look for: `[Honeycomb] SDK initialized successfully`

2. **Test Error Tracking**:
   - Trigger an error in the app
   - Check Honeycomb UI for the error event

3. **Test Performance Tracking**:
   - Save a document
   - Query Honeycomb for `name = "supabase.saveDocument"`

## Troubleshooting

### No Data in Honeycomb
- Check environment variable is set correctly
- Verify API key has "Send Events" permission
- Check browser console for errors
- Ensure you're not blocking tracking in browser

### Missing User Context
- User context is set after login
- Check `setUserContext` is called in App.jsx

### Performance Impact
- Honeycomb adds <5ms overhead
- Sampling reduces data volume in production
- No rate limits unlike Sentry

## Benefits Over Previous Setup

### vs Sentry
- ✅ No rate limits (was hitting 429 errors)
- ✅ Full trace context (not just errors)
- ✅ Performance monitoring included
- ✅ Custom business metrics

### vs Console Logging
- ✅ Persistent across deployments
- ✅ Searchable and queryable
- ✅ Correlates errors with user actions
- ✅ Production visibility

## Next Steps

1. **Create Alerts**:
   - Error rate > 5%
   - P95 latency > 3 seconds
   - Memory usage > 100MB

2. **Add More Custom Spans**:
   - Search operations
   - Image uploads
   - Real-time subscriptions

3. **Create SLOs**:
   - 99.9% uptime
   - <500ms save latency
   - <100ms interaction response

## Support

- Honeycomb Docs: https://docs.honeycomb.io/
- Honeycomb Slack: https://www.honeycomb.io/slack/
- GitHub Issues: https://github.com/honeycombio/honeycomb-opentelemetry-web/issues