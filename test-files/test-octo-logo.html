<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>DevLog Octo-D Logo Test</title>
  <style>
    body { 
      background: #050d1a; 
      color: #e0e7ff; 
      padding: 40px; 
      font-family: system-ui;
    }
    .test-section { 
      margin: 40px 0; 
      border-bottom: 1px solid #1e3a5f; 
      padding-bottom: 20px;
    }
    .logo-container {
      display: flex;
      gap: 40px;
      align-items: center;
      margin: 20px 0;
      flex-wrap: wrap;
    }
    .logo-item {
      text-align: center;
    }
    .logo-item p {
      margin-top: 10px;
      font-size: 14px;
      color: #94a3b8;
    }
    .gradient-text {
      background: linear-gradient(to right, #10b981, #0a7d57);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: 600;
      font-size: 20px;
    }
  </style>
</head>
<body>
  <h1>DevLog Octo-D Logo - GitHub-Inspired Design</h1>
  
  <div class="test-section">
    <h2>Logo Variants</h2>
    <div class="logo-container">
      <div class="logo-item">
        <svg width="64" height="64" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="devlog-gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stop-color="#10b981" />
              <stop offset="100%" stop-color="#0a7d57" />
            </linearGradient>
          </defs>
          
          <!-- D Letter Head -->
          <path d="M 10 4 L 14 4 C 19 4 22 8 22 12 C 22 16 19 20 14 20 L 10 20 L 10 4 Z" fill="url(#devlog-gradient1)" />
          <path d="M 13 8 L 14 8 C 16.5 8 18 9.5 18 12 C 18 14.5 16.5 16 14 16 L 13 16 L 13 8 Z" fill="#050d1a" />
          
          <!-- Tentacles -->
          <path d="M 11 18 Q 11 23 9 25 Q 7 27 6 26 Q 5 25 6 23 Q 7 21 8 19" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 14 19 Q 14 24 14 26 Q 14 28 13 27.5 Q 12 27 12 25 Q 12 23 12.5 21" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 17 19 Q 17 24 17 26 Q 17 28 18 27.5 Q 19 27 19 25 Q 19 23 18.5 21" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 20 18 Q 20 23 22 25 Q 24 27 25 26 Q 26 25 25 23 Q 24 21 23 19" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 8.5 17 Q 8 21 6 22 Q 4 23 3.5 22 Q 3 21 4 20 Q 5 19 6 18" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 22.5 17 Q 23 21 25 22 Q 27 23 27.5 22 Q 28 21 27 20 Q 26 19 25 18" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
        </svg>
        <p>LogoMinimal (64px)</p>
      </div>
      
      <div class="logo-item">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="devlog-gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stop-color="#10b981" />
              <stop offset="100%" stop-color="#0a7d57" />
            </linearGradient>
          </defs>
          
          <path d="M 10 4 L 14 4 C 19 4 22 8 22 12 C 22 16 19 20 14 20 L 10 20 L 10 4 Z" fill="url(#devlog-gradient2)" />
          <path d="M 13 8 L 14 8 C 16.5 8 18 9.5 18 12 C 18 14.5 16.5 16 14 16 L 13 16 L 13 8 Z" fill="#050d1a" />
          
          <path d="M 11 18 Q 11 23 9 25 Q 7 27 6 26 Q 5 25 6 23 Q 7 21 8 19" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 14 19 Q 14 24 14 26 Q 14 28 13 27.5 Q 12 27 12 25 Q 12 23 12.5 21" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 17 19 Q 17 24 17 26 Q 17 28 18 27.5 Q 19 27 19 25 Q 19 23 18.5 21" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 20 18 Q 20 23 22 25 Q 24 27 25 26 Q 26 25 25 23 Q 24 21 23 19" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 8.5 17 Q 8 21 6 22 Q 4 23 3.5 22 Q 3 21 4 20 Q 5 19 6 18" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 22.5 17 Q 23 21 25 22 Q 27 23 27.5 22 Q 28 21 27 20 Q 26 19 25 18" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
        </svg>
        <p>Standard (32px)</p>
      </div>
      
      <div class="logo-item">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M 7 3 L 10 3 C 14 3 17 6 17 10 C 17 14 14 17 10 17 L 7 17 L 7 3 Z" fill="#10b981" />
          <path d="M 10 6 L 10.5 6 C 12.5 6 14 7.5 14 10 C 14 12.5 12.5 14 10.5 14 L 10 14 L 10 6 Z" fill="#050d1a" />
          <path d="M 8 15 Q 8 18 7 19 M 11 16 Q 11 19 11 20 M 14 16 Q 14 19 14 20 M 16 15 Q 16 18 17 19" stroke="#10b981" stroke-width="1.5" fill="none" stroke-linecap="round" />
        </svg>
        <p>LogoIcon (24px)</p>
      </div>
      
      <div class="logo-item">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M 7 3 L 10 3 C 14 3 17 6 17 10 C 17 14 14 17 10 17 L 7 17 L 7 3 Z" fill="#10b981" />
          <path d="M 10 6 L 10.5 6 C 12.5 6 14 7.5 14 10 C 14 12.5 12.5 14 10.5 14 L 10 14 L 10 6 Z" fill="#050d1a" />
          <path d="M 8 15 Q 8 18 7 19 M 11 16 Q 11 19 11 20 M 14 16 Q 14 19 14 20 M 16 15 Q 16 18 17 19" stroke="#10b981" stroke-width="1.5" fill="none" stroke-linecap="round" />
        </svg>
        <p>Favicon (16px)</p>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>Logo with Text</h2>
    <div class="logo-container">
      <div style="display: flex; align-items: center; gap: 8px;">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="devlog-gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stop-color="#10b981" />
              <stop offset="100%" stop-color="#0a7d57" />
            </linearGradient>
          </defs>
          
          <path d="M 10 4 L 14 4 C 19 4 22 8 22 12 C 22 16 19 20 14 20 L 10 20 L 10 4 Z" fill="url(#devlog-gradient3)" />
          <path d="M 13 8 L 14 8 C 16.5 8 18 9.5 18 12 C 18 14.5 16.5 16 14 16 L 13 16 L 13 8 Z" fill="#050d1a" />
          
          <path d="M 11 18 Q 11 23 9 25 Q 7 27 6 26 Q 5 25 6 23 Q 7 21 8 19" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 14 19 Q 14 24 14 26 Q 14 28 13 27.5 Q 12 27 12 25 Q 12 23 12.5 21" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 17 19 Q 17 24 17 26 Q 17 28 18 27.5 Q 19 27 19 25 Q 19 23 18.5 21" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 20 18 Q 20 23 22 25 Q 24 27 25 26 Q 26 25 25 23 Q 24 21 23 19" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 8.5 17 Q 8 21 6 22 Q 4 23 3.5 22 Q 3 21 4 20 Q 5 19 6 18" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
          <path d="M 22.5 17 Q 23 21 25 22 Q 27 23 27.5 22 Q 28 21 27 20 Q 26 19 25 18" stroke="#10b981" stroke-width="2" fill="none" stroke-linecap="round" />
        </svg>
        <span class="gradient-text">DevLog</span>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>Design Concept</h2>
    <p>This logo combines the playful, developer-friendly aesthetic of GitHub's Octocat with the letter "D" from DevLog. The tentacles represent the interconnected nature of documentation and development logs, while the gradient green colors convey growth and innovation.</p>
    
    <h3>Key Features:</h3>
    <ul>
      <li>GitHub-inspired octopus design with "D" letter head</li>
      <li>Six flowing tentacles representing connectivity and flexibility</li>
      <li>Gradient from #10b981 to #0a7d57 for depth and luxury</li>
      <li>Scalable design that works from 16px favicons to large displays</li>
      <li>Animated version with gently waving tentacles for loading states</li>
    </ul>
  </div>
  
  <div class="test-section">
    <h2>Color Palette</h2>
    <div style="display: flex; gap: 20px; margin-top: 20px;">
      <div>
        <div style="background: #10b981; width: 100px; height: 60px; border-radius: 4px;"></div>
        <p style="margin-top: 10px;">#10b981<br>Primary Green</p>
      </div>
      <div>
        <div style="background: #0a7d57; width: 100px; height: 60px; border-radius: 4px;"></div>
        <p style="margin-top: 10px;">#0a7d57<br>Dark Green</p>
      </div>
      <div>
        <div style="background: #050d1a; width: 100px; height: 60px; border-radius: 4px; border: 1px solid #333;"></div>
        <p style="margin-top: 10px;">#050d1a<br>Background</p>
      </div>
    </div>
  </div>
</body>
</html>