<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Auth Fix</title>
</head>
<body>
    <h1>Authentication Test</h1>
    <div id="status">Loading...</div>
    <div id="results"></div>
    
    <script type="module">
        import { createClient } from 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/+esm';
        
        const supabaseUrl = 'https://zqcjipwiznesnbgbocnu.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpxY2ppcHdpem5lc25iZ2JvY251Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTE2MjI3ODgsImV4cCI6MjAyNzE5ODc4OH0.o9SrqnEOA1LfhFF2XPvnuVaJ0dWRQI88T9Xf-R1gnEg';
        
        const supabase = createClient(supabaseUrl, supabaseAnonKey);
        
        const statusEl = document.getElementById('status');
        const resultsEl = document.getElementById('results');
        
        async function testAuth() {
            try {
                // Get current session
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                
                if (sessionError) {
                    statusEl.textContent = 'Session Error: ' + sessionError.message;
                    return;
                }
                
                if (!session) {
                    statusEl.textContent = 'No active session. Please sign in through the app.';
                    return;
                }
                
                statusEl.textContent = 'Session found for user: ' + session.user.email;
                
                // Test folders query
                const { data: folders, error: foldersError } = await supabase
                    .from('folders')
                    .select('*')
                    .limit(5);
                
                let html = '<h2>Test Results:</h2>';
                
                if (foldersError) {
                    html += '<p style="color: red;">❌ Folders Query Failed: ' + foldersError.message + '</p>';
                } else {
                    html += '<p style="color: green;">✅ Folders Query Success! Found ' + (folders?.length || 0) + ' folders</p>';
                }
                
                // Test documents query
                const { data: docs, error: docsError } = await supabase
                    .from('documents')
                    .select('id, title')
                    .limit(5);
                
                if (docsError) {
                    html += '<p style="color: red;">❌ Documents Query Failed: ' + docsError.message + '</p>';
                } else {
                    html += '<p style="color: green;">✅ Documents Query Success! Found ' + (docs?.length || 0) + ' documents</p>';
                }
                
                // Test auth.uid() in RPC
                const { data: authTest, error: authError } = await supabase
                    .rpc('get_user_subscription_status', { input_user_id: session.user.id });
                
                if (authError) {
                    html += '<p style="color: orange;">⚠️ RPC Auth Test Failed: ' + authError.message + '</p>';
                } else {
                    html += '<p style="color: green;">✅ RPC Auth Test Success!</p>';
                }
                
                resultsEl.innerHTML = html;
                
            } catch (err) {
                statusEl.textContent = 'Test Error: ' + err.message;
                console.error('Full error:', err);
            }
        }
        
        // Run test on load
        testAuth();
        
        // Also listen for auth changes
        supabase.auth.onAuthStateChange((event, session) => {
            console.log('Auth event:', event);
            if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
                testAuth();
            }
        });
    </script>
</body>
</html>