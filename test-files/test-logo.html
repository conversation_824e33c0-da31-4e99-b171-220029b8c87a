<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Logo Test</title>
  <style>
    body { 
      background: #050d1a; 
      color: #e0e7ff; 
      padding: 40px; 
      font-family: system-ui;
    }
    .test-section { 
      margin: 40px 0; 
      border-bottom: 1px solid #1e3a5f; 
      padding-bottom: 20px;
    }
    .logo-container {
      display: flex;
      gap: 20px;
      align-items: center;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>Logo Test - World Icon with Soft Green Color</h1>
  
  <div class="test-section">
    <h2>Logo Variants</h2>
    <div class="logo-container">
      <div>
        <p>LogoMinimal (32px)</p>
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="2" y1="12" x2="22" y2="12"></line>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
        </svg>
      </div>
      
      <div>
        <p>LogoIcon (24px)</p>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="2" y1="12" x2="22" y2="12"></line>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
        </svg>
      </div>
      
      <div>
        <p>LogoWithText</p>
        <div style="display: flex; align-items: center; gap: 8px;">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="2" y1="12" x2="22" y2="12"></line>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
          </svg>
          <span style="font-weight: 600; font-size: 20px;">DevLog</span>
        </div>
      </div>
      
      <div>
        <p>LogoAnimated (rotating)</p>
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2" 
             style="animation: spin 3s linear infinite;">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="2" y1="12" x2="22" y2="12"></line>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
        </svg>
      </div>
    </div>
  </div>
  
  <div class="test-section">
    <h2>Color Information</h2>
    <p>Color Used: #10b981 (Soft Green - matches accent-green from Tailwind config)</p>
    <div style="background: #10b981; width: 100px; height: 40px; margin-top: 10px; border-radius: 4px;"></div>
  </div>
  
  <style>
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</body>
</html>