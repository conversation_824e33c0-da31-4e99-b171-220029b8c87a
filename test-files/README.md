# Test Files

This directory contains test files, debugging scripts, and development utilities for the Devlog project.

## Contents

### HTML Test Files
- `test-ai-block.html` - AI block functionality testing
- `test-auth-fix.html` - Authentication fix testing
- `test-autosave.html` - Auto-save functionality testing
- `test-block-animation.html` - Block animation testing
- `test-block-controls.html` - Block controls testing
- `test-enhanced-animation.html` - Enhanced animation testing
- `test-logo.html` - Logo display testing
- `test-octo-logo.html` - Octo logo testing
- `test-supabase.html` - Supabase integration testing

### JavaScript Test Files
- `test-auth.js` - Authentication testing script
- `test-document-save.js` - Document save functionality testing
- `test-folder-document-creation.js` - Folder and document creation testing

### Debug Scripts
- `debug-ai-block-save.js` - AI block save debugging
- `debug_app_save.js` - Application save debugging
- `diagnose-db.js` - Database diagnostic script

### Validation Scripts
- `validate-sitemap.js` - Sitemap validation
- `validate-sitemap-simple.js` - Simplified sitemap validation

## Usage

These files are used for:
- Manual testing of specific features
- Debugging production issues
- Isolated testing of individual components
- Database diagnostics and validation
- Sitemap and SEO validation

## Note

These files are for development purposes only and should not be deployed to production. They may contain test data, debugging information, or incomplete implementations.