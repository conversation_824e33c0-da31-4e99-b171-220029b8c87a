index-BUOgG6nL.js:26 Using optimized Supabase client
index-BUOgG6nL.js:26 Global auto-save manager initialized with defensive wrappers
index-BUOgG6nL.js:26 IndexedDB initialized successfully
index-BUOgG6nL.js:26 Starting auto-save with interval: 1 seconds
index-BUOgG6nL.js:26 SW registered: ServiceWorkerRegistration {installing: null, waiting: null, active: ServiceWorker, navigationPreload: NavigationPreloadManager, scope: 'https://www.devlog.design/', …}
index-BUOgG6nL.js:26 [Supabase] Restored existing session: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 [Supabase] Auth event: INITIAL_SESSION
index-BUOgG6nL.js:26 [AuthContext] Auth state change received: INITIAL_SESSION {mounted: true, hasSession: true, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b'}
index-BUOgG6nL.js:26 Dashboard: Starting to load entries...
index-BUOgG6nL.js:26 Using Supabase for storage
index-BUOgG6nL.js:26 SupabaseAdapter: Init with provided userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 Dashboard: Storage initialized (42ms)
index-BUOgG6nL.js:26 SupabaseAdapter: getDocuments called
index-BUOgG6nL.js:26 SupabaseAdapter: Querying documents for user 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 SupabaseAdapter: Documents query completed in 396ms
index-BUOgG6nL.js:26 SupabaseAdapter: Found 76 documents
index-BUOgG6nL.js:26 SupabaseAdapter: Found 0 unsynced documents in IndexedDB
index-BUOgG6nL.js:26 SupabaseAdapter: Total documents after merge: 76
index-BUOgG6nL.js:26 SupabaseAdapter: Returning 76 documents
index-BUOgG6nL.js:26 Dashboard: Loaded 76 entries (417ms)
index-BUOgG6nL.js:26 Dashboard: Total load time: 463ms
index-BUOgG6nL.js:26 SupabaseAdapter: Getting projects...
index-BUOgG6nL.js:26 SupabaseAdapter: Found 3 projects
index-BUOgG6nL.js:26 Dashboard: Loaded 3 projects
index-BUOgG6nL.js:26 Dashboard: Setting isLoading to false
index-BUOgG6nL.js:26 Loading folders for user: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 VirtualizedGrid - scroll container: {"totalHeight":10020,"containerHeight":787,"hasOverflow":true,"parentIsCardsContainer":true}
index-BUOgG6nL.js:26 VirtualizedGrid - scroll container: {"totalHeight":5912,"containerHeight":787,"hasOverflow":true,"parentIsCardsContainer":true}
index-BUOgG6nL.js:26 Loaded 42 folders (19 root folders)
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 PaginatedBlockLoader: Loading page 0 (offset: 0, limit: 50) for document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 PaginatedBlockLoader: Loaded 6 blocks for page 0 of document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 ExpandedView: Initial load period complete, enabling saves
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
f_ @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SessionCache: Cached 7 blocks for document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 7, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 7, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:56:29.616479+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
a_ @ index-BUOgG6nL.js:106
n_ @ index-BUOgG6nL.js:106
K1 @ index-BUOgG6nL.js:106
Y1 @ index-BUOgG6nL.js:106
h_ @ index-BUOgG6nL.js:106
$c @ index-BUOgG6nL.js:106
u_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
a_ @ index-BUOgG6nL.js:106
n_ @ index-BUOgG6nL.js:106
K1 @ index-BUOgG6nL.js:106
Y1 @ index-BUOgG6nL.js:106
h_ @ index-BUOgG6nL.js:106
$c @ index-BUOgG6nL.js:106
u_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 8, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 8, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:56:41.619699+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
a_ @ index-BUOgG6nL.js:106
n_ @ index-BUOgG6nL.js:106
K1 @ index-BUOgG6nL.js:106
Y1 @ index-BUOgG6nL.js:106
h_ @ index-BUOgG6nL.js:106
$c @ index-BUOgG6nL.js:106
u_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
a_ @ index-BUOgG6nL.js:106
n_ @ index-BUOgG6nL.js:106
K1 @ index-BUOgG6nL.js:106
Y1 @ index-BUOgG6nL.js:106
h_ @ index-BUOgG6nL.js:106
$c @ index-BUOgG6nL.js:106
u_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 9, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 9, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:56:51.849282+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Save Debug: {blockId: '5f37e8bd-c3aa-4b20-a57b-b4cf5a10fdfb', hasMessages: false, messageCount: 0, metadataKeys: Array(0), hasMessagesInMetadata: false}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 10, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 10, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 Auto-save: Found 1 documents with unsaved changes
index-BUOgG6nL.js:26 AutoSave: Saving all pending changes...
index-BUOgG6nL.js:26 AutoSave: Completed saving all documents
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 10, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 10, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:57:04.771178+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Save Debug: {blockId: '5f37e8bd-c3aa-4b20-a57b-b4cf5a10fdfb', hasMessages: false, messageCount: 0, metadataKeys: Array(0), hasMessagesInMetadata: false}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:57:04.853679+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Save Debug: {blockId: '5f37e8bd-c3aa-4b20-a57b-b4cf5a10fdfb', hasMessages: false, messageCount: 0, metadataKeys: Array(0), hasMessagesInMetadata: false}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 Auto-save: Found 1 documents with unsaved changes
index-BUOgG6nL.js:26 AutoSave: Saving all pending changes...
index-BUOgG6nL.js:26 AutoSave: Completed saving all documents
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 10, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 10, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:57:08.779497+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Save Debug: {blockId: '5f37e8bd-c3aa-4b20-a57b-b4cf5a10fdfb', hasMessages: false, messageCount: 0, metadataKeys: Array(0), hasMessagesInMetadata: false}
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called {documentId: '5227b0da-8532-4307-a84a-3c01bab5e09a', blockCount: 11, folderId: 'none', hasCreatedAt: true, title: 'My First Devlog Document'}
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', blockCount: 11, userId: '8eac28e6-0127-40d1-ba55-c10cbe52a32b', isNew: false}
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: {id: '5227b0da-8532-4307-a84a-3c01bab5e09a', title: 'My First Devlog Document', updated_at: '2025-08-13T06:57:10.697747+00:00'}
index-BUOgG6nL.js:26 🔵 AI Block Save Debug: {blockId: '5f37e8bd-c3aa-4b20-a57b-b4cf5a10fdfb', hasMessages: false, messageCount: 0, metadataKeys: Array(0), hasMessagesInMetadata: false}
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
f_ @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
setTimeout
(anonymous) @ index-BUOgG6nL.js:29
(anonymous) @ index-BUOgG6nL.js:935
(anonymous) @ index-BUOgG6nL.js:935
Mc @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
ws @ index-BUOgG6nL.js:106
U1 @ index-BUOgG6nL.js:106
o_ @ index-BUOgG6nL.js:106
Cf @ index-BUOgG6nL.js:106
f_ @ index-BUOgG6nL.js:106
U @ index-BUOgG6nL.js:91
