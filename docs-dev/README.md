# Development Documentation

This directory contains all development-related documentation for the Devlog project.

## Directory Structure

### 📁 architecture/
System design and architectural decisions:
- `ENTERPRISE_TRANSFORMATION_MASTERPLAN.md` - Long-term scaling and feature roadmap
- `PHASE_0_IMPLEMENTATION_GUIDE.md` - Initial implementation phases
- `PERFORMANCE_OPTIMIZATIONS.md` - Performance improvement strategies

### 📁 fixes/
Bug fixes and solutions documentation:
- `DOCUMENT_PERSISTENCE_*.md` - Document persistence issue fixes (multiple versions)
- `FIX_AUTH_EVENT_HANDLERS.md` - Authentication event handler fixes
- `FIX_REPEATED_CALLS.md` - Repeated API calls fix
- `MEDIUM_VIEWPORT_AUTH_FIX_SUMMARY.md` - Medium viewport authentication fixes
- `auth-fix-summary.md` - Authentication fix summary
- `SUPABASE_TOKEN_REFRESH_FIX.md` - Token refresh mechanism fixes
- `VERCEL_BUILD_FIX.md` - Vercel build configuration fixes

### 📁 guides/
Implementation and setup guides:
- `SENTRY_SETUP_GUIDE.md` - Error monitoring setup
- `VERCEL_DEPLOYMENT_GUIDE.md` - Deployment configuration
- `SUPABASE_DATA_PROTECTION_GUIDE.md` - Database security practices
- `RESPONSIVE_DESIGN_GUIDE.md` - Mobile responsiveness implementation
- `SEO_FIX_IMPLEMENTATION_GUIDE.md` - SEO optimization guide
- `PREMIUM_FOLDER_SYSTEM_GUIDE.md` - Folder organization system
- `VSCODE_FOLDER_IMPLEMENTATION_GUIDE.md` - VSCode-style explorer
- `SEO_AND_GITHUB_OAUTH_GUIDE.md` - SEO and OAuth implementation

### 📁 sql-fixes/
Database migration fixes and SQL scripts:
- `FINAL_SECURITY_FIXES.sql` - Security-related database fixes
- Various `FIX_*.sql` files - Specific database issue fixes
- `fix_sharing_rls.sql` - Row Level Security fixes for sharing

## Usage

These documents are primarily for developers working on the codebase. They contain:
- Technical implementation details
- Bug fix explanations and solutions
- Architecture decisions and reasoning
- Setup and deployment instructions