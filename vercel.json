{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "functions": {"api/**/*.js": {"maxDuration": 10}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}