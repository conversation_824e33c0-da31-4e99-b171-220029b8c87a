{"compilerOptions": {"target": "es2021", "lib": ["es2021", "webworker"], "module": "es2022", "moduleResolution": "node", "types": ["@cloudflare/workers-types", "./worker-configuration.d.ts"], "resolveJsonModule": true, "allowJs": true, "noEmit": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true}, "include": ["src/**/*", "worker-configuration.d.ts"], "exclude": ["node_modules", "dist"]}