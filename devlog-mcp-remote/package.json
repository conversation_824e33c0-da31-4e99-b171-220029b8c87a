{"name": "@devlog/mcp-remote", "version": "1.0.0", "description": "Remote MCP server for Devlog on Cloudflare Workers", "type": "module", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "tail": "wrangler tail", "types": "wrangler types", "build": "tsc --noEmit", "test": "vitest"}, "keywords": ["mcp", "model-context-protocol", "devlog", "cloudflare-workers", "ai"], "author": "Devlog Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.17.2"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250812.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0", "wrangler": "^4.28.1"}}