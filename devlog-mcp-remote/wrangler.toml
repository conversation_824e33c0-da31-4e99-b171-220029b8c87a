name = "devlog-mcp"
main = "src/index.ts"
compatibility_date = "2025-01-15"
compatibility_flags = ["nodejs_compat"]

# Build configuration (disabled for now due to TS errors)
# [build]
# command = "npm run build"
# watch_dir = "src"

# Bind to KV namespace for caching
[[kv_namespaces]]
binding = "CACHE"
id = "00264a64187c44b896c34241822355de"

# Bind to Durable Objects for session management
[[durable_objects.bindings]]
name = "SESSION"
class_name = "MCPSession"

# Critical fix: Use new_sqlite_classes for 2025
[[migrations]]
tag = "v1_initial"
new_sqlite_classes = ["MCPSession"]

# Environment variables
[vars]
SUPABASE_URL = "https://zqcjipwiznesnbgbocnu.supabase.co"
MCP_SERVER_NAME = "Devlog MCP Server"
MCP_VERSION = "1.0.0"

# Production environment
[env.production]
name = "devlog-mcp-production"
# route = "mcp.devlog.design/*"  # Uncomment when domain is set up

# Copy vars to production environment
[env.production.vars]
SUPABASE_URL = "https://zqcjipwiznesnbgbocnu.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpxY2ppcHdpem5lc25iZ2JvY251Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NjgwOTgsImV4cCI6MjA2NjU0NDA5OH0.GWgZOH0sKP2z2_IGG6_omnJwpefSRnI353hmu729ahg"
MCP_SERVER_NAME = "Devlog MCP Server"
MCP_VERSION = "1.0.0"

# Copy KV namespace to production
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "00264a64187c44b896c34241822355de"

# Copy Durable Objects to production
[[env.production.durable_objects.bindings]]
name = "SESSION"
class_name = "MCPSession"

# Development/staging environment
[env.staging]
name = "devlog-mcp-staging"
route = "mcp-staging.devlog.design/*"

# Development settings
[dev]
port = 8787
local_protocol = "http"