# Sentry Configuration
# Get your DSN from https://sentry.io/settings/[org-slug]/projects/[project-slug]/keys/
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# Sentry Build-time Configuration (for source maps)
# Organization slug from your Sentry account
SENTRY_ORG=your-org-slug

# Project name in Sentry
SENTRY_PROJECT=devlog

# Auth token for uploading source maps
# Create at: https://sentry.io/settings/account/api/auth-tokens/
# Required scopes: project:releases (create releases), org:read (read org data)
SENTRY_AUTH_TOKEN=sntrys_your-auth-token

# Optional: Use EU data center if required for GDPR
# VITE_SENTRY_DSN=https://<EMAIL>/project-id

# These are automatically set by Vercel:
# VERCEL_ENV (production, preview, development)
# VERCEL_GIT_COMMIT_SHA (used for release tracking)

# Existing Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key