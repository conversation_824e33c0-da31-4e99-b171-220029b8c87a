{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "checkJs": true, "allowJs": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@types/*": ["src/types/*"], "@components/*": ["src/components/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@contexts/*": ["src/contexts/*"], "@pages/*": ["src/pages/*"], "@stores/*": ["src/stores/*"]}, "typeRoots": ["./src/types", "./node_modules/@types"], "lib": ["ES2022", "DOM", "DOM.Iterable"], "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist", "build", "public"]}