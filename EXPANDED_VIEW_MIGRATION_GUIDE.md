# ExpandedViewEnhanced Migration Guide

## Overview
This guide shows how to migrate ExpandedViewEnhanced.jsx from 27 useState calls to 0 by using the new `useExpandedView` hook.

## What's Been Done

### 1. Created useExpandedView Hook
- Location: `/src/hooks/useExpandedView.js`
- Consolidates all 27 useState calls into Zustand stores
- Provides exact same API as original useState calls
- Includes helper functions for tag management

### 2. Updated editorStore
- Added all document-specific state (title, tags, backlinks)
- Added block selector state
- Added view mode state
- Added drag/drop state
- Added save status tracking

### 3. Updated uiStore
- Added `deleteDocument` and `shareDocument` modals
- Added `deleteDocument` loading state

### 4. Created Test Infrastructure
- `testExpandedViewMigration.js` - Verifies migration completeness

## Migration Steps

### Step 1: Import the Hook
Replace this:
```javascript
import { useState, useEffect, useRef, useCallback, startTransition, forwardRef, useImperativeHandle } from 'react';
```

With this:
```javascript
import { useEffect, useRef, useCallback, startTransition, forwardRef, useImperativeHandle } from 'react';
import { useExpandedView } from '../hooks/useExpandedView';
```

### Step 2: Replace useState Calls
At the top of the component, replace all 27 useState calls with:
```javascript
const {
  // Document data
  document,
  title,
  setTitle,
  tags,
  setTags,
  
  // Block selector
  showBlockSelector,
  setShowBlockSelector,
  selectorPosition,
  setSelectorPosition,
  
  // Title editing
  isEditingTitle,
  setIsEditingTitle,
  
  // Links
  backlinks,
  setBacklinks,
  
  // Block focus
  focusedBlockId,
  setFocusedBlockId,
  hoveredBlockId,
  setHoveredBlockId,
  
  // Tag editing
  isAddingTag,
  setIsAddingTag,
  newTag,
  setNewTag,
  editingTagIndex,
  setEditingTagIndex,
  editingTagValue,
  setEditingTagValue,
  addTag,
  removeTag,
  updateTag,
  
  // Drag and drop
  draggedBlockId,
  setDraggedBlockId,
  dropTargetId,
  setDropTargetId,
  dropPosition,
  setDropPosition,
  
  // View modes
  viewMode,
  setViewMode,
  selectedLineBlockId,
  setSelectedLineBlockId,
  linesScrollProgress,
  setLinesScrollProgress,
  
  // UI states
  showDeleteConfirm,
  setShowDeleteConfirm,
  isDeleting,
  setIsDeleting,
  showShareDialog,
  setShowShareDialog,
  
  // Save status
  saveStatus,
  setSaveStatus,
  
  // Internal update tracking
  isInternalUpdate,
  setIsInternalUpdate
} = useExpandedView(entry.id);
```

### Step 3: Remove Old useState Declarations
Delete these lines:
```javascript
const [showBlockSelector, setShowBlockSelector] = useState(false);
const [selectorPosition, setSelectorPosition] = useState(null);
const [title, setTitle] = useState(entry.title);
const [isEditingTitle, setIsEditingTitle] = useState(false);
const [backlinks, setBacklinks] = useState([]);
const [focusedBlockId, setFocusedBlockId] = useState(null);
const [tags, setTags] = useState(entry.tags || []);
const [isAddingTag, setIsAddingTag] = useState(false);
const [newTag, setNewTag] = useState('');
const [editingTagIndex, setEditingTagIndex] = useState(null);
const [editingTagValue, setEditingTagValue] = useState('');
const [draggedBlockId, setDraggedBlockId] = useState(null);
const [dropTargetId, setDropTargetId] = useState(null);
const [dropPosition, setDropPosition] = useState('after');
const [isInternalUpdate, setIsInternalUpdate] = useState(false);
const [viewMode, setViewMode] = useState('blocks');
const [selectedLineBlockId, setSelectedLineBlockId] = useState(null);
const [linesScrollProgress, setLinesScrollProgress] = useState({ top: 0, bottom: 1 });
const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
const [isDeleting, setIsDeleting] = useState(false);
const [hoveredBlockId, setHoveredBlockId] = useState(null);
const [showShareDialog, setShowShareDialog] = useState(false);
const [saveStatus, setSaveStatus] = useState(null);
```

### Step 4: Update Tag Management
The hook provides helper functions for tag management. Replace any direct tag manipulation with:
```javascript
// Adding a tag
addTag(newTagValue); // Instead of manual array manipulation

// Removing a tag
removeTag(index); // Instead of filter

// Updating a tag
updateTag(index, newValue); // Instead of manual array update
```

### Step 5: Update Title and Tag Handlers
The hook automatically syncs with the document store, so update handlers:
```javascript
// Old way
const handleTitleChange = (newTitle) => {
  setTitle(newTitle);
  // Manual save logic
};

// New way - hook handles document store sync
const handleTitleChange = (newTitle) => {
  setTitle(newTitle); // Hook handles the rest
};
```

## Testing

Run in browser console:
```javascript
// Before migration
window.testExpandedViewMigration.countOriginalUseState();

// After migration
window.testExpandedViewMigration.runAllTests();
```

## Benefits

1. **Zero useState calls** - All state managed in Zustand
2. **Redux DevTools** - Time-travel debugging
3. **Centralized state** - Easier to debug and maintain
4. **Automatic sync** - Document store updates handled by hook
5. **Performance** - Selective subscriptions prevent unnecessary re-renders

## Troubleshooting

If something doesn't work after migration:
1. Check the browser console for errors
2. Verify all state names match exactly
3. Check that setters are called with correct values
4. Use Redux DevTools to inspect state changes
5. Run `window.__APP_STATE__.editor` to see current editor state