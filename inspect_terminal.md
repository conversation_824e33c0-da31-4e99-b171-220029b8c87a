index-C8FhFS1i.js:26 Using optimized Supabase client
index-C8FhFS1i.js:26 Global auto-save manager initialized with defensive wrappers
index-C8FhFS1i.js:26 [Honeycomb] SDK initialized successfully in production mode
index-C8FhFS1i.js:26 IndexedDB initialized successfully
index-C8FhFS1i.js:26 Starting auto-save with interval: 30 seconds
index-C8FhFS1i.js:26 [AuthStore] Already initialized, skipping
index-C8FhFS1i.js:26 [RealtimeManager] Cleaning up all subscriptions
index-C8FhFS1i.js:26 [AuthStore] Already initialized, skipping
index-C8FhFS1i.js:26 Auto-save interval of 2s is too low. Using minimum of 3s for stability.
(anonymous) @ index-C8FhFS1i.js:26
(anonymous) @ index-C8FhFS1i.js:64
$u @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Uk @ index-C8FhFS1i.js:63
im @ index-C8FhFS1i.js:63
Bk @ index-C8FhFS1i.js:63
Lk @ index-C8FhFS1i.js:63
Ck @ index-C8FhFS1i.js:63
Nk @ index-C8FhFS1i.js:63
qk @ index-C8FhFS1i.js:63
Xu @ index-C8FhFS1i.js:63
Uk @ index-C8FhFS1i.js:63
(anonymous) @ index-C8FhFS1i.js:63
G @ index-C8FhFS1i.js:48
index-C8FhFS1i.js:26 [useRealtimeManager] Initializing for user: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-C8FhFS1i.js:26 [RealtimeDebug] Subscription created for documents in RealtimeManager
index-C8FhFS1i.js:26 [RealtimeDebug] Active subscriptions: 1
index-C8FhFS1i.js:26 [Honeycomb] User context set: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-C8FhFS1i.js:26 [RealtimeDebug] Subscription created for blocks in RealtimeManager
index-C8FhFS1i.js:26 [RealtimeDebug] Active subscriptions: 2
index-C8FhFS1i.js:26 SW registered: ServiceWorkerRegistration {installing: null, waiting: null, active: ServiceWorker, navigationPreload: NavigationPreloadManager, scope: 'https://www.devlog.design/', …}
index-C8FhFS1i.js:26 RealtimeManager: Connected to documents channel
index-C8FhFS1i.js:26 RealtimeManager: Disconnected from documents channel
index-C8FhFS1i.js:26 Auth initialization failed: Error: Session timeout
    at index-C8FhFS1i.js:64:44320
    at r (index-C8FhFS1i.js:26:9336)
(anonymous) @ index-C8FhFS1i.js:26
initialize @ index-C8FhFS1i.js:64
await in initialize
(anonymous) @ index-C8FhFS1i.js:64
$u @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Uk @ index-C8FhFS1i.js:63
(anonymous) @ index-C8FhFS1i.js:63
G @ index-C8FhFS1i.js:48
index-C8FhFS1i.js:26 Dashboard useState count: 2
index-C8FhFS1i.js:26 [AuthStore] Already initialized, skipping
index-C8FhFS1i.js:26 Loading folders for user: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-C8FhFS1i.js:26 VirtualizedGrid - scroll container: {"totalHeight":120,"containerHeight":789,"hasOverflow":false,"parentIsCardsContainer":true}
index-C8FhFS1i.js:26 [AuthStore] Already initialized, skipping
index-C8FhFS1i.js:26 Auto-save interval of 2s is too low. Using minimum of 3s for stability.
(anonymous) @ index-C8FhFS1i.js:26
(anonymous) @ index-C8FhFS1i.js:64
$u @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Uk @ index-C8FhFS1i.js:63
im @ index-C8FhFS1i.js:63
Bk @ index-C8FhFS1i.js:63
Lk @ index-C8FhFS1i.js:63
Ck @ index-C8FhFS1i.js:63
Nk @ index-C8FhFS1i.js:63
qk @ index-C8FhFS1i.js:63
Xu @ index-C8FhFS1i.js:63
Hk @ index-C8FhFS1i.js:63
(anonymous) @ index-C8FhFS1i.js:63
setTimeout
(anonymous) @ index-C8FhFS1i.js:29
(anonymous) @ index-C8FhFS1i.js:64
initialize @ index-C8FhFS1i.js:64
(anonymous) @ index-C8FhFS1i.js:64
$u @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Zs @ index-C8FhFS1i.js:63
bk @ index-C8FhFS1i.js:63
Uk @ index-C8FhFS1i.js:63
(anonymous) @ index-C8FhFS1i.js:63
G @ index-C8FhFS1i.js:48
index-C8FhFS1i.js:26 Dashboard: Starting to load entries...
index-C8FhFS1i.js:26 Dashboard useState count: 2
index-C8FhFS1i.js:26 Dashboard: Skipping load - already loading or initialized
index-C8FhFS1i.js:26 Dashboard useState count: 2
index-C8FhFS1i.js:26 Dashboard: Skipping load - already loading or initialized
index-C8FhFS1i.js:26 RealtimeManager: Connected to documents channel
index-C8FhFS1i.js:26 RealtimeManager: Disconnected from documents channel
index-C8FhFS1i.js:26 RealtimeManager: Connected to documents channel
index-C8FhFS1i.js:26 RealtimeManager: Disconnected from documents channel
