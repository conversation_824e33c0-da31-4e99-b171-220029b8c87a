name: Generate Supabase Types

on:
  push:
    branches: [main]
    paths:
      - 'supabase/migrations/**'
      - '.github/workflows/generate-types.yml'
      - 'scripts/generate-type-helpers.js'
  workflow_dispatch: # Allow manual trigger

jobs:
  generate-types:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # Grant write access to push commits
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install Supabase CLI
        run: |
          # Create a temporary directory for extraction
          mkdir -p /tmp/supabase-cli
          cd /tmp/supabase-cli
          
          # Download and extract to temp directory
          wget -qO- https://github.com/supabase/cli/releases/download/v1.137.2/supabase_linux_amd64.tar.gz | tar xvz
          
          # Move the binary to /usr/local/bin
          sudo mv supabase /usr/local/bin/
          
          # Clean up temp directory
          cd /
          rm -rf /tmp/supabase-cli
          
          # Verify installation
          supabase --version
          
      - name: Generate TypeScript types
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
        timeout-minutes: 5
        run: |
          # Ensure the types directory exists
          mkdir -p src/types
          
          # Generate types from Supabase project
          supabase gen types typescript \
            --project-id "$SUPABASE_PROJECT_ID" \
            --schema public \
            > src/types/database.types.ts
            
          # Also create a .d.ts file for better IDE support
          cp src/types/database.types.ts src/types/database.types.d.ts
            
      - name: Generate type helpers
        timeout-minutes: 2
        run: |
          # Create scripts directory if it doesn't exist
          mkdir -p scripts
          
          # Run type helper generation
          node scripts/generate-type-helpers.js
          
      - name: Commit and push changes
        timeout-minutes: 2
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          # Add generated files
          git add src/types/database.types.ts
          git add src/types/database.types.d.ts
          git add src/types/supabase-helpers.js
          
          # Commit only if there are changes
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            git commit -m "chore: update Supabase types [skip ci]"
            git push
          fi