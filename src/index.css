/* Import custom styles - must be before @tailwind */
@import './styles/typography.css';
@import './styles/fluid-grids.css';
@import './styles/glassmorphism.css';
@import './styles/problem-cards.css';
@import './styles/core-features.css';
@import './styles/hero-premium.css';
@import './styles/demo.css';
@import './styles/pricing-fallback.css';
@import './styles/block-controls.css';
@import './styles/inline-action-bar.css';
@import './styles/video-showcase.css';
@import './styles/hero-background-animation.css';
@import './styles/hero-quantum-field.css';
@import './styles/hero-knowledge-constellation.css';
@import './styles/MemoryErosion.css';
@import './styles/MemoryErosionEnhanced.css';
@import './styles/mobile-optimizations.css';
@import './styles/unified-gradients.css';
@import './styles/section-transitions.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global viewport fixes */
@layer base {
  /* Ensure HTML and body take full height */
  html {
    height: 100%;
    overflow-x: hidden;
  }
  
  body {
    min-height: 100%;
    min-height: 100vh;
    min-height: 100dvh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
  
  /* Root app container */
  #root {
    min-height: 100vh;
    min-height: 100dvh;
    display: flex;
    flex-direction: column;
  }
  
  /* Prevent horizontal scroll on all viewports */
  * {
    max-width: 100vw;
  }
}

/* Fluid Typography System */
@layer base {
  :root {
  /* Safe area insets for mobile devices */
  --sat: env(safe-area-inset-top, 0);
  --sab: env(safe-area-inset-bottom, 0);
  --sal: env(safe-area-inset-left, 0);
  --sar: env(safe-area-inset-right, 0);
  
  /* Scrollbar width calculation */
  --scrollbar-width: calc(100vw - 100%);
  
    /* Fluid font sizes using clamp() */
    --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --font-size-sm: clamp(0.875rem, 0.825rem + 0.25vw, 1rem);
    --font-size-base: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
    --font-size-lg: clamp(1.125rem, 1.05rem + 0.375vw, 1.25rem);
    --font-size-xl: clamp(1.25rem, 1.15rem + 0.5vw, 1.5rem);
    --font-size-2xl: clamp(1.5rem, 1.35rem + 0.75vw, 1.875rem);
    --font-size-3xl: clamp(1.875rem, 1.65rem + 1.125vw, 2.25rem);
    --font-size-4xl: clamp(2.25rem, 1.95rem + 1.5vw, 3rem);
    
    /* Fluid spacing */
    --spacing-xs: clamp(0.25rem, 0.2rem + 0.25vw, 0.5rem);
    --spacing-sm: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem);
    --spacing-md: clamp(1rem, 0.8rem + 1vw, 1.5rem);
    --spacing-lg: clamp(1.5rem, 1.2rem + 1.5vw, 2rem);
    --spacing-xl: clamp(2rem, 1.5rem + 2.5vw, 3rem);
    --spacing-2xl: clamp(3rem, 2rem + 5vw, 5rem);
    
    /* Fluid line heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Responsive container widths */
    --container-xs: 100%;
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    
    /* Touch target sizes */
    --touch-target: 44px;
    --touch-target-small: 36px;
  }
  
  /* Apply fluid typography to base elements */
  body {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    /* Prevent iOS font size adjustment */
    -webkit-text-size-adjust: 100%;
  }
  
  h1 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
  }
  
  h2 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-tight);
  }
  
  h3 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-normal);
  }
  
  h4 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
  }
  
  /* Ensure minimum font size for readability */
  p, li, td, th {
    font-size: max(var(--font-size-base), 16px);
  }
  
  /* Responsive text utilities */
  .text-responsive-xs { font-size: var(--font-size-xs); }
  .text-responsive-sm { font-size: var(--font-size-sm); }
  .text-responsive-base { font-size: var(--font-size-base); }
  .text-responsive-lg { font-size: var(--font-size-lg); }
  .text-responsive-xl { font-size: var(--font-size-xl); }
  .text-responsive-2xl { font-size: var(--font-size-2xl); }
  .text-responsive-3xl { font-size: var(--font-size-3xl); }
  .text-responsive-4xl { font-size: var(--font-size-4xl); }
  
  /* Responsive spacing utilities */
  .space-responsive-xs { padding: var(--spacing-xs); }
  .space-responsive-sm { padding: var(--spacing-sm); }
  .space-responsive-md { padding: var(--spacing-md); }
  .space-responsive-lg { padding: var(--spacing-lg); }
  .space-responsive-xl { padding: var(--spacing-xl); }
  .space-responsive-2xl { padding: var(--spacing-2xl); }
}

/* Responsive utilities */
@layer utilities {
  /* Touch-friendly tap targets */
  .touch-target {
    min-height: var(--touch-target);
    min-width: var(--touch-target);
  }
  
  .touch-target-small {
    min-height: var(--touch-target-small);
    min-width: var(--touch-target-small);
  }
  
  /* Safe area padding for mobile devices */
  .safe-padding {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  /* Prevent text selection on interactive elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }
  
  /* Smooth momentum scrolling on iOS */
  .momentum-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }
}

/* Ensure full height for the app */
html, body, #root {
  height: 100%;
  /* Use dynamic viewport height for mobile */
  height: 100dvh;
}

/* Subtle animations for floating controls */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Only prevent scrolling on dashboard - other pages can scroll */
body:has(.dashboard-container) {
  overflow: hidden;
}

/* Smooth scrolling globally */
@layer base {
  html {
    scroll-behavior: smooth;
  }
}

/* Mobile menu slide-in animation */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.mobile-menu-enter {
  animation: slideInRight 0.3s ease-out;
}

/* Navigation scrollbar fix */
.nav-with-scrollbar {
  left: 0;
  right: 0;
  padding-right: 17px; /* Account for scrollbar */
}

/* On systems with overlay scrollbars (Mac), reduce padding */
@supports (scrollbar-width: none) or (-webkit-scrollbar-width: none) {
  .nav-with-scrollbar {
    padding-right: 0;
  }
}

/* Remove padding on touch devices (mobile/tablet) */
@media (pointer: coarse) {
  .nav-with-scrollbar {
    padding-right: 0;
  }
}

/* Custom animations */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Smooth transitions for expanded view */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Tailwind-style animations */
.animate-in {
  animation-duration: 200ms;
  animation-fill-mode: both;
}

.fade-in {
  animation-name: fadeIn;
}

.slide-in-from-top-1 {
  --tw-translate-y: -0.25rem;
}

.slide-in-from-right-1 {
  --tw-translate-x: 0.25rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(var(--tw-translate-y, 0)) translateX(var(--tw-translate-x, 0));
  }
  to {
    opacity: 1;
    transform: translateY(0) translateX(0);
  }
}

/* Code block improvements */
.code-block-fullscreen {
  z-index: 9999;
}

/* Prevent hover reflows in AI blocks */
.message-bubble button {
  opacity: 0.5;
  transition: opacity 150ms ease-in-out;
}

.message-bubble:hover button,
.message-bubble button:focus {
  opacity: 1;
}

/* Disable group hover opacity changes during edit */
.ai-messages-container:has(textarea) .group:hover button {
  opacity: inherit;
}

/* Block hover effects without JavaScript */
.group {
  /* Default z-index */
  z-index: 1;
  /* Create stacking context */
  position: relative;
  /* Smooth transitions */
  transition: z-index 0ms;
}

/* Higher z-index on hover */
.group:hover {
  z-index: 10;
}

/* Prevent any layout shifts during AI block editing */
.group:has(.ai-edit-textarea) {
  z-index: 20 !important;
}

/* Disable all hover effects when AI block is being edited */
.group:has(.ai-edit-textarea):hover {
  z-index: 20 !important;
}

/* Optimized skeleton loading styles */
.skeleton-block {
  position: relative;
  height: var(--skeleton-height);
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  overflow: hidden;
  background: rgba(31, 41, 55, 0.5); /* dark-primary/50 */
  border: 1px solid rgba(55, 65, 81, 0.5); /* gray-800/50 */
}

.skeleton-content {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(156, 163, 175, 0.1) 50%,
    transparent 100%
  );
  animation: skeleton-shimmer 1.5s ease-in-out infinite;
}

@keyframes skeleton-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Type-specific skeleton patterns */
.skeleton-block[data-skeleton-type="heading"]::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 1rem;
  right: 25%;
  height: 2rem;
  background: rgba(107, 114, 128, 0.3);
  border-radius: 0.25rem;
  transform: translateY(-50%);
}

.skeleton-block[data-skeleton-type="text"]::before,
.skeleton-block[data-skeleton-type="text"]::after {
  content: '';
  position: absolute;
  left: 1rem;
  right: 1rem;
  height: 0.875rem;
  background: rgba(107, 114, 128, 0.3);
  border-radius: 0.25rem;
}

.skeleton-block[data-skeleton-type="text"]::before {
  top: 1rem;
  right: 3rem;
}

.skeleton-block[data-skeleton-type="text"]::after {
  top: 2.5rem;
  right: 5rem;
}

.skeleton-block[data-skeleton-type="code"] {
  background: rgba(31, 41, 55, 0.8);
}

.skeleton-block[data-skeleton-type="code"]::before {
  content: '';
  position: absolute;
  top: 1rem;
  left: 1rem;
  width: 4rem;
  height: 1rem;
  background: rgba(107, 114, 128, 0.3);
  border-radius: 0.25rem;
}

/* Fade in animation when real content loads */
.block-fade-in {
  animation: block-fade-in 0.3s ease-out;
}

@keyframes block-fade-in {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating toolbar animation */
.floating-toolbar {
  animation: toolbar-fade-in 0.2s ease-out;
}

@keyframes toolbar-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Focus mode transitions */
.focus-mode-dimmed {
  transition: opacity 0.3s ease-out;
}

/* Custom scrollbar for code blocks */
.code-block pre::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.code-block pre::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.code-block pre::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.code-block pre::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Minimal scrollbar for floating tags */
.minimal-scrollbar::-webkit-scrollbar {
  width: 3px;
}

.minimal-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.minimal-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1.5px;
}

.minimal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Template block styles */
.template-block {
  /* Ensure proper rendering */
  contain: layout;
}

/* Project Card Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

/* Animation delays for staggered effects */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

/* Sparkle animation */
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom select dropdown styling */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23ffffff40' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 12px;
  padding-right: 2.5rem !important;
}

select:focus {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%2310b981' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
}

/* Dark background for select options */
select option {
  background-color: #0a1628;
  color: #e2e8f0;
  padding: 0.5rem;
}

/* Custom scrollbar for file explorer and cards container */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}


/* Prevent text selection on drag */
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Optimize sidebar and focus indicator performance */
.sidebar-container {
  will-change: width;
  contain: layout style;
}

.focus-indicator {
  will-change: background-color, border-color, transform;
  transform: translateZ(0); /* Force GPU acceleration */
  backface-visibility: hidden; /* Prevent flickering */
}

/* Prevent layout shifts during transitions */
.project-explorer-item {
  transform: translateZ(0);
  backface-visibility: hidden;
}

