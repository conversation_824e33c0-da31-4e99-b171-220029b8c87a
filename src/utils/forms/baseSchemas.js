/**
 * @fileoverview Base Zod schemas for form validation
 * Provides reusable validation schemas for common form patterns
 */

import { z } from 'zod';

// Common field schemas
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Invalid email address');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number');

export const uuidSchema = z
  .string()
  .uuid('Invalid ID format');

export const urlSchema = z
  .string()
  .url('Invalid URL format')
  .or(z.literal(''));

export const tagSchema = z
  .string()
  .min(1, 'Tag cannot be empty')
  .max(50, 'Tag is too long')
  .regex(/^[a-zA-Z0-9-_]+$/, 'Tag can only contain letters, numbers, hyphens, and underscores');

export const titleSchema = z
  .string()
  .min(1, 'Title is required')
  .max(255, 'Title is too long');

// Document schemas
export const documentSchema = z.object({
  id: uuidSchema.optional(),
  title: titleSchema,
  tags: z.array(tagSchema).default([]),
  user_id: uuidSchema.optional(),
  is_template: z.boolean().default(false),
  is_published: z.boolean().default(false),
  folder_id: uuidSchema.nullable().optional(),
  organization_id: uuidSchema.nullable().optional(),
  position: z.number().int().min(0).default(0),
  preview: z.string().optional(),
  metadata: z.object({
    preview: z.string().optional(),
    blockCount: z.number().optional(),
    syncStatus: z.enum(['synced', 'pending', 'error']).optional(),
    lastSyncedAt: z.string().datetime().optional(),
    isNewDocument: z.boolean().optional(),
    createdLocally: z.boolean().optional()
  }).optional(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
  deleted_at: z.string().datetime().nullable().optional()
});

// Block schemas
export const blockTypeSchema = z.enum([
  'text',
  'code',
  'heading',
  'ai',
  'table',
  'filetree',
  'todo',
  'image',
  'inline-image'
]);

export const blockPositionSchema = z
  .number()
  .int()
  .min(0, 'Position must be non-negative');

export const blockMetadataSchema = z.object({
  // Code block metadata
  language: z.string().optional(),
  filename: z.string().optional(),
  
  // Heading metadata
  level: z.number().int().min(1).max(6).optional(),
  
  // Todo metadata
  checked: z.boolean().optional(),
  
  // Image metadata
  images: z.array(z.object({
    url: urlSchema,
    alt: z.string().optional(),
    caption: z.string().optional(),
    width: z.number().optional(),
    height: z.number().optional()
  })).optional(),
  
  // Table metadata
  headers: z.array(z.string()).optional(),
  rows: z.array(z.array(z.string())).optional()
}).passthrough(); // Allow additional properties

export const blockSchema = z.object({
  id: uuidSchema.optional(),
  document_id: uuidSchema,
  type: blockTypeSchema,
  content: z.string(),
  position: blockPositionSchema,
  metadata: blockMetadataSchema.optional(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional()
});

// Auth schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required')
});

export const signupSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const forgotPasswordSchema = z.object({
  email: emailSchema
});

export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Profile schemas
export const profileSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username is too long')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
    .optional()
    .nullable(),
  full_name: z
    .string()
    .max(255, 'Name is too long')
    .optional()
    .nullable(),
  avatar_url: urlSchema.optional().nullable()
});

// Sharing schemas
export const sharePermissionSchema = z.enum(['view', 'edit']);

export const documentShareSchema = z.object({
  permissions: z.array(sharePermissionSchema).min(1, 'At least one permission is required'),
  expires_at: z.string().datetime().optional().nullable()
});

// Search/Filter schemas
export const searchSchema = z.object({
  query: z.string().optional(),
  tags: z.array(tagSchema).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  sortBy: z.enum(['created_at', 'updated_at', 'title']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// Batch operation schemas
export const batchDocumentOperationSchema = z.object({
  documentIds: z.array(uuidSchema).min(1, 'At least one document must be selected'),
  operation: z.enum(['delete', 'archive', 'restore', 'tag'])
});

export const batchBlockOperationSchema = z.object({
  blockIds: z.array(uuidSchema).min(1, 'At least one block must be selected'),
  operation: z.enum(['delete', 'move', 'duplicate'])
});

// Import/Export schemas
export const exportOptionsSchema = z.object({
  format: z.enum(['json', 'markdown', 'html']),
  includeMetadata: z.boolean().default(true),
  includeImages: z.boolean().default(true)
});

export const importOptionsSchema = z.object({
  format: z.enum(['json', 'markdown']),
  mergeStrategy: z.enum(['replace', 'append', 'skip']).default('skip')
});

// Supabase-specific helpers
export const supabaseInsertSchema = (schema) => 
  schema.omit({ id: true, created_at: true, updated_at: true, deleted_at: true });

export const supabaseUpdateSchema = (schema) => 
  schema.partial().omit({ id: true, user_id: true, created_at: true, updated_at: true, deleted_at: true });

// Helper to create both insert and update schemas
export const createSupabaseSchemas = (baseSchema) => ({
  insert: supabaseInsertSchema(baseSchema),
  update: supabaseUpdateSchema(baseSchema),
  base: baseSchema
});

// Pre-built schemas for common entities
export const documentSchemas = createSupabaseSchemas(documentSchema);
export const blockSchemas = createSupabaseSchemas(blockSchema);
export const profileSchemas = createSupabaseSchemas(profileSchema);