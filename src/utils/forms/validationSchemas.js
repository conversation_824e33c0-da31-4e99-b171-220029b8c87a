import { z } from 'zod';

/**
 * Reusable validation schemas for forms
 * These schemas match the database constraints from database.types.ts
 */

// ======================
// Auth Schemas
// ======================

export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number');

export const signUpSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const signInSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// ======================
// Document Schemas
// ======================

export const documentTitleSchema = z
  .string()
  .min(1, 'Title is required')
  .max(255, 'Title must be less than 255 characters')
  .regex(/^[^\s].*[^\s]$/, 'Title cannot start or end with whitespace');

export const documentTagSchema = z
  .string()
  .min(1, 'Tag cannot be empty')
  .max(50, 'Tag must be less than 50 characters')
  .regex(/^[a-zA-Z0-9-_]+$/, 'Tags can only contain letters, numbers, hyphens, and underscores');

export const documentSchema = z.object({
  title: documentTitleSchema,
  tags: z
    .array(documentTagSchema)
    .max(20, 'Maximum 20 tags allowed')
    .default([]),
  folder_id: z.string().uuid().nullable().optional(),
  is_template: z.boolean().default(false),
});

// ======================
// Block Schemas
// ======================

export const blockTypes = [
  'text', 'code', 'heading', 'ai', 'table', 
  'filetree', 'todo', 'image', 'inline-image', 
  'version-track', 'issue-tracker'
] as const;

export const blockContentSchema = z
  .string()
  .max(1000000, 'Content must be less than 1MB');

export const blockSchema = z.object({
  type: z.enum(blockTypes),
  content: blockContentSchema,
  metadata: z.record(z.any()).default({}),
  position: z.number().int().min(0),
});

// ======================
// Profile Schemas
// ======================

export const usernameSchema = z
  .string()
  .min(3, 'Username must be at least 3 characters')
  .max(30, 'Username must be less than 30 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens');

export const profileSchema = z.object({
  username: usernameSchema.optional(),
  full_name: z.string().max(255, 'Name must be less than 255 characters').optional(),
  avatar_url: z.string().url('Please enter a valid URL').optional(),
});

// ======================
// Folder Schemas
// ======================

export const folderNameSchema = z
  .string()
  .min(1, 'Folder name is required')
  .max(100, 'Folder name must be less than 100 characters');

export const folderSchema = z.object({
  name: folderNameSchema,
  parent_id: z.string().uuid().nullable().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color').optional(),
  icon: z.string().max(50).optional(),
});

// ======================
// API Key Schemas
// ======================

export const apiKeyNameSchema = z
  .string()
  .min(1, 'API key name is required')
  .max(100, 'API key name must be less than 100 characters');

export const apiKeySchema = z.object({
  name: apiKeyNameSchema,
});

// ======================
// Search Schemas
// ======================

export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(200, 'Search query too long'),
  filters: z.object({
    tags: z.array(z.string()).optional(),
    folder_id: z.string().uuid().optional(),
    date_from: z.string().datetime().optional(),
    date_to: z.string().datetime().optional(),
  }).optional(),
});

// ======================
// Helper Functions
// ======================

/**
 * Get error messages from Zod validation
 * @param {import('zod').ZodError} error - Zod error object
 * @returns {Object} Object with field names as keys and error messages as values
 */
export function getZodErrors(error) {
  const errors = {};
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    errors[path] = err.message;
  });
  return errors;
}

/**
 * Validate data against a schema
 * @param {import('zod').ZodSchema} schema - Zod schema
 * @param {any} data - Data to validate
 * @returns {{success: boolean, data?: any, errors?: Object}}
 */
export function validateSchema(schema, data) {
  try {
    const validData = schema.parse(data);
    return { success: true, data: validData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: getZodErrors(error) };
    }
    throw error;
  }
}