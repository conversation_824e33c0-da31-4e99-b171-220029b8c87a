/**
 * @fileoverview Supabase-specific helpers for form data handling
 * Provides utilities to prepare data for Supabase operations
 */

/**
 * Prepare data for Supabase insert operation
 * Removes system fields that are auto-generated
 * @param {Object} data - Form data
 * @returns {Object} Data ready for insert
 */
export function prepareForInsert(data) {
  const {
    id,
    created_at,
    updated_at,
    deleted_at,
    ...insertData
  } = data;
  
  return insertData;
}

/**
 * Prepare data for Supabase update operation
 * Removes immutable fields and system fields
 * @param {Object} data - Form data
 * @returns {Object} Data ready for update
 */
export function prepareForUpdate(data) {
  const {
    id,
    user_id,
    created_at,
    updated_at,
    deleted_at,
    ...updateData
  } = data;
  
  return updateData;
}

/**
 * Validate data before submitting to Supabase
 * Ensures required fields are present and valid
 * @param {Object} data - Data to validate
 * @param {string} operation - 'insert' or 'update'
 * @returns {{valid: boolean, errors: Object}} Validation result
 */
export function validateBeforeSubmit(data, operation) {
  const errors = {};
  
  if (operation === 'insert') {
    // Check required fields for insert
    if (!data.title?.trim()) {
      errors.title = 'Title is required';
    }
    
    if (data.user_id && !isValidUUID(data.user_id)) {
      errors.user_id = 'Invalid user ID format';
    }
  }
  
  if (operation === 'update') {
    // Check that at least one field is being updated
    const updateFields = Object.keys(prepareForUpdate(data));
    if (updateFields.length === 0) {
      errors._form = 'No fields to update';
    }
  }
  
  // Validate UUID fields
  const uuidFields = ['folder_id', 'organization_id', 'document_id'];
  uuidFields.forEach(field => {
    if (data[field] && data[field] !== null && !isValidUUID(data[field])) {
      errors[field] = `Invalid ${field} format`;
    }
  });
  
  // Validate array fields
  if (data.tags && !Array.isArray(data.tags)) {
    errors.tags = 'Tags must be an array';
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Transform Supabase response to form-friendly format
 * @param {Object} response - Supabase response data
 * @returns {Object} Form-friendly data
 */
export function transformFromSupabase(response) {
  if (!response) return null;
  
  // Handle array responses
  if (Array.isArray(response)) {
    return response.map(item => transformFromSupabase(item));
  }
  
  // Transform single item
  const transformed = { ...response };
  
  // Convert null values to empty strings for form fields
  Object.keys(transformed).forEach(key => {
    if (transformed[key] === null) {
      // Keep null for nullable foreign keys
      if (key.endsWith('_id')) {
        return;
      }
      // Convert to empty string for text fields
      if (typeof transformed[key] === 'string') {
        transformed[key] = '';
      }
    }
  });
  
  return transformed;
}

/**
 * Helper to check if a string is a valid UUID
 * @param {string} value - Value to check
 * @returns {boolean} Is valid UUID
 */
function isValidUUID(value) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return typeof value === 'string' && uuidRegex.test(value);
}

/**
 * Merge form data with existing record
 * Useful for partial updates
 * @param {Object} existing - Existing record from database
 * @param {Object} updates - Form updates
 * @returns {Object} Merged data
 */
export function mergeWithExisting(existing, updates) {
  const merged = { ...existing };
  
  Object.keys(updates).forEach(key => {
    // Only update if value is different and not undefined
    if (updates[key] !== undefined && updates[key] !== existing[key]) {
      merged[key] = updates[key];
    }
  });
  
  return merged;
}

/**
 * Create optimistic update data
 * Adds temporary fields for optimistic UI updates
 * @param {Object} data - Form data
 * @returns {Object} Optimistic data
 */
export function createOptimisticData(data) {
  return {
    ...data,
    _optimistic: true,
    _timestamp: Date.now(),
    updated_at: new Date().toISOString()
  };
}

/**
 * Clean optimistic fields from data
 * @param {Object} data - Data with optimistic fields
 * @returns {Object} Clean data
 */
export function cleanOptimisticData(data) {
  const { _optimistic, _timestamp, ...cleanData } = data;
  return cleanData;
}

/**
 * Handle Supabase error response
 * Converts Supabase errors to form-friendly error format
 * @param {Object} error - Supabase error
 * @returns {Object} Form errors object
 */
export function handleSupabaseError(error) {
  const formErrors = {};
  
  if (error.code === '23505') {
    // Unique constraint violation
    if (error.message.includes('email')) {
      formErrors.email = 'This email is already registered';
    } else if (error.message.includes('username')) {
      formErrors.username = 'This username is already taken';
    } else {
      formErrors._form = 'This value already exists';
    }
  } else if (error.code === '23503') {
    // Foreign key violation
    formErrors._form = 'Referenced item does not exist';
  } else if (error.code === '42501') {
    // Insufficient privileges
    formErrors._form = 'You do not have permission to perform this action';
  } else if (error.code === 'PGRST116') {
    // Not found
    formErrors._form = 'Item not found';
  } else {
    // Generic error
    formErrors._form = error.message || 'An error occurred';
  }
  
  return formErrors;
}