/**
 * Test file to ensure auth functionality works after migration
 * Run these tests manually before and after migration
 */

export const authTests = {
  // Test current auth state
  async testCurrentUser() {
    console.group('🧪 Testing Current User');
    try {
      // Check both old context and new store
      const storeUser = window.__APP_STATE__?.auth?.user;
      const hookUser = document.querySelector('[data-testid="user-email"]')?.textContent;
      
      console.log('Store user:', storeUser);
      console.log('UI shows user:', hookUser);
      console.log('Is authenticated:', !!storeUser);
      
      // Check if user data is consistent
      if (storeUser && hookUser && !hookUser.includes(storeUser.email)) {
        console.warn('⚠️ User data mismatch between store and UI');
      }
      
      console.groupEnd();
      return !!storeUser;
    } catch (error) {
      console.error('Test failed:', error);
      console.groupEnd();
      return false;
    }
  },

  // Test login flow
  async testLogin(email, password) {
    console.group('🧪 Testing Login');
    try {
      console.log('Login test for:', email);
      
      // Get the store directly
      const authStore = window.__APP_STATE__?.auth;
      if (!authStore) {
        throw new Error('Auth store not found');
      }
      
      // Attempt login
      const result = await authStore.signIn(email, password);
      
      if (result.error) {
        console.error('Login error:', result.error);
        console.groupEnd();
        return false;
      }
      
      // Wait a bit for state to update
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if user is now in store
      const userAfterLogin = window.__APP_STATE__?.auth?.user;
      console.log('User after login:', userAfterLogin);
      console.log('Login result:', userAfterLogin ? '✅ Success' : '❌ Failed');
      
      console.groupEnd();
      return !!userAfterLogin;
    } catch (error) {
      console.error('Login test failed:', error);
      console.groupEnd();
      return false;
    }
  },

  // Test logout flow  
  async testLogout() {
    console.group('🧪 Testing Logout');
    try {
      const authStore = window.__APP_STATE__?.auth;
      if (!authStore) {
        throw new Error('Auth store not found');
      }
      
      // Check user before logout
      const userBefore = authStore.user;
      console.log('User before logout:', userBefore);
      
      if (!userBefore) {
        console.warn('No user logged in to test logout');
        console.groupEnd();
        return true; // Already logged out
      }
      
      // Attempt logout
      const result = await authStore.signOut();
      
      if (result.error) {
        console.error('Logout error:', result.error);
        console.groupEnd();
        return false;
      }
      
      // Wait for state update
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if user is cleared
      const userAfterLogout = window.__APP_STATE__?.auth?.user;
      console.log('User after logout:', userAfterLogout);
      console.log('Logout successful:', !userAfterLogout ? '✅' : '❌');
      
      console.groupEnd();
      return !userAfterLogout;
    } catch (error) {
      console.error('Logout test failed:', error);
      console.groupEnd();
      return false;
    }
  },

  // Test protected route access
  async testProtectedRoute() {
    console.group('🧪 Testing Protected Route Access');
    try {
      const user = window.__APP_STATE__?.auth?.user;
      const currentPath = window.location.pathname;
      const isProtectedRoute = currentPath.includes('/dashboard') || 
                               currentPath.includes('/settings') ||
                               currentPath.includes('/documents');
      
      console.log('Current user:', user);
      console.log('Current path:', currentPath);
      console.log('Is protected route:', isProtectedRoute);
      
      if (isProtectedRoute && !user) {
        console.error('❌ On protected route without authentication!');
        console.groupEnd();
        return false;
      }
      
      const canAccess = !!user || !isProtectedRoute;
      console.log('Can access current route:', canAccess ? '✅' : '❌');
      
      console.groupEnd();
      return canAccess;
    } catch (error) {
      console.error('Protected route test failed:', error);
      console.groupEnd();
      return false;
    }
  },

  // Test session persistence
  async testSessionPersistence() {
    console.group('🧪 Testing Session Persistence');
    try {
      // Check localStorage for session
      const storedSession = localStorage.getItem('sb-xrzxhlzahuuqipugfklh-auth-token');
      console.log('Has stored session:', !!storedSession);
      
      if (storedSession) {
        try {
          const sessionData = JSON.parse(storedSession);
          console.log('Session expires at:', new Date(sessionData.expires_at * 1000));
          console.log('Session valid:', new Date() < new Date(sessionData.expires_at * 1000));
        } catch (e) {
          console.warn('Could not parse session data');
        }
      }
      
      // Check if current user matches stored session
      const currentUser = window.__APP_STATE__?.auth?.user;
      console.log('Current user in memory:', !!currentUser);
      console.log('Session persistence:', storedSession && currentUser ? '✅ Working' : '⚠️ Check needed');
      
      console.groupEnd();
      return !!storedSession === !!currentUser;
    } catch (error) {
      console.error('Session persistence test failed:', error);
      console.groupEnd();
      return false;
    }
  },

  // Test trial status
  async testTrialStatus() {
    console.group('🧪 Testing Trial Status');
    try {
      const trialStatus = window.__APP_STATE__?.auth?.trialStatus;
      const user = window.__APP_STATE__?.auth?.user;
      
      console.log('User:', user?.email);
      console.log('Trial status:', trialStatus);
      
      if (user && !trialStatus) {
        console.warn('⚠️ User logged in but no trial status loaded');
      }
      
      if (trialStatus) {
        console.log('Trial active:', trialStatus.isActive);
        console.log('Days remaining:', trialStatus.daysRemaining);
        console.log('Trial end:', trialStatus.trialEnd);
      }
      
      console.groupEnd();
      return true;
    } catch (error) {
      console.error('Trial status test failed:', error);
      console.groupEnd();
      return false;
    }
  },

  // Run all tests
  async runAll() {
    console.log('🏃 Running all auth tests...');
    console.log('==================================');
    
    const results = {
      currentUser: await this.testCurrentUser(),
      protectedRoute: await this.testProtectedRoute(),
      sessionPersistence: await this.testSessionPersistence(),
      trialStatus: await this.testTrialStatus(),
      // Don't auto-run login/logout to avoid side effects
    };
    
    console.log('==================================');
    console.log('📊 Test Results:');
    console.table(results);
    
    const allPassed = Object.values(results).every(result => result);
    console.log(allPassed ? '✅ All tests passed!' : '❌ Some tests failed');
    
    return results;
  },

  // Helper to record current state before migration
  recordCurrentState() {
    console.log('📸 Recording current auth state...');
    
    const state = {
      timestamp: new Date().toISOString(),
      user: window.__APP_STATE__?.auth?.user,
      session: window.__APP_STATE__?.auth?.session,
      trialStatus: window.__APP_STATE__?.auth?.trialStatus,
      loading: window.__APP_STATE__?.auth?.loading,
      localStorage: {
        hasAuthToken: !!localStorage.getItem('sb-xrzxhlzahuuqipugfklh-auth-token'),
        hasAuthRefresh: !!localStorage.getItem('sb-xrzxhlzahuuqipugfklh-auth-token-refresh')
      },
      currentPath: window.location.pathname
    };
    
    // Save to sessionStorage for comparison after migration
    sessionStorage.setItem('auth-migration-before', JSON.stringify(state));
    console.log('State recorded. Access with: JSON.parse(sessionStorage.getItem("auth-migration-before"))');
    
    return state;
  },

  // Helper to compare state after migration
  compareWithRecordedState() {
    console.log('🔍 Comparing auth state after migration...');
    
    const before = JSON.parse(sessionStorage.getItem('auth-migration-before') || '{}');
    const after = {
      timestamp: new Date().toISOString(),
      user: window.__APP_STATE__?.auth?.user,
      session: window.__APP_STATE__?.auth?.session,
      trialStatus: window.__APP_STATE__?.auth?.trialStatus,
      loading: window.__APP_STATE__?.auth?.loading,
      localStorage: {
        hasAuthToken: !!localStorage.getItem('sb-xrzxhlzahuuqipugfklh-auth-token'),
        hasAuthRefresh: !!localStorage.getItem('sb-xrzxhlzahuuqipugfklh-auth-token-refresh')
      },
      currentPath: window.location.pathname
    };
    
    console.log('Before migration:', before);
    console.log('After migration:', after);
    
    // Compare critical fields
    const comparison = {
      userIdMatch: before.user?.id === after.user?.id,
      userEmailMatch: before.user?.email === after.user?.email,
      sessionMatch: !!before.session === !!after.session,
      trialStatusMatch: !!before.trialStatus === !!after.trialStatus,
      localStorageMatch: before.localStorage?.hasAuthToken === after.localStorage?.hasAuthToken
    };
    
    console.table(comparison);
    
    const allMatched = Object.values(comparison).every(match => match);
    console.log(allMatched ? '✅ State successfully migrated!' : '❌ State mismatch detected');
    
    return { before, after, comparison };
  }
};

// Make available globally for testing
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.authTests = authTests;
  console.log('🧪 Auth migration tests loaded. Access with window.authTests');
  console.log('Commands:');
  console.log('- authTests.runAll() - Run all tests');
  console.log('- authTests.recordCurrentState() - Record state before migration');
  console.log('- authTests.compareWithRecordedState() - Compare after migration');
}