/**
 * Debugging utilities for Supabase real-time subscriptions
 * Helps track active subscriptions and detect memory leaks
 */

class RealtimeDebugger {
  constructor() {
    this.activeSubscriptions = new Map();
    this.subscriptionHistory = [];
    this.startTime = Date.now();
    
    // Make it globally available in development
    if (typeof window !== 'undefined' && import.meta.env.DEV) {
      window.__REALTIME_DEBUG__ = this;
    }
  }

  // Track when a subscription is created
  trackSubscription(table, channel, component = 'Unknown') {
    const id = `${table}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const subscription = {
      id,
      table,
      channel,
      component,
      startTime: Date.now(),
      status: 'active'
    };
    
    this.activeSubscriptions.set(id, subscription);
    this.subscriptionHistory.push({
      ...subscription,
      event: 'created'
    });
    
    console.log(`[RealtimeDebug] Subscription created for ${table} in ${component}`);
    console.log(`[RealtimeDebug] Active subscriptions: ${this.activeSubscriptions.size}`);
    
    return id;
  }

  // Track when a subscription is cleaned up
  trackCleanup(id) {
    const subscription = this.activeSubscriptions.get(id);
    if (subscription) {
      subscription.status = 'cleaned';
      subscription.endTime = Date.now();
      subscription.duration = subscription.endTime - subscription.startTime;
      
      this.activeSubscriptions.delete(id);
      this.subscriptionHistory.push({
        ...subscription,
        event: 'cleaned'
      });
      
      console.log(`[RealtimeDebug] Subscription cleaned for ${subscription.table}`);
      console.log(`[RealtimeDebug] Duration: ${subscription.duration}ms`);
      console.log(`[RealtimeDebug] Active subscriptions: ${this.activeSubscriptions.size}`);
    }
  }

  // Get all active subscriptions
  getActiveSubscriptions() {
    return Array.from(this.activeSubscriptions.values());
  }

  // Get subscription statistics
  getStats() {
    const now = Date.now();
    const activeList = this.getActiveSubscriptions();
    
    return {
      totalActive: activeList.length,
      totalCreated: this.subscriptionHistory.filter(h => h.event === 'created').length,
      totalCleaned: this.subscriptionHistory.filter(h => h.event === 'cleaned').length,
      oldestActive: activeList.length > 0 
        ? Math.max(...activeList.map(s => now - s.startTime)) 
        : 0,
      byTable: activeList.reduce((acc, sub) => {
        acc[sub.table] = (acc[sub.table] || 0) + 1;
        return acc;
      }, {}),
      byComponent: activeList.reduce((acc, sub) => {
        acc[sub.component] = (acc[sub.component] || 0) + 1;
        return acc;
      }, {}),
      possibleLeaks: activeList.filter(s => now - s.startTime > 300000) // Active > 5 min
    };
  }

  // Log current state
  logState() {
    const stats = this.getStats();
    console.group('[RealtimeDebug] Current State');
    console.table({
      'Active Subscriptions': stats.totalActive,
      'Total Created': stats.totalCreated,
      'Total Cleaned': stats.totalCleaned,
      'Potential Leaks': stats.possibleLeaks.length
    });
    
    if (stats.totalActive > 0) {
      console.group('Active Subscriptions by Table');
      console.table(stats.byTable);
      console.groupEnd();
      
      console.group('Active Subscriptions by Component');
      console.table(stats.byComponent);
      console.groupEnd();
      
      if (stats.possibleLeaks.length > 0) {
        console.warn('⚠️ Potential memory leaks detected:');
        console.table(stats.possibleLeaks.map(s => ({
          table: s.table,
          component: s.component,
          duration: `${Math.round((Date.now() - s.startTime) / 1000)}s`
        })));
      }
    }
    console.groupEnd();
  }

  // Reset all tracking
  reset() {
    this.activeSubscriptions.clear();
    this.subscriptionHistory = [];
    console.log('[RealtimeDebug] Debugger reset');
  }
}

// Create singleton instance
const realtimeDebugger = new RealtimeDebugger();

export default realtimeDebugger;