/**
 * Test utilities for TextBlock migration
 * Validates that useState has been replaced with blockEditorStore
 */

// Test 1: Check that Text<PERSON>lock uses blockEditorStore
export const testBlockEditorIntegration = () => {
  console.group('🧪 Testing TextBlock Migration');
  
  try {
    // Check if blockEditorStore is available
    const blockEditorStore = window.__APP_STATE__?.blockEditor;
    if (!blockEditorStore) {
      throw new Error('blockEditorStore not found in window.__APP_STATE__');
    }
    
    console.log('✅ blockEditorStore is available');
    
    // Check store structure
    const requiredMethods = [
      'setBlockEditing',
      'isBlockEditing',
      'setToolbarState',
      'updateToolbar',
      'setSelectionState',
      'setBlockCollapsed',
      'toggleBlockCollapse',
      'updateSlashHint',
      'clearBlockState'
    ];
    
    const missingMethods = requiredMethods.filter(method => 
      typeof blockEditorStore[method] !== 'function'
    );
    
    if (missingMethods.length > 0) {
      throw new Error(`Missing methods: ${missingMethods.join(', ')}`);
    }
    
    console.log('✅ All required methods are present');
    
    // Check store state
    const state = blockEditorStore.getState();
    console.log('📊 Current blockEditorStore state:', {
      editingBlocks: Object.keys(state.editingBlocks).length,
      toolbarStates: Object.keys(state.toolbarStates).length,
      selectionStates: Object.keys(state.selectionStates).length,
      collapseStates: Object.keys(state.collapseStates).length,
      slashCommandStates: Object.keys(state.slashCommandStates).length
    });
    
    console.log('✅ TextBlock migration successful!');
    console.groupEnd();
    return true;
  } catch (error) {
    console.error('❌ TextBlock migration test failed:', error);
    console.groupEnd();
    return false;
  }
};

// Test 2: Simulate block editing
export const testBlockEditing = (blockId = 'test-block-123') => {
  console.group('🧪 Testing Block Editing Functions');
  
  try {
    const store = window.__APP_STATE__.blockEditor;
    
    // Test editing state
    store.setBlockEditing(blockId, true);
    const isEditing = store.isBlockEditing(blockId);
    console.log('✅ Editing state:', isEditing);
    
    // Test toolbar state
    store.updateToolbar(blockId, true, { top: 100, left: 200 });
    const state = store.getState();
    console.log('✅ Toolbar state:', state.toolbarStates[blockId]);
    
    // Test selection state
    store.setSelectionState(blockId, { text: 'selected text', range: { start: 0, end: 13 } });
    console.log('✅ Selection state:', state.selectionStates[blockId]);
    
    // Test collapse state
    store.toggleBlockCollapse(blockId);
    console.log('✅ Collapse state:', state.collapseStates[blockId]);
    
    // Test slash command state
    store.updateSlashHint(blockId, 'h1', { top: 150, left: 100 });
    console.log('✅ Slash command state:', state.slashCommandStates[blockId]);
    
    // Cleanup
    store.clearBlockState(blockId);
    console.log('✅ State cleared');
    
    console.groupEnd();
    return true;
  } catch (error) {
    console.error('❌ Block editing test failed:', error);
    console.groupEnd();
    return false;
  }
};

// Test 3: Check for remaining useState in TextBlock
export const checkForUseState = async () => {
  console.group('🔍 Checking for remaining useState in TextBlock');
  
  try {
    // In a real test, we'd fetch and parse the file
    // For now, we'll just log instructions
    console.log('Run this command to check for useState:');
    console.log('grep -n "useState" src/components/blocks/TextBlock.jsx');
    console.log('');
    console.log('Expected: Only 1 useState for content (temporary)');
    console.log('This will be migrated to document store later');
    
    console.groupEnd();
    return true;
  } catch (error) {
    console.error('❌ useState check failed:', error);
    console.groupEnd();
    return false;
  }
};

// Run all tests
export const runAllTests = () => {
  console.log('🚀 Running TextBlock Migration Tests...\n');
  
  const results = {
    blockEditorIntegration: testBlockEditorIntegration(),
    blockEditing: testBlockEditing(),
    useStateCheck: checkForUseState()
  };
  
  const passed = Object.values(results).every(result => result === true);
  
  console.log('\n📊 Test Results:');
  console.table(results);
  
  if (passed) {
    console.log('\n🎉 All tests passed! TextBlock migration is successful.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the logs above.');
  }
  
  return results;
};

// Make available globally
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.testTextBlockMigration = {
    testBlockEditorIntegration,
    testBlockEditing,
    checkForUseState,
    runAllTests
  };
  
  console.log('📦 TextBlock migration tests loaded. Available commands:');
  console.log('- window.testTextBlockMigration.runAllTests()');
  console.log('- window.testTextBlockMigration.testBlockEditorIntegration()');
  console.log('- window.testTextBlockMigration.testBlockEditing()');
  console.log('- window.testTextBlockMigration.checkForUseState()');
}