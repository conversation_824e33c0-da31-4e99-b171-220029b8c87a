/**
 * Performance Optimizer for AI #2
 * Monitors and optimizes app performance during migrations
 */

class PerformanceOptimizer {
  constructor() {
    this.metrics = {
      renderCounts: new Map(),
      slowComponents: [],
      memorySnapshots: []
    };
    this.observer = null;
  }

  /**
   * Track component render counts
   */
  trackRender(componentName) {
    const current = this.metrics.renderCounts.get(componentName) || 0;
    this.metrics.renderCounts.set(componentName, current + 1);
    
    // Warn if excessive renders
    if (current > 50) {
      console.warn(`⚠️ ${componentName} rendered ${current} times - consider optimization`);
      
      if (!this.metrics.slowComponents.includes(componentName)) {
        this.metrics.slowComponents.push(componentName);
      }
    }
  }

  /**
   * Monitor performance metrics
   */
  startMonitoring() {
    console.log('🚀 Starting Performance Monitoring...');
    
    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn(`⚠️ Long task detected: ${entry.duration.toFixed(2)}ms`);
          }
        }
      });
      
      try {
        this.observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        console.log('Long task monitoring not supported');
      }
    }
    
    // Monitor memory usage
    if (performance.memory) {
      setInterval(() => {
        const used = performance.memory.usedJSHeapSize;
        const limit = performance.memory.jsHeapSizeLimit;
        const percentage = (used / limit) * 100;
        
        this.metrics.memorySnapshots.push({
          timestamp: Date.now(),
          used: Math.round(used / 1024 / 1024),
          percentage: percentage.toFixed(1)
        });
        
        // Keep only last 10 snapshots
        if (this.metrics.memorySnapshots.length > 10) {
          this.metrics.memorySnapshots.shift();
        }
        
        if (percentage > 80) {
          console.warn(`⚠️ High memory usage: ${percentage.toFixed(1)}%`);
        }
      }, 30000); // Every 30 seconds
    }
  }

  /**
   * Get optimization suggestions
   */
  getSuggestions() {
    const suggestions = [];
    
    // Check for components with high render counts
    this.metrics.renderCounts.forEach((count, component) => {
      if (count > 30) {
        suggestions.push({
          component,
          issue: 'Excessive renders',
          suggestion: 'Add React.memo or optimize props/state',
          severity: count > 50 ? 'high' : 'medium'
        });
      }
    });
    
    // Check memory usage trend
    if (this.metrics.memorySnapshots.length > 2) {
      const recent = this.metrics.memorySnapshots.slice(-3);
      const increasing = recent.every((snapshot, i) => 
        i === 0 || snapshot.used > recent[i - 1].used
      );
      
      if (increasing) {
        suggestions.push({
          component: 'App',
          issue: 'Memory leak detected',
          suggestion: 'Check for uncleared subscriptions or event listeners',
          severity: 'high'
        });
      }
    }
    
    return suggestions;
  }

  /**
   * Generate performance report
   */
  getReport() {
    const topRenders = Array.from(this.metrics.renderCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
    
    const memoryTrend = this.metrics.memorySnapshots.length > 0
      ? this.metrics.memorySnapshots[this.metrics.memorySnapshots.length - 1]
      : null;
    
    return {
      timestamp: new Date().toISOString(),
      topRenderingComponents: topRenders,
      slowComponents: this.metrics.slowComponents,
      memoryUsage: memoryTrend,
      suggestions: this.getSuggestions()
    };
  }

  /**
   * Apply automatic optimizations
   */
  applyOptimizations() {
    console.log('🔧 Applying Performance Optimizations...');
    
    // Debounce expensive operations
    if (window.requestIdleCallback) {
      console.log('✅ Using requestIdleCallback for non-critical tasks');
    }
    
    // Enable concurrent features
    if (window.React && window.React.startTransition) {
      console.log('✅ React concurrent features available');
    }
    
    // Suggest lazy loading
    if (this.metrics.slowComponents.length > 0) {
      console.log('💡 Consider lazy loading:', this.metrics.slowComponents);
    }
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    console.log('🛑 Performance monitoring stopped');
  }
}

// Create singleton instance
const performanceOptimizer = new PerformanceOptimizer();

// Auto-start in development
if (import.meta.env.DEV) {
  performanceOptimizer.startMonitoring();
  
  // Make available globally
  window.performanceOptimizer = performanceOptimizer;
  
  console.log('Performance optimizer ready. Commands:');
  console.log('- window.performanceOptimizer.getReport()');
  console.log('- window.performanceOptimizer.getSuggestions()');
  console.log('- window.performanceOptimizer.applyOptimizations()');
}

export default performanceOptimizer;