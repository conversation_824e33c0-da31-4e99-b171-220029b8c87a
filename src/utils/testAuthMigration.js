/**
 * <PERSON><PERSON><PERSON> to test auth migration completeness
 * Run this in the browser console to verify everything works
 */

export const testAuthMigration = {
  // Check if stores are available
  checkStoresAvailable() {
    console.group('🔍 Checking Store Availability');
    
    const checks = {
      windowAppState: !!window.__APP_STATE__,
      authStore: !!window.__APP_STATE__?.auth,
      authTests: !!window.authTests,
      storeHasUser: !!window.__APP_STATE__?.auth?.user,
      storeHasSession: !!window.__APP_STATE__?.auth?.session,
      storeHasError: window.__APP_STATE__?.auth?.hasOwnProperty('error'),
      storeHasLoading: window.__APP_STATE__?.auth?.hasOwnProperty('loading'),
      storeHasTrialStatus: window.__APP_STATE__?.auth?.hasOwnProperty('trialStatus')
    };
    
    console.table(checks);
    
    const allChecks = Object.values(checks).every((check, index) => {
      // First 3 must be true, others can be false if no user
      if (index < 3) return check === true;
      return true; // Other checks don't need to be true
    });
    
    console.log(allChecks ? '✅ Stores properly initialized' : '❌ Store initialization issues');
    console.groupEnd();
    
    return allChecks;
  },
  
  // Check auth store methods
  checkAuthMethods() {
    console.group('🔍 Checking Auth Store Methods');
    
    const authStore = window.__APP_STATE__?.auth;
    if (!authStore) {
      console.error('❌ Auth store not found');
      console.groupEnd();
      return false;
    }
    
    const methods = [
      'signIn',
      'signUp', 
      'signOut',
      'signInWithProvider',
      'resetPassword',
      'updatePassword',
      'checkTrialStatus',
      'initialize',
      'cleanup'
    ];
    
    const methodChecks = methods.map(method => ({
      method,
      exists: typeof authStore[method] === 'function',
      type: typeof authStore[method]
    }));
    
    console.table(methodChecks);
    
    const allMethodsExist = methodChecks.every(check => check.exists);
    console.log(allMethodsExist ? '✅ All auth methods present' : '❌ Some auth methods missing');
    console.groupEnd();
    
    return allMethodsExist;
  },
  
  // Check useAuth hook compatibility
  async checkUseAuthHook() {
    console.group('🔍 Checking useAuth Hook');
    
    try {
      // Check if we can import the hook
      const { useAuth } = await import('../hooks/useAuth');
      console.log('✅ useAuth hook imported successfully');
      
      // Note: We can't actually call the hook here since we're not in a React component
      // But we can verify it exists
      console.log('Hook type:', typeof useAuth);
      console.log('Hook defined:', useAuth !== undefined);
      
      console.groupEnd();
      return true;
    } catch (error) {
      console.error('❌ Failed to import useAuth hook:', error);
      console.groupEnd();
      return false;
    }
  },
  
  // Run all checks
  async runAllChecks() {
    console.log('🏃 Running Auth Migration Verification...');
    console.log('=====================================');
    
    const results = {
      storesAvailable: this.checkStoresAvailable(),
      authMethods: this.checkAuthMethods(),
      useAuthHook: await this.checkUseAuthHook(),
      authTests: !!window.authTests
    };
    
    // Run auth tests if available
    if (window.authTests) {
      console.log('\n📋 Running Auth Tests...');
      const testResults = await window.authTests.runAll();
      results.authTestResults = testResults;
    }
    
    console.log('\n=====================================');
    console.log('📊 Migration Verification Summary:');
    console.table(results);
    
    const allPassed = results.storesAvailable && 
                     results.authMethods && 
                     results.useAuthHook && 
                     results.authTests;
    
    console.log('\n' + (allPassed ? '✅ Auth migration ready!' : '❌ Auth migration incomplete'));
    
    if (allPassed) {
      console.log('\n📝 Next Steps:');
      console.log('1. Record current state: window.authTests.recordCurrentState()');
      console.log('2. Test login/logout flows manually');
      console.log('3. After testing: window.authTests.compareWithRecordedState()');
    }
    
    return results;
  }
};

// Make available globally for testing
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.testAuthMigration = testAuthMigration;
  console.log('🧪 Auth migration verification loaded. Run: window.testAuthMigration.runAllChecks()');
}