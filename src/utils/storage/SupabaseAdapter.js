import { supabase, ensureAuthenticated } from '../../lib/supabaseOptimized';
import { createSpan, recordException, trackMetric } from '../honeycomb';

/**
 * @typedef {import('../../types/supabase-helpers').Document} Document
 * @typedef {import('../../types/supabase-helpers').Block} Block
 * @typedef {import('../../types/supabase-helpers').Profile} Profile
 * @typedef {import('../../types/supabase-helpers').DocumentShare} DocumentShare
 * @typedef {import('../../types/supabase-helpers').SupabaseError} SupabaseError
 */

/**
 * Supabase storage adapter for managing documents and blocks
 * Provides caching, error handling, and optimized queries
 */
export class SupabaseAdapter {
  constructor() {
    /** @type {boolean} */
    this.initialized = false;
    /** @type {Document[] | null} */
    this.documentsCache = null;
    /** @type {number} */
    this.cacheTimestamp = 0;
    /** @type {number} */
    this.CACHE_DURATION = 5000; // 5 seconds cache
    /** @type {Map<string, Promise<any>>} */
    this.saveQueue = new Map(); // Prevent concurrent saves
    /** @type {string | null} */
    this.userId = null;
  }
  
  /**
   * Invalidate the documents cache
   * @returns {void}
   */
  invalidateCache() {
    this.documentsCache = null;
    this.cacheTimestamp = 0;
  }

  /**
   * Initialize the adapter with user authentication
   * @param {string | null} [userId=null] - Optional user ID to avoid auth call
   * @returns {Promise<boolean>} - Success status
   * @throws {Error} If authentication fails
   */
  async init(userId = null) {
    // If userId is provided, use it directly (avoid extra auth call)
    if (userId) {
      console.log(`SupabaseAdapter: Init with provided userId ${userId}`);
      this.userId = userId;
      this.initialized = true;
      return true;
    }
    
    // Otherwise check if user is authenticated
    console.log('SupabaseAdapter: Getting user from auth...');
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('SupabaseAdapter: Auth error:', error);
      throw error;
    }
    
    if (!user) {
      console.error('SupabaseAdapter: No authenticated user');
      throw new Error('User must be authenticated');
    }
    
    console.log(`SupabaseAdapter: Init with auth userId ${user.id}`);
    this.userId = user.id;
    this.initialized = true;
    return true;
  }

  async getItem(key) {
    if (!this.initialized) await this.init();

    if (key === 'journeyLogEntries') {
      return this.getDocuments();
    }
    
    // For other settings/metadata
    const { data, error } = await supabase
      .from('settings')
      .select('value')
      .eq('user_id', this.userId)
      .eq('key', key)
      .maybeSingle();
    
    if (error && error.code !== 'PGRST116') { // Not found is ok
      console.error('Error getting item:', error);
      return null;
    }
    
    return data?.value || null;
  }

  async setItem(key, value) {
    if (!this.initialized) await this.init();

    if (key === 'journeyLogEntries') {
      // Handle batch update of all documents
      return this.updateAllDocuments(value);
    }

    // For other settings - check if exists first
    const { data: existing } = await supabase
      .from('settings')
      .select('id')
      .eq('user_id', this.userId)
      .eq('key', key)
      .maybeSingle();

    if (existing) {
      // Update existing
      const { error } = await supabase
        .from('settings')
        .update({
          value,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', this.userId)
        .eq('key', key);

      if (error) {
        console.error('Error updating setting:', error);
        throw error;
      }
    } else {
      // Insert new
      const { error } = await supabase
        .from('settings')
        .insert({
          user_id: this.userId,
          key,
          value,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error inserting setting:', error);
        throw error;
      }
    }
  }

  async removeItem(key) {
    if (!this.initialized) await this.init();

    const { error } = await supabase
      .from('settings')
      .delete()
      .eq('user_id', this.userId)
      .eq('key', key);

    if (error) {
      console.error('Error removing item:', error);
      throw error;
    }
  }

  async clear() {
    if (!this.initialized) await this.init();

    // Delete all user's documents
    const { error: docsError } = await supabase
      .from('documents')
      .delete()
      .eq('user_id', this.userId);

    if (docsError) {
      console.error('Error clearing documents:', docsError);
      throw docsError;
    }

    // Delete all user's settings
    const { error: settingsError } = await supabase
      .from('settings')
      .delete()
      .eq('user_id', this.userId);

    if (settingsError) {
      console.error('Error clearing settings:', settingsError);
      throw settingsError;
    }
  }

  /**
   * Get documents list without blocks (for dashboard/list views)
   * @returns {Promise<Document[]>} Array of documents without blocks
   * @throws {Error} If query fails
   */
  async getDocumentsList() {
    if (!this.initialized) await this.init();
    
    // Ensure authentication before querying
    try {
      await ensureAuthenticated();
    } catch (authError) {
      console.error('SupabaseAdapter: Authentication failed:', authError);
      throw authError;
    }

    const queryStart = performance.now();
    const { data: documents, error } = await supabase
      .from('documents')
      .select('id, title, tags, created_at, updated_at, is_template, metadata, folder_id, position')
      .eq('user_id', this.userId)
      .order('updated_at', { ascending: false });
    
    const queryTime = performance.now() - queryStart;
    console.log(`SupabaseAdapter: Documents list query completed in ${Math.round(queryTime)}ms`);
    
    if (error) {
      console.error('Error getting documents list:', error);
      return [];
    }
    
    return documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      preview: doc.metadata?.preview || 'Click to view document...', // Use stored preview
      createdAt: doc.created_at,
      updatedAt: doc.updated_at,
      isTemplate: doc.is_template,
      tags: doc.tags || [],
      metadata: doc.metadata || {},
      blocks: [], // Empty blocks array for list view
      blockCount: doc.metadata?.blockCount || 0, // Include block count
      folder_id: doc.folder_id || null,
      position: doc.position || 0
    }));
  }

  /**
   * Get all documents for the current user (without blocks)
   * @returns {Promise<Document[]>} Array of documents
   * @throws {Error} If query fails
   */
  async getDocuments() {
    if (!this.initialized) await this.init();
    console.log('SupabaseAdapter: getDocuments called');
    
    // Ensure authentication before querying
    try {
      await ensureAuthenticated();
    } catch (authError) {
      console.error('SupabaseAdapter: Authentication failed:', authError);
      throw authError;
    }

    // Check cache first
    const now = Date.now();
    if (this.documentsCache && (now - this.cacheTimestamp) < this.CACHE_DURATION) {
      console.log('SupabaseAdapter: Returning cached documents');
      return this.documentsCache;
    }

    const queryStart = performance.now();
    
    // Try optimized function first
    let documents = null;
    let docError = null;
    
    // Use direct query - don't try RPC function that doesn't exist
    console.log(`SupabaseAdapter: Querying documents for user ${this.userId}`);
    
    const { data, error } = await supabase
      .from('documents')
      .select('id, title, tags, created_at, updated_at, metadata, is_template, project_id, folder_id, position')
      .eq('user_id', this.userId)
      .is('deleted_at', null)
      .order('updated_at', { ascending: false });
    
    documents = data;
    docError = error;
    
    const queryTime = performance.now() - queryStart;
    console.log(`SupabaseAdapter: Documents query completed in ${Math.round(queryTime)}ms`);
    
    if (docError) {
      console.error('Error getting documents:', docError);
      return this.documentsCache || [];
    }
    
    if (!documents || documents.length === 0) {
      console.log('SupabaseAdapter: No documents found, returning empty array');
      return [];
    }
    
    console.log(`SupabaseAdapter: Found ${documents.length} documents`);
    
    // Check IndexedDB for unsynced documents
    let unsyncedDocs = [];
    try {
      const IndexedDBAdapter = (await import('./IndexedDBAdapter.js')).default;
      const allIndexedDBDocs = await IndexedDBAdapter.getAllDocuments();
      
      // Filter for documents that are marked as unsynced
      unsyncedDocs = allIndexedDBDocs.filter(doc => 
        doc.metadata?.syncStatus === 'pending' || 
        doc.metadata?.createdLocally === true
      );
      
      console.log(`SupabaseAdapter: Found ${unsyncedDocs.length} unsynced documents in IndexedDB`);
    } catch (error) {
      console.warn('Could not check IndexedDB for unsynced documents:', error);
    }
    
    // Create a map of Supabase documents for easy lookup
    const supabaseDocMap = new Map(documents.map(doc => [doc.id, doc]));
    
    // Merge unsynced documents with Supabase results
    const mergedDocuments = [...documents];
    
    for (const unsyncedDoc of unsyncedDocs) {
      if (!supabaseDocMap.has(unsyncedDoc.id)) {
        // This document only exists in IndexedDB
        mergedDocuments.push({
          ...unsyncedDoc,
          created_at: unsyncedDoc.createdAt,
          updated_at: unsyncedDoc.updatedAt,
          is_template: unsyncedDoc.isTemplate || false
        });
      }
    }
    
    console.log(`SupabaseAdapter: Total documents after merge: ${mergedDocuments.length}`);
    
    // Transform documents to app format
    const documentsWithBlocks = mergedDocuments.map(doc => ({
      id: doc.id,
      title: doc.title,
      preview: doc.metadata?.preview || doc.preview || 'Click to view document...',
      createdAt: doc.created_at || doc.createdAt,
      updatedAt: doc.updated_at || doc.updatedAt,
      isTemplate: doc.is_template || doc.isTemplate || false,
      tags: doc.tags || [],
      metadata: doc.metadata || {},
      blocks: [], // Don't load blocks on document list - let ExpandedView handle it
      blockCount: doc.metadata?.blockCount || doc.block_count || doc.blockCount || 0,
      project_id: doc.project_id || null, // Include project_id for filtering
      folder_id: doc.folder_id || null,
      position: doc.position || 0
    }));
    
    // Transform to legacy format
    const transformedDocuments = documentsWithBlocks;
    
    // Update cache
    this.documentsCache = transformedDocuments;
    this.cacheTimestamp = now;
    
    console.log(`SupabaseAdapter: Returning ${transformedDocuments.length} documents`);
    
    return transformedDocuments;
  }

  /**
   * Get a single document with all its blocks (for editing)
   * @param {string} documentId - The document ID
   * @returns {Promise<Document & {blocks: Block[]} | null>} Document with blocks or null
   */
  async getDocument(documentId) {
    if (!this.initialized) await this.init();
    
    const queryStart = performance.now();
    
    // Get document
    const { data: doc, error: docError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', this.userId)
      .single();
    
    if (docError) {
      console.error('Error getting document:', docError);
      return null;
    }
    
    // Get blocks for this document
    const { data: blocks, error: blockError } = await supabase
      .from('blocks')
      .select('*')
      .eq('document_id', documentId)
      .order('position');
    
    if (blockError) {
      console.error('Error getting blocks:', blockError);
    }
    
    const queryTime = performance.now() - queryStart;
    console.log(`SupabaseAdapter: Single document query completed in ${Math.round(queryTime)}ms`);
    console.log(`SupabaseAdapter: Loaded ${blocks?.length || 0} blocks for document ${documentId}`);
    
    return {
      id: doc.id,
      title: doc.title,
      preview: this.generatePreview(blocks || []),
      createdAt: doc.created_at,
      updatedAt: doc.updated_at,
      isTemplate: doc.is_template,
      tags: doc.tags || [],
      metadata: doc.metadata || {},
      blocks: (blocks || []).map(block => this.transformBlockFromDB(block))
    };
  }

  /**
   * Save or update a document with its blocks
   * @param {Document & {blocks?: Block[]}} document - Document to save
   * @returns {Promise<Document>} Saved document
   * @throws {Error} If save fails
   */
  async saveDocument(document) {
    if (!this.initialized) await this.init();

    // Start Honeycomb span for tracking save operation
    const span = createSpan('supabase.saveDocument', {
      'document.id': document.id,
      'document.blocks.count': document.blocks?.length || 0,
      'document.folder_id': document.folder_id || 'none',
      'document.title': document.title,
      'document.size': JSON.stringify(document).length,
    });
    
    const startTime = performance.now();

    console.log(`SupabaseAdapter: saveDocument called`, {
      documentId: document.id,
      blockCount: document.blocks?.length || 0,
      folderId: document.folder_id || 'none',
      hasCreatedAt: !!document.created_at || !!document.createdAt,
      title: document.title
    });
    
    // Skip auth check if we already have userId (reduces latency)
    if (!this.userId) {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.error('Authentication error:', authError);
        throw new Error('Authentication required for save operation');
      }
    }
    console.log(`SupabaseAdapter: Using userId ${this.userId} for save`);
    
    // Check if document is deleted - prevent saving deleted documents
    if (document.deleted_at || document.metadata?.deleted) {
      console.log(`SupabaseAdapter: Skipping save for deleted document ${document.id}`);
      
      // End span with deletion status
      if (span) {
        span.setAttributes({
          'save.skipped': true,
          'save.reason': 'document_deleted',
        });
        span.end();
      }
      
      return { 
        success: false, 
        reason: 'Document is deleted',
        document: document 
      };
    }
    
    // CRITICAL: Prevent data loss - check if we're trying to save 0 blocks for a document that has blocks
    const blocks = document.blocks || [];
    const documentId = document.id;
    const isNewDocument = document.metadata?.isNewDocument === true || 
                         document.metadata?.createdLocally === true ||
                         !document.createdAt;
    
    // Only perform the safety check for existing documents, not brand new ones
    if (blocks.length === 0 && documentId && documentId !== 'new' && !isNewDocument) {
      // Check if this document already has blocks
      const { data: existingBlocks, error: checkError } = await supabase
        .from('blocks')
        .select('id')
        .eq('document_id', documentId)
        .is('deleted_at', null)
        .limit(1);
      
      if (!checkError && existingBlocks && existingBlocks.length > 0) {
        console.error(`🚨 CRITICAL: Attempted to save 0 blocks for document ${documentId} that has existing blocks. Preventing data loss.`);
        console.warn('Stack trace:', new Error().stack);
        
        // Return the document without saving to prevent data loss
        return {
          ...document,
          id: documentId,
          blocks: [] // Return empty blocks as requested, but don't delete existing ones
        };
      }
    }
    
    const { blocks: documentBlocks, ...docData } = document;
    
    // CRITICAL FIX: If blocks are not provided, this is a partial update
    // Don't touch the blocks - only update document metadata
    if (documentBlocks === undefined) {
      console.log('SupabaseAdapter: Partial update detected (no blocks provided), updating only document metadata');
      
      const updateData = {};
      
      // Only include fields that were explicitly provided
      if (docData.title !== undefined) updateData.title = docData.title;
      if (docData.tags !== undefined) updateData.tags = docData.tags;
      if (docData.metadata !== undefined) {
        // Merge with existing metadata to not lose fields
        const { data: currentDoc } = await supabase
          .from('documents')
          .select('metadata')
          .eq('id', docData.id)
          .single();
        
        updateData.metadata = {
          ...(currentDoc?.metadata || {}),
          ...docData.metadata
        };
      }
      
      updateData.updated_at = new Date().toISOString();
      
      const { data: savedDoc, error: docError } = await supabase
        .from('documents')
        .update(updateData)
        .eq('id', docData.id)
        .eq('user_id', this.userId)
        .select()
        .single();
      
      if (docError) {
        console.error('Error updating document metadata:', docError);
        
        // Record error in Honeycomb
        recordException(docError, {
          'operation': 'document.metadata_update',
          'document.id': docData.id,
        });
        
        // End span with error
        if (span) {
          span.setAttributes({
            'error': true,
            'error.message': docError.message,
          });
          span.end();
        }
        
        throw docError;
      }
      
      // Track successful metadata update
      const duration = performance.now() - startTime;
      if (span) {
        span.setAttributes({
          'save.duration_ms': duration,
          'save.success': true,
          'save.type': 'metadata_only',
        });
        span.end();
      }
      
      // Invalidate cache and return
      this.invalidateCache();
      return savedDoc.id;
    }
    
    // Use preview from document if already provided, otherwise generate
    let preview = docData.preview || 'Click to view document...';
    if (!docData.preview && documentBlocks && documentBlocks.length > 0) {
      const firstTextBlock = documentBlocks.find(b => b.type === 'text' && b.content);
      const firstHeading = documentBlocks.find(b => b.type === 'heading' && b.content);
      preview = firstTextBlock?.content.substring(0, 100) + '...' || 
                firstHeading?.content || 
                'Click to start writing...';
    }
    
    // Check if this is a new document
    // Only consider it new if it has no createdAt (never saved to DB)
    // The metadata flags alone don't mean it's new - they might just be stale
    const isNewDocumentForSave = !docData.createdAt && !docData.created_at;
    const hasFolderId = docData.folder_id && docData.folder_id !== null;
    
    // Special handling for documents created locally with folders
    // These need to be created in the database first
    let isLocallyCreatedWithFolder = docData.metadata?.createdLocally && hasFolderId;
    
    let savedDoc;
    let docError;
    
    if ((isNewDocumentForSave || isLocallyCreatedWithFolder) && hasFolderId) {
      // First check if document already exists in database
      const { data: existingDoc, error: checkError } = await supabase
        .from('documents')
        .select('id, created_at')
        .eq('id', docData.id)
        .eq('user_id', this.userId)
        .maybeSingle();
      
      if (existingDoc) {
        // Document already exists, update it instead
        console.log('SupabaseAdapter: Document already exists in database, updating instead of creating');
        isLocallyCreatedWithFolder = false; // Clear the flag
        // Fall through to the regular update logic below
      } else {
        // Document doesn't exist, create it
        console.log('SupabaseAdapter: Using security definer function for document creation with folder');
        
        const { data, error } = await supabase.rpc('create_document_with_folder_check', {
          p_id: docData.id,
          p_title: docData.title,
          p_folder_id: docData.folder_id,
          p_preview: preview,
          p_tags: docData.tags || [],
          p_metadata: {
            ...(docData.metadata || {}),
            preview: preview,
            blockCount: documentBlocks?.length || 0,
            syncStatus: 'synced',
            lastSyncedAt: new Date().toISOString(),
            isNewDocument: false, // Clear the flag after first save
            createdLocally: false // Clear this flag too
          },
          p_is_template: docData.isTemplate || false,
          p_position: docData.position || 0
        });
        
        savedDoc = data;
        docError = error;
      }
      
      // Add a small delay to ensure transaction visibility
      if (!docError && documentBlocks && documentBlocks.length > 0) {
        console.log('SupabaseAdapter: Adding delay to ensure document visibility before saving blocks');
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    // Declare documentToSave at function scope to avoid ReferenceError
    let documentToSave = null;
    
    // If we haven't saved the document yet (either it's not new with folder, or it already exists)
    if (!savedDoc && !docError) {
      // Use regular upsert for updates or documents without folders
      documentToSave = {
        id: docData.id,
        user_id: this.userId,
        title: docData.title,
        is_template: docData.isTemplate || false,
        tags: docData.tags || [],
        metadata: {
          ...(docData.metadata || {}),
          preview: preview,
          blockCount: documentBlocks?.length || 0,
          syncStatus: 'synced', // Mark as synced when saved to Supabase
          lastSyncedAt: new Date().toISOString(),
          isNewDocument: false, // Clear the flag after first save
          createdLocally: false // Clear this flag too
        },
        created_at: docData.createdAt || docData.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        folder_id: docData.folder_id || null,
        position: docData.position || 0
      };
      
      console.log('SupabaseAdapter: Saving document to Supabase:', {
        id: documentToSave.id,
        title: documentToSave.title,
        blockCount: documentBlocks?.length || 0,
        userId: this.userId,
        isNew: isNewDocumentForSave
      });
      
      if (isNewDocumentForSave || isLocallyCreatedWithFolder) {
        // For new documents or documents created locally with folders, use insert
        const { data, error } = await supabase
          .from('documents')
          .insert(documentToSave)
          .select()
          .single();
        
        savedDoc = data;
        docError = error;
      } else {
        // For existing documents, use update to respect RLS policies
        // Note: Supabase update with multiple conditions can cause 406 errors
        // We'll use a simpler approach with just id and user_id
        const { data, error } = await supabase
          .from('documents')
          .update({
            title: documentToSave.title,
            is_template: documentToSave.is_template,
            tags: documentToSave.tags,
            metadata: documentToSave.metadata,
            updated_at: documentToSave.updated_at,
            folder_id: documentToSave.folder_id,
            position: documentToSave.position
          })
          .eq('id', documentToSave.id)
          .eq('user_id', this.userId)
          .is('deleted_at', null)
          .select()
          .single();
        
        savedDoc = data;
        docError = error;
      }
    }
    
    if (docError) {
      // Check if the error is because the document was deleted
      if (docError.code === 'PGRST116' && docError.details?.includes('0 rows')) {
        console.log(`SupabaseAdapter: Document ${documentToSave?.id || docData.id} appears to be deleted, skipping save`);
        return {
          success: false,
          reason: 'Document not found or deleted',
          document: document
        };
      }
      
      console.error('Error saving document:', {
        code: docError.code,
        message: docError.message,
        details: docError.details,
        hint: docError.hint,
        status: docError.status,
        statusText: docError.statusText,
        // Additional debugging info for 406 errors
        documentId: documentToSave?.id || docData.id,
        userId: this.userId,
        isNewDocument: isNewDocumentForSave
      });
      
      // Special handling for 406 errors
      if (docError.status === 406) {
        console.error('406 Error - This usually indicates a content negotiation issue or invalid query syntax');
      }
      
      throw docError;
    }
    
    if (!savedDoc) {
      console.error('Document save returned no data');
      throw new Error('Document save failed - no data returned');
    }
    
    console.log('SupabaseAdapter: Document saved successfully:', {
      id: savedDoc.id,
      title: savedDoc.title,
      updated_at: savedDoc.updated_at
    });
    
    // 3. Use atomic function to save blocks
    // console.log(`SupabaseAdapter: Using atomic save for ${documentBlocks?.length || 0} blocks`);
    
    // Prepare blocks for the RPC call - use Promise.resolve to prevent blocking
    const blocksToSave = await Promise.resolve((documentBlocks || []).map((block, index) => {
      // console.log(`🟨 SupabaseAdapter: Processing block for save:`, {
      //   index: index,
      //   blockId: block.id,
      //   blockType: block.type,
      //   hasData: !!block.data,
      //   hasMetadata: !!block.metadata,
      //   dataContent: block.data,
      //   metadataContent: block.metadata,
      //   blockKeys: Object.keys(block)
      // });
      
      // Extract all block data that should be persisted in metadata
      const extractBlockData = (block) => {
        const metadata = {};
        
        // List of properties to exclude from metadata (they have their own columns)
        const excludedProps = ['id', 'type', 'content', 'position', 'tags', 'language', 'filePath', 'isNew', 'createdAt', 'updatedAt'];
        
        // Copy all non-excluded properties to metadata
        Object.keys(block).forEach(key => {
          if (!excludedProps.includes(key) && block[key] !== undefined) {
            metadata[key] = block[key];
          }
        });
        
        // Also merge any existing data or metadata
        if (block.data) {
          Object.assign(metadata, block.data);
        }
        if (block.metadata) {
          Object.assign(metadata, block.metadata);
        }
        
        // Debug logging for AI blocks
        if (block.type === 'ai') {
          console.log('🔵 AI Block Save Debug:', {
            blockId: block.id,
            hasMessages: !!block.messages,
            messageCount: block.messages?.length || 0,
            metadataKeys: Object.keys(metadata),
            hasMessagesInMetadata: !!metadata.messages
          });
        }
        
        return metadata;
      };
      
      const blockToSave = {
        id: block.id || crypto.randomUUID(), // Generate ID if missing
        type: block.type,
        content: block.content || '',
        position: index,
        metadata: extractBlockData(block),  // Extract all block-specific data
        tags: block.tags || [],  // Include tags for the RPC function
        language: block.language || null,
        file_path: block.filePath || null
      };
      
      // console.log(`🟨 SupabaseAdapter: Block prepared for DB:`, {
      //   blockId: blockToSave.id,
      //   blockType: blockToSave.type,
      //   metadataAssigned: blockToSave.metadata,
      //   metadataSource: block.data ? 'block.data' : (block.metadata ? 'block.metadata' : 'empty object')
      // });
      
      return blockToSave;
    }));
    
    // console.log(`SupabaseAdapter: Preparing to save ${blocksToSave.length} blocks:`, 
    //   blocksToSave.map(b => ({ id: b.id, type: b.type, content: b.content.substring(0, 50) + '...' }))
    // );
    
    // Note: Atomic save is now handled earlier in the flow for new documents with folders
    // This prevents race conditions when saving blocks immediately after document creation
    
    // Try optimized save function first, fallback to original if not available
    let blocksError = null;
    let useOptimized = true;
    let retryCount = 0;
    const maxRetries = 2;
    
    // Retry logic for the optimized function as well
    while (retryCount <= maxRetries && useOptimized) {
      try {
        // Try the safer save_document_blocks_v3 function that prevents data loss
        const { error } = await supabase.rpc('save_document_blocks_v3', {
          p_document_id: savedDoc.id,
          p_blocks: blocksToSave
        });
        
        if (error) {
          if (error.message?.includes('function') && error.message?.includes('does not exist')) {
            console.log('SupabaseAdapter: Optimized function not available, falling back to original');
            useOptimized = false;
            break;
          } else if (error.message?.includes('Document not found') && retryCount < maxRetries) {
            // Document might not be visible yet due to transaction timing
            console.log(`Document not found error, retrying (${retryCount + 1}/${maxRetries})...`);
            await new Promise(resolve => setTimeout(resolve, 200 * (retryCount + 1)));
            retryCount++;
            continue;
          } else {
            blocksError = error;
            break;
          }
        } else {
          // Success, break the loop
          break;
        }
      } catch (e) {
        useOptimized = false;
        break;
      }
    }
    
    // Fallback to original function with retry logic
    if (!useOptimized) {
      let retryCount = 0;
      const maxRetries = 2;
      
      while (retryCount <= maxRetries) {
        const { error } = await supabase.rpc('save_document_blocks', {
          doc_id: savedDoc.id,
          blocks: blocksToSave
        });
        
        blocksError = error;
        
        // If no error or not a duplicate key error, break
        if (!error || error.code !== '23505') {
          break;
        }
        
        // On duplicate key error, wait and retry
        console.log(`Duplicate key error, retrying (${retryCount + 1}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 500 * (retryCount + 1)));
        retryCount++;
      }
    }
    
    if (blocksError) {
      console.error('Error saving blocks atomically:', blocksError);
      throw blocksError;
    }
    
    // console.log(`SupabaseAdapter: Successfully saved document ${savedDoc.id} with ${blocksToSave.length} blocks atomically`);
    
    // Verify blocks were saved (in development only)
    if (process.env.NODE_ENV === 'development') {
      const { data: savedBlocks, error: verifyError } = await supabase
        .from('blocks')
        .select('id, type, content, position')
        .eq('document_id', savedDoc.id)
        .order('position');
      
      if (verifyError) {
        console.error('Error verifying saved blocks:', verifyError);
      } else {
        // console.log(`SupabaseAdapter: Verified ${savedBlocks?.length || 0} blocks saved for document ${savedDoc.id}`);
      }
    }
    
    // Invalidate cache after successful save
    this.invalidateCache();
    
    // Update the document in IndexedDB to clear the createdLocally flag
    if (savedDoc && (docData.metadata?.createdLocally || docData.metadata?.isNewDocument)) {
      try {
        const updatedDoc = {
          ...docData,
          createdAt: savedDoc.created_at,
          created_at: savedDoc.created_at,
          metadata: {
            ...(docData.metadata || {}),
            createdLocally: false,
            isNewDocument: false,
            syncStatus: 'synced',
            lastSyncedAt: new Date().toISOString()
          }
        };
        
        // Update in IndexedDB
        const IndexedDBAdapter = (await import('./IndexedDBAdapter.js')).default;
        await IndexedDBAdapter.saveDocument(updatedDoc);
        console.log('SupabaseAdapter: Updated document in IndexedDB to clear createdLocally flag');
      } catch (error) {
        console.error('Error updating document in IndexedDB:', error);
        // Don't throw - the save to Supabase was successful
      }
    }

    // Track successful save completion
    const duration = performance.now() - startTime;
    trackMetric('document.save.duration', duration, {
      'document.id': savedDoc.id,
      'blocks.count': documentBlocks?.length || 0,
      'success': true,
    });
    
    // End the span successfully
    if (span) {
      span.setAttributes({
        'save.duration_ms': duration,
        'save.success': true,
        'blocks.saved': documentBlocks?.length || 0,
      });
      span.end();
    }

    return savedDoc.id;
  }

  // Safe save method that prevents concurrent saves for the same document
  async saveDocumentSafe(document) {
    const documentId = document.id;
    
    // Prevent concurrent saves for the same document
    if (this.saveQueue.has(documentId)) {
      console.log(`SupabaseAdapter: Waiting for previous save of document ${documentId} to complete`);
      await this.saveQueue.get(documentId);
    }
    
    const savePromise = this.saveDocument(document);
    this.saveQueue.set(documentId, savePromise);
    
    try {
      const result = await savePromise;
      return result;
    } finally {
      this.saveQueue.delete(documentId);
    }
  }

  async deleteDocument(documentId) {
    if (!this.initialized) await this.init();

    try {
      console.log(`SupabaseAdapter: Starting deletion of document ${documentId}`);
      
      const deleteStart = performance.now();
      
      // Try soft delete first (if function exists)
      try {
        const { data, error } = await supabase.rpc('soft_delete_document', {
          p_document_id: documentId
        });
        
        if (!error) {
          console.log('SupabaseAdapter: Soft delete successful');
          this.invalidateCache();
          const deleteTime = performance.now() - deleteStart;
          console.log(`SupabaseAdapter: Soft delete completed in ${Math.round(deleteTime)}ms`);
          return true;
        } else if (!error.message?.includes('function') || !error.message?.includes('does not exist')) {
          // If it's not a "function doesn't exist" error, throw it
          throw error;
        }
      } catch (e) {
        if (!e.message?.includes('function') || !e.message?.includes('does not exist')) {
          throw e;
        }
      }
      
      // Fallback to hard delete if soft delete not available
      console.log('SupabaseAdapter: Soft delete not available, using hard delete');
      
      // Use Promise.allSettled to continue even if one fails
      const [blocksResult, docResult] = await Promise.allSettled([
        // Delete blocks
        supabase
          .from('blocks')
          .delete()
          .eq('document_id', documentId),
        
        // Delete document
        supabase
          .from('documents')
          .delete()
          .eq('id', documentId)
          .eq('user_id', this.userId)
      ]);
      
      const deleteTime = performance.now() - deleteStart;
      console.log(`SupabaseAdapter: Hard delete operations completed in ${Math.round(deleteTime)}ms`);
      
      // Check results
      if (blocksResult.status === 'rejected') {
        console.error('Error deleting blocks:', blocksResult.reason);
      } else if (blocksResult.value.error) {
        console.error('Error deleting blocks:', blocksResult.value.error);
      }
      
      if (docResult.status === 'rejected') {
        console.error('Error deleting document:', docResult.reason);
        throw docResult.reason;
      } else if (docResult.value.error) {
        console.error('Error deleting document:', docResult.value.error);
        throw docResult.value.error;
      }

      // Invalidate cache after successful delete
      this.invalidateCache();
      
      console.log(`Successfully deleted document ${documentId}`);
      return true;
    } catch (error) {
      console.error('Error in deleteDocument:', error);
      throw error;
    }
  }

  // Generate preview from blocks
  generatePreview(blocks) {
    if (!blocks || blocks.length === 0) {
      return 'Click to view document...';
    }
    
    // Find first text content block
    const firstTextBlock = blocks.find(b => 
      (b.type === 'text' || b.type === 'heading') && b.content
    );
    
    if (firstTextBlock) {
      const preview = firstTextBlock.content.substring(0, 150);
      return preview.length < firstTextBlock.content.length ? preview + '...' : preview;
    }
    
    return 'Click to view document...';
  }

  // Transform blocks between DB and app formats
  transformBlockFromDB(block) {
    // console.log(`🟧 SupabaseAdapter: transformBlockFromDB called:`, {
    //   blockId: block.id,
    //   blockType: block.type,
    //   hasMetadata: !!block.metadata,
    //   metadataContent: block.metadata,
    //   dbBlockKeys: Object.keys(block)
    // });
    
    const baseBlock = {
      id: block.id,
      type: block.type,
      content: block.content || ''
    };

    // Add type-specific fields
    if (block.type === 'code') {
      baseBlock.language = block.language;
      baseBlock.filePath = block.file_path;
      baseBlock.versionOf = block.version_of;
    }

    // For blocks that use 'data' property (table, todo, template, version-track, issue-tracker), restore it from metadata
    if (block.type === 'table' || block.type === 'todo' || block.type === 'template' || block.type === 'version-track' || block.type === 'issue-tracker') {
      baseBlock.data = block.metadata || {};
      // console.log(`🟧 SupabaseAdapter: Restoring data property for ${block.type} block:`, {
      //   blockId: block.id,
      //   restoredData: baseBlock.data,
      //   isEmptyData: Object.keys(baseBlock.data).length === 0
      // });
    } else if (block.metadata) {
      // For other blocks, merge metadata properties directly
      Object.assign(baseBlock, block.metadata);
      
      // Debug logging for AI blocks
      if (block.type === 'ai') {
        console.log('🔵 AI Block Load Debug:', {
          blockId: block.id,
          metadataKeys: Object.keys(block.metadata || {}),
          hasMessagesInMetadata: !!block.metadata?.messages,
          messageCount: block.metadata?.messages?.length || 0,
          hasMessagesInBaseBlock: !!baseBlock.messages
        });
      }
      
      // Also handle specific known properties for certain block types
      if (block.type === 'filetree' && block.metadata.treeData) {
        baseBlock.treeData = block.metadata.treeData;
      }
      if (block.type === 'ai' && block.metadata.messages) {
        baseBlock.messages = block.metadata.messages;
      }
      if (block.type === 'image' && block.metadata.images) {
        baseBlock.images = block.metadata.images;
      }
      if (block.type === 'inline-image') {
        // inline-image stores properties directly in metadata
        if (block.metadata.url) baseBlock.url = block.metadata.url;
        if (block.metadata.alt) baseBlock.alt = block.metadata.alt;
        if (block.metadata.dimensions) baseBlock.dimensions = block.metadata.dimensions;
      }
    }

    // console.log(`🟧 SupabaseAdapter: Block transformed from DB:`, {
    //   blockId: baseBlock.id,
    //   blockType: baseBlock.type,
    //   hasData: !!baseBlock.data,
    //   transformedBlockKeys: Object.keys(baseBlock),
    //   finalBlock: baseBlock
    // });

    return baseBlock;
  }

  transformBlockToDB(block, documentId, position) {
    const dbBlock = {
      id: block.id,
      document_id: documentId,
      user_id: this.userId, // Add user_id for RLS policy
      type: block.type,
      content: block.content || '',
      position: position,
      metadata: {}
    };

    // Handle type-specific fields
    if (block.type === 'code') {
      dbBlock.language = block.language;
      dbBlock.file_path = block.filePath;
      dbBlock.version_of = block.versionOf;
    }

    // Store other fields in metadata
    const knownFields = ['id', 'type', 'content', 'language', 'filePath', 'versionOf'];
    Object.keys(block).forEach(key => {
      if (!knownFields.includes(key)) {
        dbBlock.metadata[key] = block[key];
      }
    });

    // Generate content preview for version-track blocks
    if (block.type === 'version-track' && block.data?.repository) {
      const repo = block.data.repository;
      const versionCount = Object.keys(repo.versions || {}).length;
      const branchCount = Object.keys(repo.branches || {}).length;
      const currentVersion = repo.HEAD ? repo.versions[repo.HEAD] : null;
      
      dbBlock.content = `Version Control: ${versionCount} versions, ${branchCount} branches${currentVersion ? ` - Latest: "${currentVersion.message}"` : ''}`;
    }

    return dbBlock;
  }

  // Batch update for compatibility
  async updateAllDocuments(documents) {
    if (!this.initialized) await this.init();

    console.log(`SupabaseAdapter: updateAllDocuments called with ${documents.length} documents`);
    
    // Only save documents that have blocks loaded
    // Skip documents where blocks are undefined (not loaded from getDocuments)
    for (const doc of documents) {
      // CRITICAL: Skip documents without loaded blocks to prevent data loss
      if (doc.blocks === undefined) {
        console.log(`SupabaseAdapter: Skipping save for document ${doc.id} - blocks not loaded`);
        continue;
      }
      
      // CRITICAL: Skip documents with empty blocks array that came from getDocuments()
      // These documents have blocks: [] because getDocuments() doesn't load blocks for performance
      // We can identify them by checking if they have blockCount metadata but 0 blocks
      if (doc.blocks.length === 0 && doc.blockCount > 0) {
        console.log(`SupabaseAdapter: Skipping save for document ${doc.id} - has ${doc.blockCount} blocks but blocks not loaded`);
        continue;
      }
      
      // Only save if blocks are explicitly provided (even if empty array)
      console.log(`SupabaseAdapter: Saving document ${doc.id} with ${doc.blocks.length} blocks`);
      await this.saveDocument(doc);
    }

    // Invalidate cache after successful update
    this.invalidateCache();
  }

  // Storage info methods (for compatibility)
  async getStorageInfo() {
    return {
      usage: 0,
      quota: Infinity,
      percentUsed: 0
    };
  }

  isAvailable() {
    return true;
  }

  getName() {
    return 'Supabase';
  }

  // Project CRUD operations
  async getProjects() {
    if (!this.initialized) await this.init();
    
    console.log('SupabaseAdapter: Getting projects...');
    
    try {
      // Get projects
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', this.userId)
        .order('created_at', { ascending: false });
      
      if (projectsError) {
        console.error('SupabaseAdapter: Error getting projects:', projectsError);
        return [];
      }
      
      // Get document counts for each project
      const projectsWithCounts = await Promise.all(projects.map(async (project) => {
        const { count, error: countError } = await supabase
          .from('documents')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', this.userId)
          .eq('project_id', project.id);
        
        if (countError) {
          console.error(`Error getting count for project ${project.id}:`, countError);
        }
        
        return {
          ...project,
          document_count: count || 0
        };
      }));
      
      console.log(`SupabaseAdapter: Found ${projectsWithCounts.length} projects`);
      return projectsWithCounts;
    } catch (error) {
      console.error('SupabaseAdapter: Failed to get projects:', error);
      return [];
    }
  }

  async createProject(projectData) {
    if (!this.initialized) await this.init();
    
    console.log('SupabaseAdapter: Creating project:', projectData);
    
    const { data, error } = await supabase
      .from('projects')
      .insert({
        user_id: this.userId,
        title: projectData.title,
        description: projectData.description || null,
        color: projectData.color || '#10b981',
        icon: projectData.icon || 'folder'
      })
      .select()
      .single();
    
    if (error) {
      console.error('SupabaseAdapter: Error creating project:', error);
      throw error;
    }
    
    console.log('SupabaseAdapter: Project created:', data);
    return data;
  }

  async updateProject(projectId, updates) {
    if (!this.initialized) await this.init();
    
    console.log('SupabaseAdapter: Updating project:', projectId, updates);
    
    const { data, error } = await supabase
      .from('projects')
      .update({
        title: updates.title,
        description: updates.description,
        color: updates.color,
        icon: updates.icon,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId)
      .eq('user_id', this.userId)
      .select()
      .single();
    
    if (error) {
      console.error('SupabaseAdapter: Error updating project:', error);
      throw error;
    }
    
    console.log('SupabaseAdapter: Project updated:', data);
    return data;
  }

  async deleteProject(projectId) {
    if (!this.initialized) await this.init();
    
    console.log('SupabaseAdapter: Deleting project:', projectId);
    
    // First, unassign all documents from this project
    const { error: unassignError } = await supabase
      .from('documents')
      .update({ project_id: null })
      .eq('project_id', projectId)
      .eq('user_id', this.userId);
    
    if (unassignError) {
      console.error('SupabaseAdapter: Error unassigning documents:', unassignError);
      throw unassignError;
    }
    
    // Then delete the project
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId)
      .eq('user_id', this.userId);
    
    if (error) {
      console.error('SupabaseAdapter: Error deleting project:', error);
      throw error;
    }
    
    console.log('SupabaseAdapter: Project deleted successfully');
    return true;
  }

  async assignDocumentToProject(documentId, projectId) {
    if (!this.initialized) await this.init();
    
    console.log('SupabaseAdapter: Assigning document to project:', { documentId, projectId });
    
    const { error } = await supabase
      .from('documents')
      .update({ 
        project_id: projectId,
        updated_at: new Date().toISOString()
      })
      .eq('id', documentId)
      .eq('user_id', this.userId);
    
    if (error) {
      console.error('SupabaseAdapter: Error assigning document:', error);
      throw error;
    }
    
    // Invalidate cache to reflect the change
    this.invalidateCache();
    
    console.log('SupabaseAdapter: Document assigned successfully');
    return true;
  }
}