# Storage System Usage Guide

## Quick Start

To use the new IndexedDB storage system in your app, you have two options:

### Option 1: Direct Replacement (Recommended)
Simply rename the files to use the new Dashboard:
```bash
# Backup original
mv src/pages/Dashboard.jsx src/pages/Dashboard.backup.jsx

# Use new version
mv src/pages/DashboardWithIndexedDB.jsx src/pages/Dashboard.jsx
```

### Option 2: Test First
Update your App.jsx to import the new Dashboard:
```javascript
// In src/App.jsx
import Dashboard from './pages/DashboardWithIndexedDB';
```

## Features Implemented

### 1. IndexedDB Storage (✅ Completed)
- **Capacity**: 1GB+ vs 5-10MB with localStorage
- **Performance**: Faster queries with indexes
- **Reliability**: Transactional operations
- **Backwards Compatible**: Falls back to localStorage if needed

### 2. LZ-String Compression (✅ Completed)
- **Space Savings**: 50-80% compression ratio
- **Automatic**: Transparent compression/decompression
- **Smart**: Only compresses document blocks (largest data)
- **Stats Tracking**: Monitor compression effectiveness

### 3. Storage Monitoring (✅ Completed)
- **Real-time Usage**: Shows current storage usage
- **Visual Indicators**: Color-coded warnings
- **Storage Type**: Shows if using IndexedDB or localStorage

## Architecture

```
┌─────────────────────┐
│     Dashboard       │  <-- Your React Component
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│   storageWrapper    │  <-- Compatibility Layer
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│ CompressedStorage   │  <-- Compression Layer
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│  IndexedDBAdapter   │  <-- Core Storage
└──────────┬──────────┘
           │
     ┌─────▼─────┐
     │ IndexedDB │  <-- Browser Database
     └───────────┘
```

## Migration Process

1. **Automatic Migration**: On first load, all localStorage data is copied to IndexedDB
2. **Dual Storage**: Data is kept in both places temporarily
3. **Seamless Fallback**: If IndexedDB fails, localStorage is used
4. **No Data Loss**: All existing documents are preserved

## Next Steps

After running `npm install` to install lz-string:

1. Test the new storage system
2. Monitor compression effectiveness
3. Check storage usage patterns
4. Consider implementing auto-backup reminders

## Storage Limits

- **IndexedDB**: Up to 60% of disk space (often 100GB+)
- **Compression**: Effectively multiplies capacity by 2-5x
- **Example**: 1GB IndexedDB + compression = 2-5GB effective storage

## API Reference

### Using the Storage Hook
```javascript
import { useStorage } from './utils/storage/useStorage';

function MyComponent() {
  const {
    getAllDocuments,
    saveDocument,
    deleteDocument,
    storageInfo,
    formatBytes
  } = useStorage();
  
  // Use storage operations
}
```

### Direct Storage Access
```javascript
import storageWrapper from './utils/storage/storageWrapper';

// Get all documents
const docs = await storageWrapper.getEntries();

// Save documents
await storageWrapper.saveEntries(docs);

// Get storage info
const info = await storageWrapper.getStorageInfo();
```