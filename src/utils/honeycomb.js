/**
 * Honeycomb.io Observability Configuration
 * Provides complete platform health monitoring and error tracking
 * Replaces Sentry with no rate limits and better performance insights
 */

import { HoneycombWebSDK, WebVitalsInstrumentation } from '@honeycombio/opentelemetry-web';
import { getWebAutoInstrumentations } from '@opentelemetry/auto-instrumentations-web';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';

// Configuration based on environment
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

/**
 * Initialize Honeycomb SDK with comprehensive instrumentation
 * This captures:
 * - All API calls (including Supabase)
 * - User interactions (clicks, form submissions)
 * - Page loads and navigation
 * - JavaScript errors
 * - Core Web Vitals (LCP, FID, CLS, etc.)
 * - Custom business metrics
 */
export function initializeHoneycomb() {
  if (!isBrowser) {
    console.log('[Honeycomb] Skipping initialization - not in browser environment');
    return null;
  }

  const apiKey = import.meta.env.VITE_HONEYCOMB_API_KEY;
  
  if (!apiKey) {
    console.warn('[Honeycomb] No API key found. Set VITE_HONEYCOMB_API_KEY in environment variables.');
    return null;
  }

  try {
    const sdk = new HoneycombWebSDK({
      // Your Honeycomb API key
      apiKey: apiKey,
      
      // Service name - shows up in Honeycomb UI
      serviceName: isProduction ? 'devlog' : 'devlog-dev',
      
      // Sampling rate - capture percentage of traces
      // Dev: 100%, Production: 10% (adjust based on traffic)
      sampleRate: isDevelopment ? 1 : 0.1,
      
      // Debug mode - logs to console in development
      debug: isDevelopment,
      
      // Local visualizations - shows trace links in console
      localVisualizations: isDevelopment,
      
      // Custom resource attributes for all traces
      resourceAttributes: {
        'environment': isProduction ? 'production' : 'development',
        'deployment.environment': isProduction ? 'vercel' : 'local',
        'service.version': '1.0.0', // Update with your version
        'browser.user_agent': navigator.userAgent,
        'browser.language': navigator.language,
        'browser.online': navigator.onLine,
        'browser.viewport.width': window.innerWidth,
        'browser.viewport.height': window.innerHeight,
        'device.type': getDeviceType(),
      },
      
      // Instrumentations - what to automatically track
      instrumentations: [
        // Auto-instrumentations for web
        getWebAutoInstrumentations({
          // Capture all fetch/XHR requests
          '@opentelemetry/instrumentation-fetch': {
            propagateTraceHeaderCorsUrls: [
              // Add your API domains here
              /.*supabase\.co.*/,
              /.*devlog\.design.*/,
            ],
            clearTimingResources: true,
            // Add request/response headers as span attributes
            applyCustomAttributesOnSpan: (span, request, response) => {
              span.setAttribute('http.request.body.size', request.headers?.['content-length'] || 0);
              span.setAttribute('http.response.body.size', response?.headers?.get('content-length') || 0);
              
              // Track Supabase-specific headers
              if (request.url?.includes('supabase')) {
                span.setAttribute('supabase.operation', request.headers?.['x-supabase-operation'] || 'unknown');
              }
            },
          },
          
          // User interaction tracking
          '@opentelemetry/instrumentation-user-interaction': {
            eventNames: ['click', 'submit', 'change', 'dblclick'],
            // Add element details to spans
            shouldPreventSpanCreation: (event, element) => {
              // Skip tracking for certain elements
              if (element.classList?.contains('no-track')) {
                return true;
              }
              return false;
            },
          },
          
          // Document load tracking
          '@opentelemetry/instrumentation-document-load': {
            enabled: true,
          },
        }),
        
        // Core Web Vitals instrumentation
        new WebVitalsInstrumentation({
          vitalsToTrack: ['LCP', 'FID', 'CLS', 'FCP', 'TTFB', 'INP'],
        }),
      ],
      
      // Skip certain operations from tracking
      skipOptionsValidation: false,
      
      // Custom span processor for additional processing
      spanProcessors: [],
    });

    // Start the SDK
    sdk.start();
    
    console.log(`[Honeycomb] SDK initialized successfully in ${isProduction ? 'production' : 'development'} mode`);
    
    // Store SDK instance globally for custom instrumentation
    window.__honeycombSDK = sdk;
    
    // Initialize user context storage
    window.__honeycombUserContext = {};
    
    // Add global error handler
    setupGlobalErrorHandler(sdk);
    
    // Track page visibility changes
    trackPageVisibility();
    
    // Track memory usage
    if (isDevelopment) {
      trackMemoryUsage();
    }
    
    return sdk;
  } catch (error) {
    console.error('[Honeycomb] Failed to initialize SDK:', error);
    return null;
  }
}

/**
 * Set up global error handler to catch unhandled errors
 */
function setupGlobalErrorHandler(sdk) {
  window.addEventListener('error', (event) => {
    sdk.recordException(event.error || new Error(event.message), {
      'error.type': 'unhandled_error',
      'error.message': event.message,
      'error.filename': event.filename,
      'error.lineno': event.lineno,
      'error.colno': event.colno,
    });
  });

  window.addEventListener('unhandledrejection', (event) => {
    sdk.recordException(new Error(event.reason), {
      'error.type': 'unhandled_promise_rejection',
      'error.reason': String(event.reason),
    });
  });
}

/**
 * Track page visibility changes
 */
function trackPageVisibility() {
  document.addEventListener('visibilitychange', () => {
    const span = window.__honeycombSDK?.tracer?.startSpan('page.visibility_change');
    if (span) {
      span.setAttributes({
        'page.visibility': document.visibilityState,
        'page.hidden': document.hidden,
      });
      span.end();
    }
  });
}

/**
 * Track memory usage (development only)
 */
function trackMemoryUsage() {
  if (!performance.memory) return;
  
  setInterval(() => {
    const span = window.__honeycombSDK?.tracer?.startSpan('browser.memory_check');
    if (span) {
      span.setAttributes({
        'memory.used_js_heap_size': performance.memory.usedJSHeapSize,
        'memory.total_js_heap_size': performance.memory.totalJSHeapSize,
        'memory.limit_js_heap_size': performance.memory.jsHeapSizeLimit,
        'memory.usage_percentage': (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100,
      });
      span.end();
    }
  }, 30000); // Check every 30 seconds
}

/**
 * Get device type based on viewport
 */
function getDeviceType() {
  const width = window.innerWidth;
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  return 'desktop';
}

/**
 * Create a custom span for tracking operations
 */
export function createSpan(name, attributes = {}) {
  if (!window.__honeycombSDK?.tracer) return null;
  
  try {
    const span = window.__honeycombSDK.tracer.startSpan(name, {
      attributes: {
        'span.kind': 'internal',
        ...(window.__honeycombUserContext || {}), // Include user context if available
        ...attributes,
      },
    });
    
    return span;
  } catch (error) {
    console.warn('[Honeycomb] Could not create span:', error);
    return null;
  }
}

/**
 * Record an exception with context
 */
export function recordException(error, context = {}) {
  if (!window.__honeycombSDK) return;
  
  try {
    // Include user context if available
    const fullContext = {
      ...(window.__honeycombUserContext || {}),
      'error.context': JSON.stringify(context),
      'error.stack': error.stack,
      'error.name': error.name,
      'error.message': error.message,
      ...context,
    };
    
    if (window.__honeycombSDK.recordException) {
      window.__honeycombSDK.recordException(error, fullContext);
    }
  } catch (recordError) {
    console.warn('[Honeycomb] Could not record exception:', recordError);
    // Don't throw - we don't want error reporting to cause more errors
  }
}

/**
 * Add user context to all future traces
 */
export function setUserContext(userId, userEmail, userTier) {
  // Don't crash if SDK isn't initialized
  if (!window.__honeycombSDK) return;
  
  try {
    // Store user context for use in future spans
    // Note: setGlobalAttributes doesn't exist in the Web SDK
    // We store context and include it in each span instead
    window.__honeycombUserContext = {
      'user.id': userId,
      'user.email': userEmail,
      'user.tier': userTier || 'free',
      'session.id': generateSessionId(),
    };
    
    console.log('[Honeycomb] User context set:', userId);
  } catch (error) {
    console.warn('[Honeycomb] Could not set user context:', error);
  }
}

/**
 * Generate a session ID
 */
function generateSessionId() {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Track custom metrics
 */
export function trackMetric(name, value, attributes = {}) {
  const span = createSpan(`metric.${name}`, {
    'metric.name': name,
    'metric.value': value,
    ...attributes,
  });
  
  if (span) {
    span.end();
  }
}

// Export for use in other files
export default {
  initializeHoneycomb,
  createSpan,
  recordException,
  setUserContext,
  trackMetric,
};