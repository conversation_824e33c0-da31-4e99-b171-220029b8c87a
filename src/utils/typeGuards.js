/**
 * @fileoverview Type guard functions for runtime type checking
 * These help ensure type safety when working with Supabase data
 */

/**
 * Check if a value is a valid Block
 * @param {any} value - The value to check
 * @returns {value is import('../types/supabase-helpers').Block}
 */
export function isBlock(value) {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.document_id === 'string' &&
    typeof value.type === 'string' &&
    typeof value.content === 'string' &&
    typeof value.position === 'number' &&
    isValidBlockType(value.type)
  );
}

/**
 * Check if a value is a valid Document
 * @param {any} value - The value to check
 * @returns {value is import('../types/supabase-helpers').Document}
 */
export function isDocument(value) {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.user_id === 'string' &&
    typeof value.title === 'string' &&
    Array.isArray(value.tags) &&
    value.tags.every(tag => typeof tag === 'string')
  );
}

/**
 * Check if a value is a valid Profile
 * @param {any} value - The value to check
 * @returns {value is import('../types/supabase-helpers').Profile}
 */
export function isProfile(value) {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    (value.username === null || typeof value.username === 'string') &&
    (value.full_name === null || typeof value.full_name === 'string') &&
    (value.avatar_url === null || typeof value.avatar_url === 'string')
  );
}

/**
 * Check if a value is a valid DocumentShare
 * @param {any} value - The value to check
 * @returns {value is import('../types/supabase-helpers').DocumentShare}
 */
export function isDocumentShare(value) {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.document_id === 'string' &&
    typeof value.share_code === 'string' &&
    Array.isArray(value.permissions) &&
    value.permissions.every(perm => typeof perm === 'string')
  );
}

/**
 * Check if a value is a valid block type
 * @param {any} type - The block type to check
 * @returns {type is import('../types/supabase-helpers').BLOCK_TYPES[keyof import('../types/supabase-helpers').BLOCK_TYPES]}
 */
export function isValidBlockType(type) {
  const validTypes = [
    'text', 'code', 'heading', 'ai', 'table',
    'filetree', 'todo', 'image', 'inline-image',
    'version-track', 'issue-tracker'
  ];
  return typeof type === 'string' && validTypes.includes(type);
}

/**
 * Check if a value is a valid UUID
 * @param {any} value - The value to check
 * @returns {boolean}
 */
export function isUUID(value) {
  if (typeof value !== 'string') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
}

/**
 * Check if a value is a valid email
 * @param {any} value - The value to check
 * @returns {boolean}
 */
export function isEmail(value) {
  if (typeof value !== 'string') return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Check if a value is a valid URL
 * @param {any} value - The value to check
 * @returns {boolean}
 */
export function isURL(value) {
  if (typeof value !== 'string') return false;
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if a value is a Supabase error
 * @param {any} error - The error to check
 * @returns {error is import('../types/supabase-helpers').SupabaseError}
 */
export function isSupabaseError(error) {
  return (
    error &&
    typeof error === 'object' &&
    typeof error.message === 'string' &&
    typeof error.code === 'string'
  );
}

/**
 * Type guard for array of documents
 * @param {any} value - The value to check
 * @returns {value is import('../types/supabase-helpers').Document[]}
 */
export function isDocumentArray(value) {
  return Array.isArray(value) && value.every(isDocument);
}

/**
 * Type guard for array of blocks
 * @param {any} value - The value to check
 * @returns {value is import('../types/supabase-helpers').Block[]}
 */
export function isBlockArray(value) {
  return Array.isArray(value) && value.every(isBlock);
}

/**
 * Type guard for block metadata
 * @param {any} metadata - The metadata to check
 * @param {string} blockType - The type of block
 * @returns {boolean}
 */
export function isValidBlockMetadata(metadata, blockType) {
  if (!metadata || typeof metadata !== 'object') return true; // Metadata is optional
  
  switch (blockType) {
    case 'code':
      return !metadata.language || typeof metadata.language === 'string';
    
    case 'heading':
      return !metadata.level || (
        typeof metadata.level === 'number' &&
        metadata.level >= 1 &&
        metadata.level <= 6
      );
    
    case 'todo':
      return !metadata.checked || typeof metadata.checked === 'boolean';
    
    case 'image':
    case 'inline-image':
      return !metadata.images || (
        Array.isArray(metadata.images) &&
        metadata.images.every(img => 
          typeof img === 'object' &&
          typeof img.url === 'string'
        )
      );
    
    default:
      return true; // No specific validation for other types
  }
}

/**
 * Safe type assertion with validation
 * @template T
 * @param {any} value - The value to assert
 * @param {(value: any) => value is T} guard - The type guard function
 * @param {string} [errorMessage] - Custom error message
 * @returns {T}
 * @throws {TypeError} If the value doesn't match the type
 */
export function assertType(value, guard, errorMessage) {
  if (!guard(value)) {
    throw new TypeError(errorMessage || 'Type assertion failed');
  }
  return value;
}

/**
 * Safe optional type assertion
 * @template T
 * @param {any} value - The value to assert
 * @param {(value: any) => value is T} guard - The type guard function
 * @returns {T | null}
 */
export function assertOptionalType(value, guard) {
  if (value === null || value === undefined) return null;
  return guard(value) ? value : null;
}