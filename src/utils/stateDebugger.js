/**
 * State Debugger Utility
 * Helps debug state changes and find where state is being used
 */

import { useEffect, useRef } from 'react';

/**
 * Hook to log component renders and props/state changes
 * @param {string} componentName - Name of the component
 * @param {Object} props - Component props
 * @param {Object} state - Component state or store state
 */
export function useRenderLogger(componentName, props = {}, state = {}) {
  const renderCount = useRef(0);
  const previousProps = useRef(props);
  const previousState = useRef(state);
  
  useEffect(() => {
    renderCount.current += 1;
    
    const changedProps = {};
    const changedState = {};
    
    // Find changed props
    Object.keys(props).forEach(key => {
      if (props[key] !== previousProps.current[key]) {
        changedProps[key] = {
          from: previousProps.current[key],
          to: props[key]
        };
      }
    });
    
    // Find changed state
    Object.keys(state).forEach(key => {
      if (state[key] !== previousState.current[key]) {
        changedState[key] = {
          from: previousState.current[key],
          to: state[key]
        };
      }
    });
    
    // Log if there are changes
    if (Object.keys(changedProps).length > 0 || Object.keys(changedState).length > 0) {
      console.group(`🔄 ${componentName} (render #${renderCount.current})`);
      
      if (Object.keys(changedProps).length > 0) {
        console.log('Props changed:', changedProps);
      }
      
      if (Object.keys(changedState).length > 0) {
        console.log('State changed:', changedState);
      }
      
      console.groupEnd();
    }
    
    // Update refs
    previousProps.current = props;
    previousState.current = state;
  });
}

/**
 * Hook to track why a component re-rendered
 * @param {string} componentName - Name of the component
 * @param {Object} deps - Dependencies to track
 */
export function useWhyDidYouUpdate(componentName, deps) {
  const previousDeps = useRef();
  
  useEffect(() => {
    if (previousDeps.current) {
      const allKeys = Object.keys({ ...previousDeps.current, ...deps });
      const changedDeps = {};
      
      allKeys.forEach(key => {
        if (previousDeps.current[key] !== deps[key]) {
          changedDeps[key] = {
            from: previousDeps.current[key],
            to: deps[key]
          };
        }
      });
      
      if (Object.keys(changedDeps).length) {
        console.log(`🤔 ${componentName} updated because of:`, changedDeps);
      }
    }
    
    previousDeps.current = deps;
  });
}

/**
 * State change monitor
 * Logs all state changes with stack traces
 */
export function createStateMonitor(storeName) {
  return (set, get, api) => {
    return {
      ...api,
      setState: (...args) => {
        console.group(`🏪 ${storeName} State Change`);
        console.log('Previous state:', get());
        
        // Get stack trace
        const stack = new Error().stack;
        const caller = stack.split('\n')[3]?.trim();
        console.log('Called from:', caller);
        
        // Apply state change
        set(...args);
        
        console.log('New state:', get());
        console.groupEnd();
      }
    };
  };
}

/**
 * Performance monitor for state selectors
 * Tracks how often selectors are called
 */
export function createSelectorMonitor(selectorName) {
  let callCount = 0;
  let lastCallTime = Date.now();
  
  return (selector) => {
    return (...args) => {
      callCount++;
      const now = Date.now();
      const timeSinceLastCall = now - lastCallTime;
      
      if (timeSinceLastCall < 16) { // Less than one frame
        console.warn(`⚠️ ${selectorName} called ${callCount} times rapidly`);
      }
      
      lastCallTime = now;
      return selector(...args);
    };
  };
}

/**
 * Find which components are using specific state
 * Add this to components to track state usage
 */
export function useStateUsageTracker(componentName, stateKeys = []) {
  useEffect(() => {
    if (!window.__STATE_USAGE__) {
      window.__STATE_USAGE__ = {};
    }
    
    stateKeys.forEach(key => {
      if (!window.__STATE_USAGE__[key]) {
        window.__STATE_USAGE__[key] = new Set();
      }
      window.__STATE_USAGE__[key].add(componentName);
    });
    
    return () => {
      stateKeys.forEach(key => {
        window.__STATE_USAGE__[key]?.delete(componentName);
      });
    };
  }, [componentName, stateKeys]);
}

/**
 * Log all components using specific state
 */
export function logStateUsage() {
  if (!window.__STATE_USAGE__) {
    console.log('No state usage tracked yet');
    return;
  }
  
  console.group('📊 State Usage Report');
  Object.entries(window.__STATE_USAGE__).forEach(([key, components]) => {
    console.log(`${key}: Used by ${components.size} components`, Array.from(components));
  });
  console.groupEnd();
}

// Export for console access
if (process.env.NODE_ENV === 'development') {
  window.__STATE_DEBUG__ = {
    logStateUsage,
    clearUsageTracking: () => { window.__STATE_USAGE__ = {}; }
  };
}