/**
 * System Monitor for AI #2
 * Monitors system health while AI #1 performs block migrations
 */

class SystemMonitor {
  constructor() {
    this.checks = {
      auth: false,
      providers: false,
      errors: false,
      memory: false,
      performance: false
    };
    this.errorCount = 0;
    this.warningCount = 0;
    this.startTime = Date.now();
    this.checkInterval = null;
  }

  checkHealth() {
    console.group('🏥 System Health Check - ' + new Date().toLocaleTimeString());
    
    // Check auth
    try {
      const authState = window.__APP_STATE__?.auth;
      this.checks.auth = !!authState && authState.isInitialized;
      console.log('Auth:', this.checks.auth ? '✅' : '❌', 
        authState ? `(User: ${authState.user?.email || 'none'})` : '');
    } catch (e) {
      this.checks.auth = false;
      console.error('Auth check failed:', e);
    }
    
    // Check providers
    try {
      const providersExist = document.querySelector('[class*="Provider"]');
      this.checks.providers = !!providersExist;
      console.log('Providers:', this.checks.providers ? '✅' : '❌');
    } catch (e) {
      this.checks.providers = false;
      console.error('Provider check failed:', e);
    }
    
    // Check for errors
    try {
      const currentErrors = window.errorChecker?.getReport?.() || { errorCount: 0, warningCount: 0 };
      this.checks.errors = currentErrors.errorCount === 0;
      console.log('Errors:', this.checks.errors ? '✅' : '❌', 
        `(${currentErrors.errorCount} errors, ${currentErrors.warningCount} warnings)`);
      
      // Track error trends
      if (currentErrors.errorCount > this.errorCount) {
        console.warn('⚠️ New errors detected!', currentErrors.errors?.slice(-1));
      }
      this.errorCount = currentErrors.errorCount;
      this.warningCount = currentErrors.warningCount;
    } catch (e) {
      console.log('Error checker not available');
    }
    
    // Check memory
    try {
      if (performance.memory) {
        const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit;
        this.checks.memory = memoryUsage < 0.9;
        console.log('Memory:', this.checks.memory ? '✅' : '❌', 
          `(${Math.round(memoryUsage * 100)}% used - ${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB)`);
        
        if (memoryUsage > 0.8) {
          console.warn('⚠️ Memory usage high!');
        }
      } else {
        console.log('Memory: N/A (not available in this browser)');
        this.checks.memory = true; // Assume OK if can't check
      }
    } catch (e) {
      this.checks.memory = true;
      console.error('Memory check failed:', e);
    }
    
    // Check performance
    try {
      // Check if any long tasks are blocking
      if (window.PerformanceObserver) {
        this.checks.performance = true;
        console.log('Performance:', this.checks.performance ? '✅' : '❌');
      }
    } catch (e) {
      this.checks.performance = true;
    }
    
    // Overall status
    const allChecks = Object.values(this.checks);
    const passedChecks = allChecks.filter(c => c).length;
    const status = passedChecks === allChecks.length ? '✅ HEALTHY' : 
                   passedChecks >= 3 ? '⚠️ DEGRADED' : '❌ CRITICAL';
    
    console.log('\n📊 Overall Status:', status, `(${passedChecks}/${allChecks.length} checks passed)`);
    console.log('⏱️ Uptime:', Math.round((Date.now() - this.startTime) / 1000 / 60), 'minutes');
    
    console.groupEnd();
    
    return {
      status,
      checks: this.checks,
      errorCount: this.errorCount,
      warningCount: this.warningCount,
      uptime: Date.now() - this.startTime
    };
  }

  startMonitoring(intervalMs = 60000) {
    console.log('🚀 Starting System Monitor (AI #2)');
    console.log('Monitoring interval:', intervalMs / 1000, 'seconds');
    
    // Initial check
    this.checkHealth();
    
    // Set up error tracking if not already set up
    if (!window.errorChecker) {
      this.setupErrorTracking();
    }
    
    // Regular checks
    this.checkInterval = setInterval(() => {
      this.checkHealth();
    }, intervalMs);
    
    // Listen for critical events
    this.setupEventListeners();
    
    return this;
  }

  setupErrorTracking() {
    window.errorChecker = {
      errors: [],
      warnings: [],
      
      startMonitoring() {
        // Override console.error
        const originalError = console.error;
        console.error = (...args) => {
          this.errors.push({
            message: args.join(' '),
            timestamp: new Date().toISOString(),
            stack: new Error().stack
          });
          originalError.apply(console, args);
        };
        
        // Override console.warn
        const originalWarn = console.warn;
        console.warn = (...args) => {
          this.warnings.push({
            message: args.join(' '),
            timestamp: new Date().toISOString()
          });
          originalWarn.apply(console, args);
        };
        
        // Monitor unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
          this.errors.push({
            message: `Unhandled Promise: ${event.reason}`,
            timestamp: new Date().toISOString(),
            promise: event.promise
          });
        });
      },
      
      getReport() {
        return {
          errorCount: this.errors.length,
          warningCount: this.warnings.length,
          errors: this.errors,
          warnings: this.warnings,
          critical: this.errors.filter(e => 
            e.message.includes('Cannot read') || 
            e.message.includes('undefined') ||
            e.message.includes('null')
          )
        };
      },
      
      clear() {
        this.errors = [];
        this.warnings = [];
      }
    };
    
    window.errorChecker.startMonitoring();
  }

  setupEventListeners() {
    // Monitor visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        console.log('👁️ Page became visible, running health check...');
        this.checkHealth();
      }
    });
    
    // Monitor online/offline
    window.addEventListener('online', () => {
      console.log('🌐 Back online');
      this.checkHealth();
    });
    
    window.addEventListener('offline', () => {
      console.warn('🔌 Gone offline');
    });
  }

  stopMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('🛑 System Monitor stopped');
    }
  }

  // Quick status for AI coordination
  getQuickStatus() {
    const allChecks = Object.values(this.checks);
    const passedChecks = allChecks.filter(c => c).length;
    return {
      healthy: passedChecks === allChecks.length,
      checksPass: `${passedChecks}/${allChecks.length}`,
      errors: this.errorCount,
      warnings: this.warningCount,
      uptime: Math.round((Date.now() - this.startTime) / 1000 / 60) + ' min'
    };
  }
}

// Create and export singleton instance
const systemMonitor = new SystemMonitor();

// Auto-start in development
if (import.meta.env.DEV) {
  systemMonitor.startMonitoring(30000); // Check every 30 seconds in dev
}

// Make available globally for debugging
window.systemMonitor = systemMonitor;

export default systemMonitor;