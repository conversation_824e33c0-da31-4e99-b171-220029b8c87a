/**
 * Environment utilities
 * Provides a consistent way to check environment across the app
 */

// Use Vite's import.meta.env for reliable environment detection
export const isDev = import.meta.env.DEV;
export const isProd = import.meta.env.PROD;
export const mode = import.meta.env.MODE;

// For backward compatibility with process.env checks
export const isNodeEnvDevelopment = import.meta.env.DEV;
export const isNodeEnvProduction = import.meta.env.PROD;

// Helper to safely check environment
export function isProduction() {
  return import.meta.env.PROD;
}

export function isDevelopment() {
  return import.meta.env.DEV;
}

// Export a process.env compatible object for gradual migration
export const processEnv = {
  NODE_ENV: import.meta.env.MODE,
  CLIENT_URL: import.meta.env.VITE_CLIENT_URL || window.location.origin,
  FRONTEND_URL: import.meta.env.VITE_FRONTEND_URL || window.location.origin
};