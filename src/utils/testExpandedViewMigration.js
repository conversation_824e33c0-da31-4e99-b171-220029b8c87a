/**
 * Test script for ExpandedView migration
 * Counts useState calls and verifies migration
 */

export const testExpandedViewMigration = {
  // Count useState calls in the original file
  async countOriginalUseState() {
    console.group('📊 Counting useState in ExpandedViewEnhanced.jsx');
    
    try {
      const response = await fetch('/src/components/ExpandedViewEnhanced.jsx');
      const content = await response.text();
      
      // Count useState occurrences
      const useStateMatches = content.match(/useState\(/g) || [];
      const count = useStateMatches.length;
      
      console.log(`Found ${count} useState calls`);
      
      // List all useState declarations
      const lines = content.split('\n');
      const useStateLines = lines
        .map((line, index) => ({ line, number: index + 1 }))
        .filter(({ line }) => line.includes('useState('))
        .map(({ line, number }) => {
          const trimmed = line.trim();
          const match = trimmed.match(/const \[(\w+),.*?\] = useState/);
          return {
            lineNumber: number,
            stateName: match ? match[1] : 'unknown',
            line: trimmed
          };
        });
      
      console.table(useStateLines);
      console.groupEnd();
      
      return { count, states: useStateLines };
    } catch (error) {
      console.error('Failed to count useState:', error);
      console.groupEnd();
      return { count: 0, states: [] };
    }
  },
  
  // Check if the new hook is working
  checkHookAvailability() {
    console.group('🔍 Checking useExpandedView Hook');
    
    const checks = {
      editorStore: !!window.__APP_STATE__?.editor,
      hasDocumentTitle: window.__APP_STATE__?.editor?.hasOwnProperty('documentTitle'),
      hasDocumentTags: window.__APP_STATE__?.editor?.hasOwnProperty('documentTags'),
      hasShowBlockSelector: window.__APP_STATE__?.editor?.hasOwnProperty('showBlockSelector'),
      hasFocusedBlockId: window.__APP_STATE__?.editor?.hasOwnProperty('focusedBlockId'),
      hasViewMode: window.__APP_STATE__?.editor?.hasOwnProperty('viewMode'),
      hasSaveStatus: window.__APP_STATE__?.editor?.hasOwnProperty('saveStatus'),
    };
    
    console.table(checks);
    
    const allChecks = Object.values(checks).every(check => check === true);
    console.log(allChecks ? '✅ Editor store properly updated' : '❌ Editor store missing properties');
    
    console.groupEnd();
    return allChecks;
  },
  
  // Verify all state is accessible
  verifyStateMigration() {
    console.group('🔍 Verifying State Migration');
    
    const editorStore = window.__APP_STATE__?.editor;
    const uiStore = window.__APP_STATE__?.ui;
    
    if (!editorStore || !uiStore) {
      console.error('❌ Stores not found');
      console.groupEnd();
      return false;
    }
    
    // List of all states that should be migrated
    const statesToCheck = [
      // Document state
      { store: 'editor', property: 'documentTitle', originalName: 'title' },
      { store: 'editor', property: 'documentTags', originalName: 'tags' },
      { store: 'editor', property: 'backlinks', originalName: 'backlinks' },
      
      // Block selector
      { store: 'editor', property: 'showBlockSelector', originalName: 'showBlockSelector' },
      { store: 'editor', property: 'selectorPosition', originalName: 'selectorPosition' },
      
      // Title editing
      { store: 'editor', property: 'isEditingTitle', originalName: 'isEditingTitle' },
      
      // Block focus
      { store: 'editor', property: 'focusedBlockId', originalName: 'focusedBlockId' },
      { store: 'editor', property: 'hoveredBlockId', originalName: 'hoveredBlockId' },
      
      // Tag editing
      { store: 'editor', property: 'isAddingTag', originalName: 'isAddingTag' },
      { store: 'editor', property: 'newTag', originalName: 'newTag' },
      { store: 'editor', property: 'editingTagIndex', originalName: 'editingTagIndex' },
      { store: 'editor', property: 'editingTagValue', originalName: 'editingTagValue' },
      
      // Drag and drop
      { store: 'editor', property: 'draggedBlockId', originalName: 'draggedBlockId' },
      { store: 'editor', property: 'dropTargetId', originalName: 'dropTargetId' },
      { store: 'editor', property: 'dropPosition', originalName: 'dropPosition' },
      
      // View mode
      { store: 'editor', property: 'viewMode', originalName: 'viewMode' },
      { store: 'editor', property: 'selectedLineBlockId', originalName: 'selectedLineBlockId' },
      { store: 'editor', property: 'linesScrollProgress', originalName: 'linesScrollProgress' },
      
      // Save state
      { store: 'editor', property: 'saveStatus', originalName: 'saveStatus' },
      { store: 'editor', property: 'isInternalUpdate', originalName: 'isInternalUpdate' },
      
      // UI state
      { store: 'ui', property: 'modals.deleteDocument', originalName: 'showDeleteConfirm' },
      { store: 'ui', property: 'modals.shareDocument', originalName: 'showShareDialog' },
      { store: 'ui', property: 'loadingStates.deleteDocument', originalName: 'isDeleting' },
    ];
    
    const results = statesToCheck.map(({ store, property, originalName }) => {
      const storeObj = store === 'editor' ? editorStore : uiStore;
      let value;
      
      if (property.includes('.')) {
        const [parent, child] = property.split('.');
        value = storeObj[parent]?.[child];
      } else {
        value = storeObj[property];
      }
      
      return {
        originalName,
        newLocation: `${store}.${property}`,
        exists: value !== undefined,
        currentValue: value
      };
    });
    
    console.table(results);
    
    const allMigrated = results.every(r => r.exists);
    console.log(allMigrated ? '✅ All states migrated successfully' : '❌ Some states missing');
    
    console.groupEnd();
    return results;
  },
  
  // Run all tests
  async runAllTests() {
    console.log('🏃 Running ExpandedView Migration Tests...');
    console.log('=====================================');
    
    const results = {
      originalCount: await this.countOriginalUseState(),
      hookAvailable: this.checkHookAvailability(),
      stateMigration: this.verifyStateMigration()
    };
    
    console.log('\n=====================================');
    console.log('📊 Migration Summary:');
    console.log(`Original useState calls: ${results.originalCount.count}`);
    console.log(`Target useState calls: 0`);
    console.log(`Reduction: 100% (${results.originalCount.count} eliminated)`);
    
    if (results.hookAvailable && results.stateMigration) {
      console.log('\n✅ ExpandedView migration ready!');
      console.log('\nNext steps:');
      console.log('1. Update ExpandedViewEnhanced.jsx to use useExpandedView hook');
      console.log('2. Replace all 27 useState calls with hook values');
      console.log('3. Test all functionality still works');
    } else {
      console.log('\n❌ Migration incomplete - check failed tests above');
    }
    
    return results;
  }
};

// Make available globally for testing
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.testExpandedViewMigration = testExpandedViewMigration;
  console.log('🧪 ExpandedView migration tests loaded. Run: window.testExpandedViewMigration.runAllTests()');
}