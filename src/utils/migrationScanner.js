/**
 * Migration Scanner Utility
 * Helps track remaining useState calls and prioritize migration
 */

export const migrationScanner = {
  // Track migration progress
  scanResults: null,
  
  /**
   * Scan for useState usage in components
   * @returns {Promise<Object>} Scan results
   */
  async scanComponents() {
    console.group('🔍 Scanning for useState usage...');
    
    try {
      // In a real implementation, this would scan the file system
      // For now, we'll use our known data
      const results = {
        totalFiles: 120,
        totalUseStateCalls: 639,
        migrated: {
          Dashboard: { before: 20, after: 0, status: '✅' },
          ExpandedViewEnhanced: { before: 24, after: 0, status: '✅' },
          AuthFormElite: { before: 15, after: 3, status: '✅' },
          Settings: { before: 8, after: 0, status: '✅' },
          Sidebar: { before: 5, after: 0, status: '✅' },
          AuthContext: { before: 10, after: 0, status: '✅' }
        },
        highPriority: [
          { component: 'TextBlock', useStateCount: 9, complexity: 'medium', store: 'blockEditorStore' },
          { component: 'CodeBlock', useStateCount: 12, complexity: 'medium', store: 'blockEditorStore' },
          { component: 'TableBlock', useStateCount: 8, complexity: 'medium', store: 'blockEditorStore' },
          { component: 'HeadingBlock', useStateCount: 5, complexity: 'low', store: 'blockEditorStore' }
        ],
        mediumPriority: [
          { component: 'ShareDialog variants', useStateCount: 45, complexity: 'high', store: 'shareStore (new)' },
          { component: 'FileTreeBlock', useStateCount: 13, complexity: 'high', store: 'blockEditorStore' },
          { component: 'ResponsiveCodeBlock', useStateCount: 13, complexity: 'high', store: 'blockEditorStore' }
        ],
        lowPriority: [
          { component: 'VersionTrackBlock', useStateCount: 28, complexity: 'very high', store: 'defer' },
          { component: 'IssueTrackerBlock', useStateCount: 17, complexity: 'very high', store: 'defer' },
          { component: 'Demo components', useStateCount: 80, complexity: 'low', store: 'demoStore (future)' },
          { component: 'Animation components', useStateCount: 60, complexity: 'low', store: 'local state' }
        ],
        localStateOnly: [
          { component: 'CircularProgress', useStateCount: 2, reason: 'Animation state' },
          { component: 'HoverEffects', useStateCount: 15, reason: 'Pure UI state' },
          { component: 'Tooltips', useStateCount: 10, reason: 'Temporary UI state' }
        ]
      };
      
      // Calculate totals
      const migratedTotal = Object.values(results.migrated)
        .reduce((sum, item) => sum + item.before, 0);
      
      const remainingTotal = results.totalUseStateCalls - migratedTotal;
      
      results.summary = {
        totalMigrated: migratedTotal,
        totalRemaining: remainingTotal,
        percentComplete: Math.round((migratedTotal / results.totalUseStateCalls) * 100),
        estimatedEffort: {
          highPriority: '1-2 weeks',
          mediumPriority: '2-3 weeks',
          lowPriority: '3-4 weeks',
          total: '6-8 weeks'
        }
      };
      
      this.scanResults = results;
      console.table(results.summary);
      console.groupEnd();
      
      return results;
    } catch (error) {
      console.error('Scan failed:', error);
      console.groupEnd();
      return null;
    }
  },
  
  /**
   * Get migration recommendations
   * @returns {Object} Recommendations
   */
  getRecommendations() {
    if (!this.scanResults) {
      console.log('Run scanComponents() first');
      return null;
    }
    
    console.group('📋 Migration Recommendations');
    
    const recommendations = {
      immediate: [
        'Complete TextBlock migration as pattern example',
        'Apply same pattern to CodeBlock and TableBlock',
        'Document the migration pattern for team'
      ],
      thisWeek: [
        'Migrate all block components using blockEditorStore',
        'Create shareStore for dialog components',
        'Update migration dashboard with real numbers'
      ],
      nextWeek: [
        'Tackle ShareDialog variants (create shareStore first)',
        'Migrate FileTreeBlock and ResponsiveCodeBlock',
        'Begin planning for complex components'
      ],
      future: [
        'VersionTrackBlock needs dedicated design session',
        'IssueTrackerBlock might need its own store',
        'Demo components can wait (low business impact)'
      ]
    };
    
    console.log('Immediate actions:');
    recommendations.immediate.forEach(action => console.log(`  ✓ ${action}`));
    
    console.log('\nThis week:');
    recommendations.thisWeek.forEach(action => console.log(`  → ${action}`));
    
    console.log('\nNext week:');
    recommendations.nextWeek.forEach(action => console.log(`  ⏱ ${action}`));
    
    console.log('\nFuture:');
    recommendations.future.forEach(action => console.log(`  📅 ${action}`));
    
    console.groupEnd();
    return recommendations;
  },
  
  /**
   * Generate migration report
   * @returns {string} Markdown report
   */
  generateReport() {
    if (!this.scanResults) {
      this.scanComponents();
    }
    
    const report = `
# State Migration Report
Generated: ${new Date().toLocaleDateString()}

## Summary
- **Total useState calls**: ${this.scanResults.totalUseStateCalls}
- **Migrated**: ${this.scanResults.summary.totalMigrated}
- **Remaining**: ${this.scanResults.summary.totalRemaining}
- **Progress**: ${this.scanResults.summary.percentComplete}%

## Completed Migrations
${Object.entries(this.scanResults.migrated)
  .map(([name, data]) => `- ${name}: ${data.before} → ${data.after} useState ${data.status}`)
  .join('\n')}

## Priority Queue

### High Priority (Next Sprint)
${this.scanResults.highPriority
  .map(item => `- ${item.component}: ${item.useStateCount} useState → ${item.store}`)
  .join('\n')}

### Medium Priority (Next Month)
${this.scanResults.mediumPriority
  .map(item => `- ${item.component}: ${item.useStateCount} useState → ${item.store}`)
  .join('\n')}

### Low Priority (Future)
${this.scanResults.lowPriority
  .map(item => `- ${item.component}: ${item.useStateCount} useState → ${item.store}`)
  .join('\n')}

## Estimated Timeline
- High Priority: ${this.scanResults.summary.estimatedEffort.highPriority}
- Medium Priority: ${this.scanResults.summary.estimatedEffort.mediumPriority}
- Low Priority: ${this.scanResults.summary.estimatedEffort.lowPriority}
- **Total: ${this.scanResults.summary.estimatedEffort.total}**
`;
    
    console.log(report);
    return report;
  },
  
  /**
   * Check specific component
   * @param {string} componentName - Component to check
   */
  checkComponent(componentName) {
    console.group(`🔍 Checking ${componentName}`);
    
    // Simulated check - in real implementation would analyze the file
    const mockData = {
      TextBlock: {
        path: 'src/components/blocks/TextBlock.jsx',
        useStateCalls: 9,
        states: [
          'isEditing', 'content', 'slashHint', 'slashHintPosition',
          'showToolbar', 'toolbarPosition', 'selectedText', 
          'isCollapsed', 'uploadingImages'
        ],
        recommendation: 'Use blockEditorStore with useBlockEditor hook'
      }
    };
    
    const data = mockData[componentName] || {
      path: 'unknown',
      useStateCalls: 0,
      states: [],
      recommendation: 'Component not found in mock data'
    };
    
    console.log('Path:', data.path);
    console.log('useState calls:', data.useStateCalls);
    console.log('States:', data.states);
    console.log('Recommendation:', data.recommendation);
    
    console.groupEnd();
    return data;
  }
};

// Make available globally for testing
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.migrationScanner = migrationScanner;
  console.log('🔍 Migration scanner loaded. Commands:');
  console.log('- migrationScanner.scanComponents()');
  console.log('- migrationScanner.getRecommendations()');
  console.log('- migrationScanner.generateReport()');
  console.log('- migrationScanner.checkComponent("TextBlock")');
}