/**
 * Quick test runner for ExpandedView migration
 */

console.log('🧪 Testing ExpandedView Migration...');
console.log('=====================================');

// Check if test is available
if (window.testExpandedViewMigration) {
  window.testExpandedViewMigration.runAllTests().then(results => {
    console.log('\n✅ Test complete! Check results above.');
  });
} else {
  console.error('❌ Test not loaded. Make sure you are in development mode.');
}

// Also check the editor store state
if (window.__APP_STATE__?.editor) {
  console.log('\n📊 Editor Store State:');
  console.table({
    documentTitle: window.__APP_STATE__.editor.documentTitle,
    documentTags: window.__APP_STATE__.editor.documentTags,
    showBlockSelector: window.__APP_STATE__.editor.showBlockSelector,
    viewMode: window.__APP_STATE__.editor.viewMode,
    saveStatus: window.__APP_STATE__.editor.saveStatus
  });
}