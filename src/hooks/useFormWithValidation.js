/**
 * @fileoverview Custom hook for form handling with Zod validation
 * Integrates react-hook-form with Zod schemas and Sentry error tracking
 */

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useState } from 'react';

/**
 * Custom hook that combines react-hook-form with Zod validation
 * @param {import('zod').ZodSchema} schema - Zod schema for validation
 * @param {Object} options - react-hook-form options
 * @param {Object} options.defaultValues - Default form values
 * @param {string} options.mode - Validation mode (onBlur, onChange, onSubmit)
 * @param {Function} options.onSubmit - Form submission handler
 * @returns {Object} Form methods and utilities
 */
export function useFormWithValidation(schema, options = {}) {
  const {
    onSubmit: externalOnSubmit,
    ...formOptions
  } = options;

  // Initialize react-hook-form with Zod resolver
  const form = useForm({
    resolver: zodResolver(schema),
    mode: 'onBlur', // Validate on blur by default for better UX
    reValidateMode: 'onChange', // Re-validate on change after error
    ...formOptions
  });

  const {
    handleSubmit: originalHandleSubmit,
    setError,
    formState: { errors, isSubmitting }
  } = form;
  
  // Success state management
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Track form errors in console during development
  useEffect(() => {
    if (Object.keys(errors).length > 0 && import.meta.env.DEV) {
      console.warn('Form validation errors:', errors);
    }
  }, [errors]);

  /**
   * Enhanced submit handler with error tracking
   */
  const handleSubmit = useCallback((onSubmit) => {
    return originalHandleSubmit(async (data) => {
      try {
        // Reset success state before submission
        setSubmitSuccess(false);
        setSuccessMessage('');
        
        // Call the provided submit handler
        const result = await onSubmit(data);
        
        // Set success state
        setSubmitSuccess(true);
        if (typeof result === 'string') {
          setSuccessMessage(result);
        } else if (result?.message) {
          setSuccessMessage(result.message);
        } else {
          setSuccessMessage('Successfully submitted!');
        }
        
        // Auto-hide success after 3 seconds
        setTimeout(() => {
          setSubmitSuccess(false);
          setSuccessMessage('');
        }, 3000);
        
        return result;
      } catch (error) {
        // Track form submission errors in Sentry
        if (window.Sentry) {
          window.Sentry.captureException(error, {
            tags: {
              form: 'form-submission',
              component: 'useFormWithValidation'
            },
            extra: {
              formData: data,
              validationErrors: errors
            }
          });
        }

        // Handle Supabase-specific errors
        if (error?.code === 'auth/user-not-found') {
          setError('email', {
            type: 'manual',
            message: 'No account found with this email'
          });
        } else if (error?.code === 'auth/wrong-password') {
          setError('password', {
            type: 'manual',
            message: 'Incorrect password'
          });
        } else if (error?.code === '23505') {
          // Postgres unique constraint violation
          setError('root', {
            type: 'manual',
            message: 'This value already exists'
          });
        } else if (error?.message) {
          // Generic error handling
          setError('root', {
            type: 'manual',
            message: error.message
          });
        } else {
          setError('root', {
            type: 'manual',
            message: 'An unexpected error occurred. Please try again.'
          });
        }

        // Re-throw to allow component-level handling if needed
        throw error;
      }
    });
  }, [originalHandleSubmit, errors, setError, setSubmitSuccess, setSuccessMessage]);

  /**
   * Helper to check if a field has an error
   */
  const hasError = useCallback((fieldName) => {
    return !!errors[fieldName];
  }, [errors]);

  /**
   * Helper to get error message for a field
   */
  const getError = useCallback((fieldName) => {
    return errors[fieldName]?.message;
  }, [errors]);

  /**
   * Reset form with optional new values
   */
  const resetForm = useCallback((values) => {
    form.reset(values);
  }, [form]);

  /**
   * Set multiple errors at once
   */
  const setErrors = useCallback((errorMap) => {
    Object.entries(errorMap).forEach(([field, message]) => {
      setError(field, {
        type: 'manual',
        message
      });
    });
  }, [setError]);

  return {
    ...form,
    handleSubmit,
    hasError,
    getError,
    resetForm,
    setErrors,
    isSubmitting,
    // Success state
    submitSuccess,
    successMessage,
    setSuccessMessage,
    // Expose commonly used methods directly
    register: form.register,
    watch: form.watch,
    setValue: form.setValue,
    errors: form.formState.errors,
    isValid: form.formState.isValid,
    isDirty: form.formState.isDirty
  };
}

/**
 * Hook for field-level validation state
 * @param {Object} form - Form object from useFormWithValidation
 * @param {string} name - Field name
 * @returns {Object} Field state and helpers
 */
export function useFieldState(form, name) {
  const error = form.errors[name];
  const value = form.watch(name);
  const isDirty = form.formState.dirtyFields[name];
  const isTouched = form.formState.touchedFields[name];

  return {
    value,
    error: error?.message,
    hasError: !!error,
    isDirty,
    isTouched,
    isValid: !error && isTouched
  };
}