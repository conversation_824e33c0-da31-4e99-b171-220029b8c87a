import { useEffect } from 'react';
import useSettingsStore from '../stores/settingsStore';
import useAuthStore from '../stores/authStore';

/**
 * Compatibility hook for SettingsContext migration
 * Drop-in replacement for useContext(SettingsContext)
 * 
 * @returns {Object} Settings state and methods matching original SettingsContext
 */
export function useSettings() {
  const user = useAuthStore((state) => state.user);
  const {
    settings,
    isLoading,
    updateSetting,
    updateSettings,
    resetSettings,
    initializeSettings
  } = useSettingsStore();
  
  // Initialize settings when user changes
  useEffect(() => {
    initializeSettings(user);
  }, [user, initializeSettings]);
  
  // Return API matching original SettingsContext
  return {
    settings,
    isLoading,
    updateSetting: (key, value) => updateSetting(key, value, user),
    updateSettings: (updates) => updateSettings(updates, user),
    resetSettings
  };
}

// Additional hooks for specific settings (matching potential usage patterns)
export const useAutoSaveInterval = () => useSettingsStore((state) => state.settings.autoSaveInterval);
export const useDefaultCodeLanguage = () => useSettingsStore((state) => state.settings.defaultCodeLanguage);
export const useShowLineNumbers = () => useSettingsStore((state) => state.settings.showLineNumbers);
export const useEnableTextCollapse = () => useSettingsStore((state) => state.settings.enableTextCollapse);
export const useSessionTimeout = () => useSettingsStore((state) => state.settings.sessionTimeout);