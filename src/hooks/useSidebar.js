import { useEffect } from 'react';
import useUIStore from '../stores/uiStore';

/**
 * Compatibility hook for SidebarContext migration
 * Drop-in replacement for useContext(SidebarContext)
 * 
 * @returns {Object} Sidebar state and methods matching original SidebarContext
 */
export function useSidebar() {
  const {
    sidebarOpen,
    setSidebarOpen,
    toggleSidebar,
    mobileMenuOpen,
    toggleMobileMenu,
    setMobileMenuOpen
  } = useUIStore();
  
  // Initialize from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    if (saved !== null) {
      setSidebarOpen(saved !== 'true');
    }
  }, [setSidebarOpen]);
  
  // Save to localStorage when sidebar state changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', (!sidebarOpen).toString());
  }, [sidebarOpen]);
  
  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Cmd/Ctrl + B to toggle sidebar
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        toggleSidebar();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleSidebar]);
  
  // Return API matching original SidebarContext
  return {
    isCollapsed: !sidebarOpen,
    setIsCollapsed: (collapsed) => setSidebarOpen(!collapsed),
    toggleCollapsed: toggleSidebar,
    showMobileSidebar: mobileMenuOpen,
    toggleMobileSidebar: toggleMobileMenu,
    closeMobileSidebar: () => setMobileMenuOpen(false)
  };
}

// Export from here for convenience
export { useSidebar as default };