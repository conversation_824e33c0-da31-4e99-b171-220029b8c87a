/**
 * Custom hook to manage ExpandedView state
 * Consolidates 27 useState calls into Zustand stores
 */

import { useCallback, useEffect } from 'react';
import useEditorStore from '../stores/editorStore';
import useUIStore from '../stores/uiStore';
import useDocumentStore from '../stores/documentStore';

export function useExpandedView(documentId) {
  const editorStore = useEditorStore();
  const uiStore = useUIStore();
  const documentStore = useDocumentStore();

  // Get document from store
  const document = documentStore.documents.find(doc => doc.id === documentId);

  // Initialize editor state for this document
  useEffect(() => {
    if (document) {
      editorStore.setActiveDocument(documentId);
      // Initialize document-specific state
      editorStore.setDocumentTitle(document.title);
      editorStore.setDocumentTags(document.tags || []);
    }
    
    return () => {
      // Cleanup on unmount
      editorStore.clearActiveDocument();
    };
  }, [documentId, document]);

  // Block selector state
  const showBlockSelector = editorStore.showBlockSelector;
  const setShowBlockSelector = editorStore.setShowBlockSelector;
  const selectorPosition = editorStore.selectorPosition;
  const setSelectorPosition = editorStore.setSelectorPosition;

  // Title editing
  const title = editorStore.documentTitle || document?.title || '';
  const setTitle = editorStore.setDocumentTitle;
  const isEditingTitle = editorStore.isEditingTitle;
  const setIsEditingTitle = editorStore.setIsEditingTitle;

  // Document links
  const backlinks = editorStore.backlinks;
  const setBacklinks = editorStore.setBacklinks;

  // Block focus
  const focusedBlockId = editorStore.focusedBlockId;
  const setFocusedBlockId = editorStore.setFocusedBlockId;

  // Tags
  const tags = editorStore.documentTags || document?.tags || [];
  const setTags = editorStore.setDocumentTags;
  const isAddingTag = editorStore.isAddingTag;
  const setIsAddingTag = editorStore.setIsAddingTag;
  const newTag = editorStore.newTag;
  const setNewTag = editorStore.setNewTag;
  const editingTagIndex = editorStore.editingTagIndex;
  const setEditingTagIndex = editorStore.setEditingTagIndex;
  const editingTagValue = editorStore.editingTagValue;
  const setEditingTagValue = editorStore.setEditingTagValue;

  // Drag and drop
  const draggedBlockId = editorStore.draggedBlockId;
  const setDraggedBlockId = editorStore.setDraggedBlockId;
  const dropTargetId = editorStore.dropTargetId;
  const setDropTargetId = editorStore.setDropTargetId;
  const dropPosition = editorStore.dropPosition;
  const setDropPosition = editorStore.setDropPosition;

  // Internal updates tracking
  const isInternalUpdate = editorStore.isInternalUpdate;
  const setIsInternalUpdate = editorStore.setIsInternalUpdate;

  // View mode
  const viewMode = editorStore.viewMode;
  const setViewMode = editorStore.setViewMode;
  const selectedLineBlockId = editorStore.selectedLineBlockId;
  const setSelectedLineBlockId = editorStore.setSelectedLineBlockId;
  const linesScrollProgress = editorStore.linesScrollProgress;
  const setLinesScrollProgress = editorStore.setLinesScrollProgress;

  // UI states
  const showDeleteConfirm = uiStore.modals.deleteDocument;
  const setShowDeleteConfirm = useCallback((show) => {
    uiStore.setModal('deleteDocument', show);
  }, []);
  
  const isDeleting = uiStore.loading.deleteDocument || false;
  const setIsDeleting = useCallback((deleting) => {
    uiStore.setLoading('deleteDocument', deleting);
  }, []);
  
  const hoveredBlockId = editorStore.hoveredBlockId;
  const setHoveredBlockId = editorStore.setHoveredBlockId;

  const showShareDialog = uiStore.modals.shareDocument;
  const setShowShareDialog = useCallback((show) => {
    uiStore.setModal('shareDocument', show);
  }, []);

  const saveStatus = editorStore.saveStatus;
  const setSaveStatus = editorStore.setSaveStatus;

  // Helper functions
  const handleTitleUpdate = useCallback(async (newTitle) => {
    setTitle(newTitle);
    // Update in document store as well
    await documentStore.updateDocument(documentId, { title: newTitle });
  }, [documentId, setTitle, documentStore.updateDocument]);

  const handleTagsUpdate = useCallback(async (newTags) => {
    setTags(newTags);
    // Update in document store as well
    await documentStore.updateDocument(documentId, { tags: newTags });
  }, [documentId, setTags, documentStore.updateDocument]);

  const addTag = useCallback((tag) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      handleTagsUpdate([...tags, trimmedTag]);
      setNewTag('');
      setIsAddingTag(false);
    }
  }, [tags, handleTagsUpdate, setNewTag, setIsAddingTag]);

  const removeTag = useCallback((indexToRemove) => {
    handleTagsUpdate(tags.filter((_, index) => index !== indexToRemove));
  }, [tags, handleTagsUpdate]);

  const updateTag = useCallback((index, newValue) => {
    const updatedTags = [...tags];
    updatedTags[index] = newValue;
    handleTagsUpdate(updatedTags);
    setEditingTagIndex(null);
    setEditingTagValue('');
  }, [tags, handleTagsUpdate, setEditingTagIndex, setEditingTagValue]);

  return {
    // Document data
    document,
    title,
    setTitle: handleTitleUpdate,
    tags,
    setTags: handleTagsUpdate,
    
    // Block selector
    showBlockSelector,
    setShowBlockSelector,
    selectorPosition,
    setSelectorPosition,
    
    // Title editing
    isEditingTitle,
    setIsEditingTitle,
    
    // Links
    backlinks,
    setBacklinks,
    
    // Block focus
    focusedBlockId,
    setFocusedBlockId,
    hoveredBlockId,
    setHoveredBlockId,
    
    // Tag editing
    isAddingTag,
    setIsAddingTag,
    newTag,
    setNewTag,
    editingTagIndex,
    setEditingTagIndex,
    editingTagValue,
    setEditingTagValue,
    addTag,
    removeTag,
    updateTag,
    
    // Drag and drop
    draggedBlockId,
    setDraggedBlockId,
    dropTargetId,
    setDropTargetId,
    dropPosition,
    setDropPosition,
    
    // View modes
    viewMode,
    setViewMode,
    selectedLineBlockId,
    setSelectedLineBlockId,
    linesScrollProgress,
    setLinesScrollProgress,
    
    // UI states
    showDeleteConfirm,
    setShowDeleteConfirm,
    isDeleting,
    setIsDeleting,
    showShareDialog,
    setShowShareDialog,
    
    // Save status
    saveStatus,
    setSaveStatus,
    
    // Internal update tracking
    isInternalUpdate,
    setIsInternalUpdate
  };
}