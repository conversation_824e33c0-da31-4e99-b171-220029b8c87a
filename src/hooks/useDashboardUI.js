import { useEffect } from 'react';
import useUIStore from '../stores/uiStore';

/**
 * Dashboard-specific UI state hook
 * Consolidates all UI-related state for Dashboard
 */
export function useDashboardUI() {
  const {
    // State
    modals,
    showProfileMenu,
    showMobileSidebarSheet,
    showMobileContextMenu,
    contextMenuTarget,
    linkCallback,
    editingProject,
    storageInfo,
    isMobile,
    loadingStates,
    
    // Actions
    openModal,
    closeModal,
    setShowProfileMenu,
    setShowMobileSidebarSheet,
    setShowMobileContextMenu,
    setContextMenuTarget,
    setLinkCallback,
    setEditingProject,
    setStorageInfo,
    setIsMobile,
    setLoading,
  } = useUIStore();
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setIsMobile]);
  
  // Return consolidated API matching Dashboard's current usage
  return {
    // Modal states
    showLinkModal: modals.link,
    setShowLinkModal: (show) => show ? openModal('link') : closeModal('link'),
    
    showProjectModal: modals.project,
    setShowProjectModal: (show) => show ? openModal('project') : closeModal('project'),
    
    showCommandPalette: modals.commandPalette,
    setShowCommandPalette: (show) => show ? openModal('commandPalette') : closeModal('commandPalette'),
    
    // Profile menu
    showProfileMenu,
    setShowProfileMenu,
    
    // Mobile states
    showMobileSidebarSheet,
    setShowMobileSidebarSheet,
    
    showMobileContextMenu,
    setShowMobileContextMenu,
    
    // Context menu
    contextMenuTarget,
    setContextMenuTarget,
    
    // Link callback
    linkCallback,
    setLinkCallback,
    
    // Project editing
    editingProject,
    setEditingProject,
    
    // Storage info
    storageInfo,
    setStorageInfo,
    
    // Device state
    isMobile,
    setIsMobile,
    
    // Loading states
    isLoading: loadingStates.documents,
    setIsLoading: (loading) => setLoading('documents', loading),
    
    // Helper methods
    closeAllModals: () => {
      closeModal('link');
      closeModal('project');
      closeModal('commandPalette');
      setShowProfileMenu(false);
      setShowMobileSidebarSheet(false);
      setShowMobileContextMenu(false);
    },
  };
}

// Export default
export default useDashboardUI;