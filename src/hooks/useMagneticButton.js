import { useState, useRef, useCallback } from 'react';

/**
 * Hook for magnetic button effect
 * @returns {Object} Button ref, hover state, and event handlers
 */
export function useMagneticButton() {
  const [magneticHover, setMagneticHover] = useState({ x: 0, y: 0 });
  const buttonRef = useRef(null);

  const handleMouseMove = useCallback((e) => {
    if (!buttonRef.current) return;
    
    const rect = buttonRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;
    
    const distance = Math.sqrt(x * x + y * y);
    const maxDistance = 100;
    
    if (distance < maxDistance) {
      const force = (maxDistance - distance) / maxDistance;
      setMagneticHover({ x: x * force * 0.3, y: y * force * 0.3 });
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    setMagneticHover({ x: 0, y: 0 });
  }, []);

  return {
    buttonRef,
    magneticStyle: {
      transform: `translate(${magneticHover.x}px, ${magneticHover.y}px)`,
    },
    onMouseMove: handleMouseMove,
    onMouseLeave: handleMouseLeave
  };
}