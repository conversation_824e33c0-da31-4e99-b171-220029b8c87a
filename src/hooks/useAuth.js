import { useEffect } from 'react';
import useAuthStore from '../stores/authStore';
import { supabase } from '../lib/supabaseOptimized';

/**
 * Compatibility hook for AuthContext migration
 * Drop-in replacement for useContext(AuthContext)
 * 
 * @returns {Object} Auth state and methods matching original AuthContext
 */
export function useAuth() {
  const {
    user,
    session,
    loading,
    error,
    trialStatus,
    initialize,
    cleanup,
    signIn,
    signUp,
    signInWithProvider,
    signOut,
    resetPassword,
    updatePassword,
    checkTrialStatus,
    isInitialized,
    isInitializing
  } = useAuthStore();
  
  // Initialize auth on mount - only once
  useEffect(() => {
    // Only initialize if not already initialized or initializing
    if (!isInitialized && !isInitializing) {
      initialize();
    }
    
    return () => {
      // Only cleanup if we're unmounting the whole app
      // Don't cleanup on every component unmount
    };
  }, []); // Empty deps - run once
  
  // Return API matching original AuthContext
  return {
    user,
    session,
    loading,
    error,
    trialStatus,
    signIn,
    signUp,
    signInWithProvider,
    signOut,
    resetPassword,
    updatePassword,
    checkTrialStatus,
    // Include supabase client for compatibility with components that access it directly
    supabase
  };
}

// Export from here for convenience
export { useAuth as default };