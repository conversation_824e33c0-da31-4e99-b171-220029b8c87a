import { useMemo } from 'react';

/**
 * Hook to calculate password strength
 * @param {string} password - The password to evaluate
 * @returns {Object} Strength level (0-4) and label
 */
export function usePasswordStrength(password) {
  return useMemo(() => {
    if (!password || password.length === 0) {
      return { strength: 0, label: '' };
    }

    let strength = 0;
    if (password.length >= 8) strength++;
    if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;

    const labels = ['Too weak', 'Weak', 'Fair', 'Good', 'Strong'];
    
    return {
      strength,
      label: labels[strength] || ''
    };
  }, [password]);
}