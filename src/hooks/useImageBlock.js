/**
 * Custom hook for ImageBlock component
 * Combines blockEditorStore and imageBlockStore
 */

import { useEffect, useCallback } from 'react';
import { useBlockEditor } from './useBlockEditor';
import useImageBlockStore from '../stores/imageBlockStore';

export function useImageBlock(blockId, initialState = {}) {
  // Get common editing states from blockEditorStore
  const blockEditor = useBlockEditor(blockId, {
    isEditing: initialState.isEditing,
    isCollapsed: initialState.isCollapsed
  });
  
  // Get image-specific UI states from imageBlockStore
  const {
    initializeImageBlock,
    updateImageBlockState,
    setUploading,
    setUploadProgress,
    setError,
    setEditingImageId,
    setDraggedImageId,
    setLightbox,
    clearImageBlockState,
    getImageBlockState
  } = useImageBlockStore();
  
  const imageBlockState = useImageBlockStore((state) => state.getImageBlockState(blockId));
  
  // Initialize image block state on mount
  useEffect(() => {
    initializeImageBlock(blockId, initialState);
    
    // Cleanup on unmount
    return () => {
      clearImageBlockState(blockId);
    };
  }, [blockId]);
  
  // Wrapped actions for image-specific states
  const setIsUploading = useCallback((isUploading) => {
    setUploading(blockId, isUploading);
  }, [blockId, setUploading]);
  
  const setUploadProgressWrapped = useCallback((progress) => {
    setUploadProgress(blockId, progress);
  }, [blockId, setUploadProgress]);
  
  const setErrorWrapped = useCallback((error) => {
    setError(blockId, error);
  }, [blockId, setError]);
  
  const setEditingImageIdWrapped = useCallback((imageId) => {
    setEditingImageId(blockId, imageId);
  }, [blockId, setEditingImageId]);
  
  const setDraggedImageIdWrapped = useCallback((imageId) => {
    setDraggedImageId(blockId, imageId);
  }, [blockId, setDraggedImageId]);
  
  const setLightboxImage = useCallback((image) => {
    setLightbox(blockId, image, imageBlockState.lightboxIndex);
  }, [blockId, imageBlockState.lightboxIndex, setLightbox]);
  
  const setLightboxIndex = useCallback((index) => {
    setLightbox(blockId, imageBlockState.lightboxImage, index);
  }, [blockId, imageBlockState.lightboxImage, setLightbox]);
  
  return {
    // From blockEditorStore (common editing states)
    ...blockEditor,
    
    // From imageBlockStore (image-specific UI states)
    isUploading: imageBlockState.isUploading,
    setIsUploading,
    
    uploadProgress: imageBlockState.uploadProgress,
    setUploadProgress: setUploadProgressWrapped,
    
    error: imageBlockState.error,
    setError: setErrorWrapped,
    
    editingImageId: imageBlockState.editingImageId,
    setEditingImageId: setEditingImageIdWrapped,
    
    draggedImageId: imageBlockState.draggedImageId,
    setDraggedImageId: setDraggedImageIdWrapped,
    
    lightboxImage: imageBlockState.lightboxImage,
    setLightboxImage,
    
    lightboxIndex: imageBlockState.lightboxIndex,
    setLightboxIndex
  };
}