import { useEffect, useRef, useCallback, useState } from 'react';

/**
 * Hook for auto-saving React Hook Form data
 * Integrates with React Hook Form to provide automatic saving capabilities
 * @param {Object} params
 * @param {Object} params.form - React Hook Form instance (from useForm)
 * @param {Function} params.onSave - Async function to save data
 * @param {number} params.delay - Debounce delay in ms (default: 3000)
 * @param {boolean} params.enabled - Whether auto-save is enabled (default: true)
 * @param {Function} params.onSuccess - Callback on successful save
 * @param {Function} params.onError - Callback on save error
 * @returns {Object} Auto-save status and controls
 */
export function useFormAutoSave({ 
  form, 
  onSave, 
  delay = 3000, 
  enabled = true,
  onSuccess,
  onError
}) {
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [error, setError] = useState(null);
  const timeoutRef = useRef(null);
  const abortControllerRef = useRef(null);
  
  const { watch, formState: { isDirty, dirtyFields } } = form;
  
  // Watch all form values
  const watchedValues = watch();
  
  // Save function
  const save = useCallback(async (forceSave = false) => {
    if (!enabled && !forceSave) return;
    if (!isDirty && !forceSave) return;
    if (saving) return;
    
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    // Cancel any pending save
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    try {
      setSaving(true);
      setError(null);
      
      // Get only the dirty fields if not force saving
      const dataToSave = forceSave ? watchedValues : Object.keys(dirtyFields).reduce((acc, key) => {
        acc[key] = watchedValues[key];
        return acc;
      }, {});
      
      await onSave(dataToSave, { 
        signal: abortControllerRef.current.signal,
        isDirty: isDirty,
        dirtyFields: dirtyFields
      });
      
      setLastSaved(new Date());
      
      // Reset form state after successful save
      form.reset(watchedValues, {
        keepValues: true,
        keepDirty: false,
        keepErrors: true
      });
      
      onSuccess?.({ data: dataToSave, timestamp: new Date() });
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err);
        console.error('Form auto-save error:', err);
        onError?.(err);
      }
    } finally {
      setSaving(false);
      abortControllerRef.current = null;
    }
  }, [watchedValues, isDirty, dirtyFields, onSave, enabled, saving, form, onSuccess, onError]);
  
  // Debounced auto-save effect
  useEffect(() => {
    if (!enabled || !isDirty) return;
    
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      save();
    }, delay);
    
    // Cleanup
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [watchedValues, isDirty, enabled, delay, save]);
  
  // Save on unmount if dirty
  useEffect(() => {
    return () => {
      if (isDirty && enabled) {
        // Cancel any pending saves
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      }
    };
  }, [isDirty, enabled]);
  
  // Handle page unload
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (isDirty && enabled) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [isDirty, enabled]);
  
  return {
    saving,
    lastSaved,
    error,
    hasUnsavedChanges: isDirty,
    dirtyFields,
    save: () => save(true), // Manual save
    clearError: () => setError(null),
    getRelativeTime: () => {
      if (!lastSaved) return null;
      const seconds = Math.floor((Date.now() - lastSaved.getTime()) / 1000);
      if (seconds < 60) return 'just now';
      if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes ago`;
      return `${Math.floor(seconds / 3600)} hours ago`;
    }
  };
}

/**
 * Simple auto-save indicator component
 * @param {Object} props
 * @param {boolean} props.saving - Whether currently saving
 * @param {Date} props.lastSaved - Last saved timestamp
 * @param {Object} props.error - Save error if any
 */
export function AutoSaveIndicator({ saving, lastSaved, error }) {
  const [relativeTime, setRelativeTime] = useState(null);
  
  useEffect(() => {
    if (!lastSaved) return;
    
    const updateRelativeTime = () => {
      const seconds = Math.floor((Date.now() - lastSaved.getTime()) / 1000);
      if (seconds < 60) setRelativeTime('just now');
      else if (seconds < 3600) setRelativeTime(`${Math.floor(seconds / 60)}m ago`);
      else setRelativeTime(`${Math.floor(seconds / 3600)}h ago`);
    };
    
    updateRelativeTime();
    const interval = setInterval(updateRelativeTime, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, [lastSaved]);
  
  if (error) {
    return (
      <div className="text-sm text-red-600 dark:text-red-400">
        Save failed
      </div>
    );
  }
  
  if (saving) {
    return (
      <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1">
        <div className="w-3 h-3 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
        Saving...
      </div>
    );
  }
  
  if (lastSaved && relativeTime) {
    return (
      <div className="text-sm text-green-600 dark:text-green-400">
        Saved {relativeTime}
      </div>
    );
  }
  
  return null;
}