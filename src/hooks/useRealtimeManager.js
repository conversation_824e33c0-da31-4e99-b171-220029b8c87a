/**
 * Hook to manage RealtimeManager lifecycle
 * Ensures proper cleanup when user logs out or component unmounts
 */

import { useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import realtimeManager from '../utils/realtimeManager';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';

export function useRealtimeManager() {
  const { user } = useAuth();

  useEffect(() => {
    if (!user?.id) {
      // No user, ensure realtime is cleaned up
      realtimeManager.cleanup();
      return;
    }

    // Initialize realtime for the user
    console.log('[useRealtimeManager] Initializing for user:', user.id);
    realtimeManager.initialize(user.id);

    // Listen for auth state changes
    const handleAuthChange = ({ authenticated, userId }) => {
      if (!authenticated) {
        console.log('[useRealtimeManager] User logged out, cleaning up realtime');
        realtimeManager.cleanup();
      }
    };

    eventBus.on(EVENT_TYPES.AUTH_STATE_CHANGED, handleAuthChange);

    // Cleanup on unmount or user change
    return () => {
      console.log('[useRealtimeManager] Cleaning up realtime subscriptions');
      realtimeManager.cleanup();
      eventBus.off(EVENT_TYPES.AUTH_STATE_CHANGED, handleAuthChange);
    };
  }, [user?.id]);
}