import { useEffect } from 'react';
import useDocumentStore from '../stores/documentStore';
import useAuthStore from '../stores/authStore';

/**
 * Dashboard-specific document operations hook
 * Consolidates all document-related state and operations for Dashboard
 */
export function useDashboardDocuments() {
  const user = useAuthStore((state) => state.user);
  
  const {
    // State
    documents,
    filteredDocuments,
    isLoading,
    searchQuery,
    filterTags,
    expandedDocumentId,
    projects,
    selectedProjectId,
    draggedDocuments,
    isDragging,
    
    // Actions
    loadDocuments,
    setSearchQuery,
    setFilterTags,
    setExpandedDocumentId,
    loadProjects,
    setSelectedProjectId,
    createProject,
    updateProject,
    deleteProject,
    startDrag,
    endDrag,
    setDraggedOver,
    moveDocuments,
    createDocument,
    updateDocument,
    deleteDocuments,
    updateAllDocuments,
  } = useDocumentStore();
  
  // Load documents and projects on mount or user change
  useEffect(() => {
    if (user?.id) {
      loadDocuments(user.id);
      loadProjects(user.id);
    }
  }, [user?.id, loadDocuments, loadProjects]);
  
  // Return consolidated API
  return {
    // Document state (replacing entries)
    entries: filteredDocuments,
    documents: filteredDocuments,
    isLoading,
    
    // Search state (replacing searchTerm)
    searchTerm: searchQuery,
    setSearchTerm: setSearchQuery,
    
    // Tag filter state (replacing selectedTags)
    selectedTags: filterTags,
    setSelectedTags: setFilterTags,
    
    // Expanded document state (replacing expandedEntry)
    expandedEntry: expandedDocumentId,
    setExpandedEntry: setExpandedDocumentId,
    
    // Project state
    projects,
    selectedProjectId,
    setSelectedProjectId,
    
    // Project operations
    createProject,
    updateProject,
    deleteProject,
    
    // Drag state (replacing activeId)
    activeId: isDragging ? draggedDocuments[0]?.id : null,
    setActiveId: (id) => {
      if (id) {
        const doc = documents.find(d => d.id === id);
        if (doc) startDrag([doc]);
      } else {
        endDrag();
      }
    },
    
    // Document operations
    createDocument,
    updateDocument,
    deleteDocuments,
    moveDocuments,
    updateAllDocuments,
    
    // Additional helpers
    reloadDocuments: () => user?.id && loadDocuments(user.id),
    reloadProjects: () => user?.id && loadProjects(user.id),
  };
}

// Export convenient named exports
export default useDashboardDocuments;