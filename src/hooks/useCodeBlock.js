/**
 * Custom hook for CodeBlock component
 * Combines blockEditorStore and codeBlockStore
 */

import { useEffect, useCallback } from 'react';
import { useBlockEditor } from './useBlockEditor';
import useCodeBlockStore from '../stores/codeBlockStore';

export function useCodeBlock(blockId, initialState = {}) {
  // Get common editing states from blockEditorStore
  const blockEditor = useBlockEditor(blockId, {
    isEditing: initialState.isEditing,
    isCollapsed: initialState.isCollapsed
  });
  
  // Get code-specific UI states from codeBlockStore
  const {
    initializeCodeBlock,
    updateCodeBlockState,
    setCopied,
    setFullscreen,
    setExpanded,
    setViewMode,
    setShowLanguageDropdown,
    setFilePathSuggestions,
    clearCodeBlockState,
    getCodeBlockState
  } = useCodeBlockStore();
  
  const codeBlockState = useCodeBlockStore((state) => state.getCodeBlockState(blockId));
  
  // Initialize code block state on mount
  useEffect(() => {
    initializeCodeBlock(blockId, initialState);
    
    // Cleanup on unmount
    return () => {
      clearCodeBlockState(blockId);
    };
  }, [blockId]);
  
  // Wrapped actions for code-specific states
  const setCopiedWrapped = useCallback((copied) => {
    setCopied(blockId, copied);
  }, [blockId, setCopied]);
  
  const setIsFullscreen = useCallback((isFullscreen) => {
    setFullscreen(blockId, isFullscreen);
  }, [blockId, setFullscreen]);
  
  const setIsExpanded = useCallback((isExpanded) => {
    setExpanded(blockId, isExpanded);
  }, [blockId, setExpanded]);
  
  const setViewModeWrapped = useCallback((viewMode) => {
    setViewMode(blockId, viewMode);
  }, [blockId, setViewMode]);
  
  const setShowLanguageDropdownWrapped = useCallback((show) => {
    setShowLanguageDropdown(blockId, show);
  }, [blockId, setShowLanguageDropdown]);
  
  const setFilePathSuggestionsWrapped = useCallback((show, suggestions) => {
    setFilePathSuggestions(blockId, show, suggestions);
  }, [blockId, setFilePathSuggestions]);
  
  return {
    // From blockEditorStore (common editing states)
    ...blockEditor,
    
    // From codeBlockStore (code-specific UI states)
    copied: codeBlockState.copied,
    setCopied: setCopiedWrapped,
    
    isFullscreen: codeBlockState.isFullscreen,
    setIsFullscreen,
    
    isExpanded: codeBlockState.isExpanded,
    setIsExpanded,
    
    viewMode: codeBlockState.viewMode,
    setViewMode: setViewModeWrapped,
    
    showLanguageDropdown: codeBlockState.showLanguageDropdown,
    setShowLanguageDropdown: setShowLanguageDropdownWrapped,
    
    showFilePathSuggestions: codeBlockState.showFilePathSuggestions,
    filePathSuggestions: codeBlockState.filePathSuggestions,
    setFilePathSuggestions: setFilePathSuggestionsWrapped,
    setShowFilePathSuggestions: (show) => setFilePathSuggestionsWrapped(show, codeBlockState.filePathSuggestions)
  };
}