/**
 * Custom hook for Supabase real-time subscriptions with proper cleanup
 * This hook ensures all subscriptions are properly cleaned up to prevent memory leaks
 */

import { useEffect, useRef, useState } from 'react';
import { supabase } from '../lib/supabaseOptimized';
import realtimeDebugger from '../utils/realtimeDebugger';

export function useSupabaseRealtime({
  table,
  event = '*', // 'INSERT', 'UPDATE', 'DELETE', or '*' for all
  filter = null, // e.g., 'user_id=eq.123'
  onUpdate,
  enabled = true,
  component = 'Unknown' // For debugging
}) {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);
  const channelRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttemptsRef = useRef(0);
  const debugIdRef = useRef(null);

  useEffect(() => {
    if (!enabled) return;

    let isMounted = true;
    
    const setupSubscription = async () => {
      try {
        // Clean up any existing subscription
        if (channelRef.current) {
          await supabase.removeChannel(channelRef.current);
        }

        // Create unique channel name
        const channelName = `${table}_${event}_${Date.now()}`;
        
        // Set up the subscription
        const channel = supabase
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: event,
              schema: 'public',
              table: table,
              filter: filter
            },
            (payload) => {
              if (isMounted) {
                console.log(`[Realtime] ${table} ${payload.eventType}:`, payload);
                onUpdate?.(payload);
              }
            }
          )
          .on('system', {}, (payload) => {
            if (payload.extension === 'postgres_changes') {
              if (payload.status === 'ok') {
                console.log(`[Realtime] Connected to ${table}`);
                setIsConnected(true);
                setError(null);
                reconnectAttemptsRef.current = 0;
              } else if (payload.status === 'error') {
                console.error(`[Realtime] Error connecting to ${table}:`, payload.message);
                setIsConnected(false);
                setError(payload.message);
                attemptReconnect();
              }
            }
          })
          .subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              setIsConnected(true);
              channelRef.current = channel;
              
              // Track subscription in debugger
              if (!debugIdRef.current) {
                debugIdRef.current = realtimeDebugger.trackSubscription(table, channel, component);
              }
            } else if (status === 'CHANNEL_ERROR') {
              setIsConnected(false);
              attemptReconnect();
            } else if (status === 'TIMED_OUT') {
              setIsConnected(false);
              attemptReconnect();
            } else if (status === 'CLOSED') {
              setIsConnected(false);
            }
          });

      } catch (err) {
        console.error(`[Realtime] Setup error for ${table}:`, err);
        setError(err.message);
        setIsConnected(false);
      }
    };

    const attemptReconnect = () => {
      if (reconnectAttemptsRef.current >= 5) {
        console.error(`[Realtime] Max reconnection attempts reached for ${table}`);
        return;
      }

      const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
      console.log(`[Realtime] Reconnecting to ${table} in ${delay}ms...`);
      
      reconnectTimeoutRef.current = setTimeout(() => {
        reconnectAttemptsRef.current++;
        setupSubscription();
      }, delay);
    };

    setupSubscription();

    // Cleanup function
    return () => {
      isMounted = false;
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (channelRef.current) {
        console.log(`[Realtime] Cleaning up subscription for ${table}`);
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
      
      // Track cleanup in debugger
      if (debugIdRef.current) {
        realtimeDebugger.trackCleanup(debugIdRef.current);
        debugIdRef.current = null;
      }
      
      setIsConnected(false);
      setError(null);
    };
  }, [table, event, filter, enabled, component]);

  return {
    isConnected,
    error,
    reconnect: () => {
      reconnectAttemptsRef.current = 0;
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
      // Trigger re-subscription by changing a dependency
      setError(null);
    }
  };
}