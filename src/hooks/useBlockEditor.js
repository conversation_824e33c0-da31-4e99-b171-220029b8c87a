/**
 * Hook for block components to manage their editing state
 * Consolidates block-specific useState calls into blockEditorStore
 */

import { useEffect, useCallback } from 'react';
import useBlockEditorStore from '../stores/blockEditorStore';

export function useBlockEditor(blockId, initialState = {}) {
  const {
    setBlockEditing,
    isBlockEditing,
    setToolbarState,
    updateToolbar,
    setSelectionState,
    setBlockCollapsed,
    toggleBlockCollapse,
    setSlashCommandState,
    updateSlashHint,
    setHoveredBlock,
    setFocusedBlock,
    clearBlockState,
    selectors
  } = useBlockEditorStore();

  // Get current state for this block
  const blockState = selectors.getBlockState(blockId);
  
  // Initialize state on mount
  useEffect(() => {
    // Set initial states if provided
    if (initialState.isEditing !== undefined) {
      setBlockEditing(blockId, initialState.isEditing);
    }
    if (initialState.isCollapsed !== undefined) {
      setBlockCollapsed(blockId, initialState.isCollapsed);
    }
    
    // Cleanup on unmount
    return () => {
      clearBlockState(blockId);
    };
  }, [blockId]);

  // Editing state
  const isEditing = blockState.isEditing;
  const setIsEditing = useCallback((editing) => {
    setBlockEditing(blockId, editing);
  }, [blockId, setBlockEditing]);

  // Toolbar state
  const showToolbar = blockState.toolbar.visible;
  const toolbarPosition = blockState.toolbar.position;
  const setShowToolbar = useCallback((visible, position = null) => {
    updateToolbar(blockId, visible, position);
  }, [blockId, updateToolbar]);

  // Selection state
  const selectedText = blockState.selection?.text || '';
  const setSelectedText = useCallback((text, range = null) => {
    setSelectionState(blockId, { text, range });
  }, [blockId, setSelectionState]);

  // Collapse state
  const isCollapsed = blockState.isCollapsed;
  const setIsCollapsed = useCallback((collapsed) => {
    setBlockCollapsed(blockId, collapsed);
  }, [blockId, setBlockCollapsed]);
  const toggleCollapse = useCallback(() => {
    toggleBlockCollapse(blockId);
  }, [blockId, toggleBlockCollapse]);

  // Slash command state
  const slashHint = blockState.slashCommand.hint;
  const slashHintPosition = blockState.slashCommand.position;
  const setSlashHint = useCallback((hint) => {
    updateSlashHint(blockId, hint, null);
  }, [blockId, updateSlashHint]);
  const setSlashHintPosition = useCallback((position) => {
    updateSlashHint(blockId, slashHint, position);
  }, [blockId, slashHint, updateSlashHint]);

  // Hover and focus
  const setHovered = useCallback((hovered) => {
    setHoveredBlock(hovered ? blockId : null);
  }, [blockId, setHoveredBlock]);
  
  const setFocused = useCallback((focused) => {
    setFocusedBlock(focused ? blockId : null);
  }, [blockId, setFocusedBlock]);

  return {
    // Editing
    isEditing,
    setIsEditing,
    
    // Toolbar
    showToolbar,
    toolbarPosition,
    setShowToolbar,
    
    // Selection
    selectedText,
    setSelectedText,
    
    // Collapse
    isCollapsed,
    setIsCollapsed,
    toggleCollapse,
    
    // Slash commands
    slashHint,
    slashHintPosition,
    setSlashHint,
    setSlashHintPosition,
    
    // Interaction
    setHovered,
    setFocused,
    
    // Direct access to all state
    blockState
  };
}