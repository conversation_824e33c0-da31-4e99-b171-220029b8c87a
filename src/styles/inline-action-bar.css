/* Inline Action Bar - World-class UX patterns */
.inline-action-bar {
  /* Ensure smooth animations */
  will-change: opacity, transform;
  
  /* Prevent layout shifts */
  contain: layout style;
}

/* Individual action buttons */
.inline-action-button {
  /* Remove default button styles */
  appearance: none;
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  
  /* Ensure proper touch targets */
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Smooth spring physics on hover */
@media (hover: hover) {
  .inline-action-button {
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .inline-action-button:hover {
    transform: scale(1.05);
  }
  
  .inline-action-button:active {
    transform: scale(0.95);
  }
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .inline-action-bar.always-visible {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
  }
  
  /* Larger touch targets on mobile */
  .inline-action-button {
    min-width: 44px !important;
    min-height: 44px !important;
  }
}

/* Compact vertical layout */
.inline-action-bar {
  height: auto;
  width: auto;
  max-width: 48px;
}

/* Responsive positioning for different screen sizes */
@media (max-width: 1024px) {
  .inline-action-bar {
    left: -4rem !important;
  }
}

@media (max-width: 768px) {
  .inline-action-bar {
    left: -3.5rem !important;
  }
}

@media (max-width: 640px) {
  .inline-action-bar {
    left: -3rem !important;
    transform: scale(0.9);
    transform-origin: left top;
  }
}

/* Vertical layout specific styles */
.inline-action-bar.vertical {
  flex-direction: column;
}

.inline-action-bar .dropdown-menu {
  position: absolute;
  right: 100%;
  top: 0;
  margin-right: 0.5rem;
}

/* Keyboard focus indicators */
.inline-action-button:focus-visible {
  outline: 2px solid #10b981;
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Drag handle cursor states */
.drag-handle {
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

/* Prevent text selection during drag */
.dragging {
  user-select: none;
  -webkit-user-select: none;
}

/* Smooth transitions for visibility */
.inline-action-bar {
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1),
              transform 200ms cubic-bezier(0.4, 0, 0.2, 1),
              visibility 200ms;
}

/* Active state animation */
@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.inline-action-button:active {
  animation: buttonPress 150ms ease-out;
}

/* Dropdown menu styling */
.inline-action-bar .dropdown-menu {
  animation: slideDown 200ms ease-out;
  z-index: 9999 !important;
  position: relative;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover bridge for better UX */
.hover-bridge {
  /* This invisible element helps maintain hover state */
  pointer-events: auto;
}

/* Compact button styling */
.inline-action-button {
  /* Smaller size for compact design */
  padding: 0.375rem;
  min-width: 28px;
  min-height: 28px;
}

/* Semi-transparent background */
.inline-action-bar {
  background-blend-mode: multiply;
}