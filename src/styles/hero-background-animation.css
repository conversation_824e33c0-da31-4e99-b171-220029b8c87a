/* Hero Background Animation - Performance-First Premium Effects */
.hero-background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
  pointer-events: none;
  contain: layout style paint;
  
  /* Mouse position custom properties */
  --mouse-x: 50%;
  --mouse-y: 50%;
}

/* Enable pointer events only on the container for mouse tracking */
.hero-background-animation {
  pointer-events: auto;
}

/* Individual blocks */
.hero-block {
  position: absolute;
  pointer-events: auto;
  will-change: transform;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Block inner container for effects */
.hero-block__inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Block content wrapper */
.hero-block__content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Hover state for individual blocks */
.hero-block:hover .hero-block__inner {
  transform: 
    rotateY(10deg) 
    rotateX(-10deg) 
    translateZ(20px)
    scale(1.05);
}

.hero-block:hover .hero-block__content {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 2px 2px rgba(255, 255, 255, 0.1);
}

/* Block glow effect */
.hero-block__glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    currentColor 0%,
    transparent 70%
  );
  opacity: 0;
  filter: blur(30px);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.hero-block:hover .hero-block__glow {
  opacity: 0.2;
}

/* Block type variations */
.hero-block--code {
  color: #3b82f6;
}

.hero-block--code .hero-block__content {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.03) 0%, 
    rgba(59, 130, 246, 0.06) 100%
  );
}

.hero-block--text {
  color: #ffffff;
}

.hero-block--ai {
  color: #8b5cf6;
}

.hero-block--ai .hero-block__content {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.03) 0%, 
    rgba(139, 92, 246, 0.06) 100%
  );
  animation: ai-pulse 4s ease-in-out infinite;
}

@keyframes ai-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.hero-block--heading {
  color: #ec4899;
}

.hero-block--todo {
  color: #10b981;
}

.hero-block--todo .hero-block__content {
  background: linear-gradient(135deg, 
    rgba(16, 185, 129, 0.03) 0%, 
    rgba(16, 185, 129, 0.06) 100%
  );
}

.hero-block--version {
  color: #f59e0b;
}

.hero-block--file {
  color: #6b7280;
}

.hero-block--table {
  color: #06b6d4;
}

/* Block icons */
.hero-block__icon {
  width: 40%;
  height: 40%;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.hero-block:hover .hero-block__icon {
  opacity: 0.9;
  transform: scale(1.1);
}

/* Code block lines */
.hero-block__code-lines {
  position: absolute;
  bottom: 15%;
  left: 15%;
  right: 15%;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.hero-block__code-line {
  height: 2px;
  background: currentColor;
  opacity: 0.2;
  border-radius: 1px;
}

.hero-block__code-line:nth-child(1) { width: 80%; }
.hero-block__code-line:nth-child(2) { width: 60%; }
.hero-block__code-line:nth-child(3) { width: 70%; }

/* Layer-based depth */
.hero-block--layer-0 {
  filter: blur(1px);
  z-index: 1;
}

.hero-block--layer-1 {
  z-index: 2;
}

.hero-block--layer-2 {
  z-index: 3;
}

/* Ambient animation */
@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

/* Performance optimizations */
@media (prefers-reduced-motion: reduce) {
  .hero-background-animation {
    display: none;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .hero-block {
    /* Disable mouse tracking on mobile */
    --repulsion-x: 0 !important;
    --repulsion-y: 0 !important;
  }
  
  .hero-block__content {
    backdrop-filter: blur(4px);
  }
  
  .hero-block:hover .hero-block__inner {
    transform: none;
  }
}

/* High performance mode */
@media (hover: hover) and (min-width: 1024px) {
  .hero-block {
    /* Enable advanced effects on capable devices */
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .hero-block__content {
    backdrop-filter: blur(12px) saturate(1.2);
  }
  
  /* Subtle shimmer effect */
  .hero-block__content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.05) 50%,
      transparent 100%
    );
    transition: left 0.6s ease;
  }
  
  .hero-block:hover .hero-block__content::before {
    left: 100%;
  }
}

/* Reduced data mode */
@media (prefers-reduced-data: reduce) {
  .hero-block__content {
    backdrop-filter: none;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .hero-block__glow {
    display: none;
  }
}

/* CSS-only magnetic field visualization */
.hero-background-animation::before {
  content: '';
  position: absolute;
  top: var(--mouse-y);
  left: var(--mouse-x);
  width: 300px;
  height: 300px;
  transform: translate(-50%, -50%);
  background: radial-gradient(
    circle at center,
    rgba(59, 130, 246, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hero-background-animation:hover::before {
  opacity: 1;
}