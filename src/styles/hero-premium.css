/* Hero Section - Premium UI Design (Optimized) */

/* CSS Variables for hero section */
:root {
  /* Hero gradients - simplified to 3 stops */
  --hero-gradient-primary: linear-gradient(135deg, #10b981 0%, #047857 50%, #064e3b 100%);
  --hero-gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --hero-gradient-accent: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
  
  /* Mesh gradient colors */
  --mesh-color-1: rgba(16, 185, 129, 0.3);
  --mesh-color-2: rgba(59, 130, 246, 0.3);
  --mesh-color-3: rgba(139, 92, 246, 0.3);
  --mesh-color-4: rgba(236, 72, 153, 0.3);
  
  /* Glow effects */
  --hero-glow-primary: rgba(16, 185, 129, 0.5);
  --hero-glow-secondary: rgba(59, 130, 246, 0.5);
  --hero-glow-accent: rgba(139, 92, 246, 0.5);
  
  /* Reusable shadows */
  --hero-shadow-sm: 0 4px 12px rgba(16, 185, 129, 0.1), inset 0 1px 1px rgba(255, 255, 255, 0.1);
  --hero-shadow-md: 0 8px 20px rgba(16, 185, 129, 0.2), inset 0 1px 1px rgba(255, 255, 255, 0.2);
  --hero-shadow-lg: 0 4px 8px rgba(16, 185, 129, 0.2), 0 8px 16px rgba(16, 185, 129, 0.15), 0 16px 32px rgba(16, 185, 129, 0.1);
  --hero-shadow-xl: 0 8px 16px rgba(16, 185, 129, 0.3), 0 16px 32px rgba(16, 185, 129, 0.2), 0 24px 48px rgba(16, 185, 129, 0.15);
}

/* Hero Container */
.hero-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: radial-gradient(ellipse at center, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
  contain: layout style paint;
}

/* Animated Gradient Mesh Background - Optimized */
.hero-gradient-mesh {
  position: absolute;
  inset: 0;
  z-index: 0;
  overflow: hidden;
  contain: strict;
  content-visibility: auto;
}

.mesh-gradient {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  background: 
    radial-gradient(circle at 20% 30%, var(--mesh-color-1) 0%, transparent 40%),
    radial-gradient(circle at 80% 20%, var(--mesh-color-2) 0%, transparent 40%),
    radial-gradient(circle at 40% 80%, var(--mesh-color-3) 0%, transparent 40%),
    radial-gradient(circle at 90% 70%, var(--mesh-color-4) 0%, transparent 40%);
  filter: blur(40px);
  opacity: 0.6;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* Particle Field - Optimized with CSS only */
.hero-particle-field {
  position: absolute;
  inset: 0;
  z-index: 1;
  pointer-events: none;
  contain: strict;
}

.hero-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* Reduced particles for performance - only 5 instead of 10 */
.hero-particle:nth-child(1) { left: 15%; animation: particleDrift 20s linear infinite; }
.hero-particle:nth-child(2) { left: 35%; animation: particleDrift 22s linear 4s infinite; }
.hero-particle:nth-child(3) { left: 55%; animation: particleDrift 18s linear 8s infinite; }
.hero-particle:nth-child(4) { left: 75%; animation: particleDrift 24s linear 12s infinite; }
.hero-particle:nth-child(5) { left: 90%; animation: particleDrift 20s linear 16s infinite; }

@keyframes particleDrift {
  0% {
    transform: translate3d(0, 100vh, 0);
    opacity: 0;
  }
  10%, 90% {
    opacity: 1;
  }
  100% {
    transform: translate3d(100px, -100vh, 0);
    opacity: 0;
  }
}

/* Floating Elements - Disabled for performance */
.hero-floating-elements {
  display: none;
}

/* Enhanced Badge - Optimized */
.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.1) 100%);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #10b981;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: var(--hero-shadow-sm);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  transform: translate3d(0, 0, 0);
}

.hero-badge:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3) 0%, rgba(16, 185, 129, 0.15) 100%);
  border-color: rgba(16, 185, 129, 0.5);
  transform: translate3d(0, -2px, 0);
  box-shadow: var(--hero-shadow-md);
}

.hero-badge-pulse {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  position: relative;
}

.hero-badge-pulse::after {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: #10b981;
  opacity: 0.4;
  animation: badgePulseRing 2s ease-in-out infinite;
  will-change: transform, opacity;
}

@keyframes badgePulseRing {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Enhanced Title */
.hero-title {
  font-size: clamp(2.5rem, 5vw + 1rem, 5rem);
  font-weight: 800;
  line-height: 1.1;
  color: #ffffff;
  position: relative;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hero-title-gradient {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  position: relative;
}

/* Glow effect without duplicated content */
.hero-title-gradient::after {
  content: '';
  position: absolute;
  inset: -20px;
  background: radial-gradient(ellipse at center, var(--hero-glow-primary) 0%, transparent 70%);
  filter: blur(20px);
  opacity: 0.3;
  z-index: -1;
  pointer-events: none;
}

/* Enhanced Subtitle */
.hero-subtitle {
  font-size: clamp(1.125rem, 2vw + 0.5rem, 1.75rem);
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  max-width: 48rem;
  margin: 0 auto;
  position: relative;
}

/* Premium CTA Buttons - Optimized */
.hero-cta-primary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: var(--hero-gradient-primary);
  color: #ffffff;
  font-weight: 600;
  font-size: 1.125rem;
  border-radius: 12px;
  border: none;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: var(--hero-shadow-lg);
  transform: translate3d(0, 0, 0);
}

.hero-cta-primary::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translate3d(-100%, 0, 0);
  transition: transform 0.6s ease;
  will-change: transform;
}

.hero-cta-primary:hover {
  transform: translate3d(0, -2px, 0);
  box-shadow: var(--hero-shadow-xl);
}

.hero-cta-primary:hover::before {
  transform: translate3d(100%, 0, 0);
}

/* Removed liquid morph effect for performance */

/* Secondary CTA */
.hero-cta-secondary {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-weight: 600;
  font-size: 1.125rem;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.hero-cta-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(16, 185, 129, 0.5);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

/* Trust indicators with animation */
.hero-trust-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  transition: color 0.3s ease, transform 0.3s ease;
}

.hero-trust-item:hover {
  color: rgba(255, 255, 255, 0.9);
  transform: translate3d(4px, 0, 0);
}

.hero-trust-check {
  color: #10b981;
}

/* Code snippets floating animation - Disabled for performance */
.floating-code {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }
  
  .hero-subtitle {
    font-size: clamp(1rem, 3vw, 1.25rem);
  }
  
  .hero-cta-primary,
  .hero-cta-secondary {
    padding: 14px 28px;
    font-size: 1rem;
  }
  
  /* Disable heavy effects on mobile */
  .mesh-gradient,
  .hero-particle-field {
    display: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .mesh-gradient,
  .hero-particle,
  .hero-badge-pulse::after {
    animation: none !important;
  }
  
  .hero-cta-primary::before {
    display: none;
  }
}

/* Progressive Enhancement */
@supports (backdrop-filter: blur(10px)) {
  .hero-badge {
    backdrop-filter: blur(10px);
  }
}

@supports not (backdrop-filter: blur(10px)) {
  .hero-badge {
    background: rgba(16, 185, 129, 0.95);
  }
}