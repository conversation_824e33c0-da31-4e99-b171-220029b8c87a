/* Memory Erosion Showcase Styles */

.showcase-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Version selector */
.version-selector {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 2000;
  background: rgba(0, 0, 0, 0.8);
  padding: 5px;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.version-selector button {
  padding: 10px 20px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  cursor: pointer;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.version-selector button:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
}

.version-selector button.active {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

/* Info panel */
.info-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 3000;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 30px;
  height: 30px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: rotate(90deg);
}

.info-panel h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #00ff88, #00b4ff, #ff00b4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tagline {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 30px;
  font-style: italic;
}

.features, .tech-stack, .instructions {
  margin-bottom: 30px;
}

.features h3, .tech-stack h3, .instructions h3 {
  color: #00ff88;
  margin-bottom: 15px;
  font-size: 1.2rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  padding: 8px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Tech badges */
.tech-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.badge {
  padding: 6px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.badge:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: rgba(0, 255, 136, 0.5);
  transform: translateY(-2px);
}

/* Instructions */
.instructions ol {
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.8;
}

.instructions li {
  margin-bottom: 10px;
}

/* Awards banner */
.awards-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 40px;
  z-index: 1500;
  animation: slideUp 1s ease-out 3s both;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.award {
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.award:nth-child(1) { animation-delay: 3.5s; }
.award:nth-child(2) { animation-delay: 3.7s; }
.award:nth-child(3) { animation-delay: 3.9s; }

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.award-icon {
  font-size: 24px;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.award-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Scrollbar styling */
.info-panel::-webkit-scrollbar {
  width: 8px;
}

.info-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.info-panel::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 136, 0.3);
  border-radius: 4px;
}

.info-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 136, 0.5);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .info-panel {
    max-width: 90vw;
    padding: 30px 20px;
  }
  
  .info-panel h2 {
    font-size: 2rem;
  }
  
  .awards-banner {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  .award {
    justify-content: center;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: light) {
  .info-panel {
    background: rgba(255, 255, 255, 0.95);
    color: #000;
  }
  
  .info-panel h2 {
    color: #000;
  }
  
  .features h3, .tech-stack h3, .instructions h3 {
    color: #0088ff;
  }
  
  .features li, .instructions ol {
    color: rgba(0, 0, 0, 0.8);
  }
  
  .badge {
    background: rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.2);
    color: rgba(0, 0, 0, 0.9);
  }
}