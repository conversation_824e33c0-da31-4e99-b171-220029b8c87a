/* Global animation styles */

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Disable animations for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Custom selection color */
::selection {
  background-color: rgba(16, 185, 129, 0.3);
  color: #e0e7ff;
}

::-moz-selection {
  background-color: rgba(16, 185, 129, 0.3);
  color: #e0e7ff;
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid #10b981;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    #1e293b 25%,
    #334155 50%,
    #1e293b 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Glow effect */
.glow {
  box-shadow: 
    0 0 20px rgba(16, 185, 129, 0.5),
    0 0 40px rgba(16, 185, 129, 0.3),
    0 0 60px rgba(16, 185, 129, 0.1);
}

/* Noise texture overlay */
.noise-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.02;
  z-index: 1;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200'%3E%3Cfilter id='noise'%3E%3CfeTurbulence baseFrequency='0.9' /%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)' /%3E%3C/svg%3E");
}

/* Document Toolbar Animations */
@keyframes pulse-once {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

.animate-pulse-once {
  animation: pulse-once 0.6s ease-out;
}