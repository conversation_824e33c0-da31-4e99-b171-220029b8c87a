/* Fluid Typography System - Enhanced for All Viewports */

:root {
  /* Font families with proper fallbacks */
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
  
  /* Viewport widths for calculations */
  --min-width: 320; /* Mobile min */
  --max-width: 2560; /* Ultra-wide max */
  --tablet-min: 768;
  --laptop-min: 1280;
  
  /* Enhanced fluid type scale with better intermediate viewport handling */
  /* Using more granular scaling for problematic ranges */
  
  /* Base font size - smoother scaling across all viewports */
  --step-0: clamp(
    1rem,                        /* 16px minimum for better readability */
    0.95rem + 0.25vw,           /* Smoother fluid scaling */
    1.125rem                     /* 18px maximum on ultra-wide */
  );
  
  /* Heading sizes with optimized scaling for tablet/laptop ranges */
  --step-1: clamp(1.125rem, 1.05rem + 0.375vw, 1.5rem);
  --step-2: clamp(1.333rem, 1.2rem + 0.666vw, 1.875rem);
  --step-3: clamp(1.777rem, 1.5rem + 1.385vw, 2.5rem);
  --step-4: clamp(2.369rem, 1.8rem + 2.845vw, 3.333rem);
  --step-5: clamp(3.157rem, 2.2rem + 4.785vw, 4.444rem);
  --step-6: clamp(4.209rem, 2.5rem + 8.545vw, 5.926rem);
  
  /* Small text with minimal scaling */
  --step--1: clamp(0.833rem, 0.8rem + 0.165vw, 0.937rem);
  --step--2: clamp(0.694rem, 0.68rem + 0.07vw, 0.781rem);
  
  /* Line heights based on golden ratio */
  --line-height-tight: 1.2;
  --line-height-normal: 1.58; /* Medium's ratio */
  --line-height-loose: 1.8;
  
  /* Letter spacing refinements */
  --letter-spacing-tight: -0.003em; /* Medium's approach for headings */
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  
  /* Fluid spacing system that scales with viewport */
  --fluid-space-unit: clamp(0.5rem, 0.4rem + 0.5vw, 0.75rem); /* Base unit */
  
  /* Fluid space scale */
  --space-1: clamp(0.25rem, 0.2rem + 0.25vw, 0.5rem);
  --space-2: clamp(0.5rem, 0.4rem + 0.5vw, 1rem);
  --space-3: clamp(0.75rem, 0.6rem + 0.75vw, 1.5rem);
  --space-4: clamp(1rem, 0.8rem + 1vw, 2rem);
  --space-5: clamp(1.5rem, 1.2rem + 1.5vw, 3rem);
  --space-6: clamp(2rem, 1.6rem + 2vw, 4rem);
  --space-8: clamp(3rem, 2.4rem + 3vw, 6rem);
  --space-10: clamp(4rem, 3.2rem + 4vw, 8rem);
  --space-12: clamp(6rem, 4.8rem + 6vw, 12rem);
  
  /* Component-specific fluid spacing */
  --card-padding: clamp(1rem, 0.8rem + 1vw, 2rem);
  --section-padding: clamp(2rem, 1.5rem + 2.5vw, 5rem);
  --container-padding: clamp(1rem, 0.75rem + 1.25vw, 2.5rem);
}

/* Apply fluid typography to base elements */
body {
  font-family: var(--font-sans);
  font-size: var(--step-0);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; /* Inter's improved legibility features */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Headings with refined spacing */
h1, .h1 {
  font-family: var(--font-sans);
  font-size: var(--step-6);
  font-weight: 800;
  line-height: var(--line-height-tight);
  letter-spacing: -0.03em;
  margin-top: 0;
  margin-bottom: var(--space-4);
}

h2, .h2 {
  font-family: var(--font-sans);
  font-size: var(--step-5);
  font-weight: 700;
  line-height: var(--line-height-tight);
  letter-spacing: -0.025em;
  margin-top: var(--space-6);
  margin-bottom: var(--space-3);
}

h3, .h3 {
  font-family: var(--font-sans);
  font-size: var(--step-4);
  font-weight: 600;
  line-height: calc(var(--line-height-tight) + 0.1);
  letter-spacing: -0.02em;
  margin-top: var(--space-5);
  margin-bottom: var(--space-2);
}

h4, .h4 {
  font-family: var(--font-sans);
  font-size: var(--step-3);
  font-weight: 600;
  line-height: calc(var(--line-height-tight) + 0.2);
  letter-spacing: -0.015em;
  margin-top: var(--space-4);
  margin-bottom: var(--space-2);
}

h5, .h5 {
  font-size: var(--step-2);
  line-height: var(--line-height-normal);
  margin-top: var(--space-3);
  margin-bottom: var(--space-1);
}

h6, .h6 {
  font-size: var(--step-1);
  line-height: var(--line-height-normal);
  margin-top: var(--space-3);
  margin-bottom: var(--space-1);
}

/* Paragraph spacing aligned to baseline grid */
p {
  margin-top: 0;
  margin-bottom: var(--space-3);
}

/* Lead text for hero sections */
.lead {
  font-size: var(--step-1);
  line-height: var(--line-height-loose);
  letter-spacing: var(--letter-spacing-normal);
}

/* Small text */
.small, small {
  font-size: var(--step--1);
  line-height: var(--line-height-normal);
}

/* Extra small text */
.text-xs {
  font-size: var(--step--2);
  line-height: var(--line-height-normal);
}

/* Button text - slightly larger for better legibility */
.btn, button {
  font-size: var(--step-0);
  line-height: var(--line-height-tight);
  letter-spacing: 0.01em;
}

/* Variable font weight based on viewport for subtle enhancement */
@supports (font-variation-settings: normal) {
  body {
    font-variation-settings: 'wght' calc(400 + (450 - 400) * ((100vw - 320px) / (1200 - 320)));
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-variation-settings: 'wght' calc(600 + (700 - 600) * ((100vw - 320px) / (1200 - 320)));
  }
}

/* Vertical rhythm utilities */
.mt-baseline-1 { margin-top: var(--space-1); }
.mt-baseline-2 { margin-top: var(--space-2); }
.mt-baseline-3 { margin-top: var(--space-3); }
.mt-baseline-4 { margin-top: var(--space-4); }
.mt-baseline-5 { margin-top: var(--space-5); }
.mt-baseline-6 { margin-top: var(--space-6); }

.mb-baseline-1 { margin-bottom: var(--space-1); }
.mb-baseline-2 { margin-bottom: var(--space-2); }
.mb-baseline-3 { margin-bottom: var(--space-3); }
.mb-baseline-4 { margin-bottom: var(--space-4); }
.mb-baseline-5 { margin-bottom: var(--space-5); }
.mb-baseline-6 { margin-bottom: var(--space-6); }

/* Ensure proper text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* Code and monospace text */
code, pre, kbd, samp, .font-mono {
  font-family: var(--font-mono);
  font-weight: 400;
  font-size: 0.875em;
  letter-spacing: 0;
  font-feature-settings: 'calt' 1; /* Enable ligatures in JetBrains Mono */
}