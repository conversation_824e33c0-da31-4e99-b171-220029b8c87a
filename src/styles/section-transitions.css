/* =====================================================
   SECTION TRANSITIONS - SMOOTH VISUAL FLOW
   ===================================================== */

/* Base transition styles */
.section-transition {
  position: relative;
  width: 100%;
  pointer-events: none;
  transform: translateZ(0); /* GPU acceleration */
  will-change: transform;
}

.section-transition svg {
  display: block;
  width: 100%;
  height: 100%;
}

/* Specific transition enhancements */
.hero-to-problem {
  margin-top: -2px;
  margin-bottom: -2px;
}

.problem-to-video {
  z-index: 2;
}

.video-to-features {
  z-index: 3;
}

.features-to-pricing {
  z-index: 4;
}

.pricing-to-cta {
  z-index: 5;
}

.cta-to-footer {
  z-index: 6;
}

/* Animation optimizations */
@media (prefers-reduced-motion: no-preference) {
  .section-transition {
    animation: subtleFloat 20s ease-in-out infinite;
  }
}

@keyframes subtleFloat {
  0%, 100% {
    transform: translateY(0) translateZ(0);
  }
  50% {
    transform: translateY(-2px) translateZ(0);
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .section-transition {
    height: 100px !important;
  }
  
  .section-transition svg {
    transform: scale(1.1); /* Prevent edge gaps on mobile */
  }
}