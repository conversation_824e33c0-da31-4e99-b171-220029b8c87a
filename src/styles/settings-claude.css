/* Claude.ai Inspired Settings - Devlog Brand Colors */

/* CSS Variables */
:root {
  /* Brand Colors */
  --brand-primary: #10b981;
  --brand-primary-hover: #059669;
  
  /* Dark Theme Colors */
  --bg-primary: #0a1628;
  --bg-secondary: #1e3a5f;
  --bg-elevated: #1e3a5f;
  --border-color: rgba(255, 255, 255, 0.1);
  
  /* Text Colors */
  --text-primary: #f3f4f6;
  --text-secondary: #9ca3af;
  --text-disabled: rgba(243, 244, 246, 0.4);
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-error: #ef4444;
  --color-warning: #f59e0b;
  
  /* Spacing Scale (4px base) */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  
  /* Typography */
  --font-ui: system-ui, -apple-system, sans-serif;
  --transition: 200ms ease;
}

/* Base */
.settings-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-ui);
  overflow: auto;
}

/* Mobile Header */
.mobile-header {
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 101;
  height: 60px;
  backdrop-filter: blur(10px);
  background: rgba(30, 58, 95, 0.95);
}

.mobile-header h1 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-2) var(--space-3);
  font-size: 0.875rem;
  cursor: pointer;
  border-radius: 8px;
  transition: all var(--transition);
  font-weight: 500;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
}

.back-button:active {
  transform: scale(0.95);
}

.menu-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: var(--space-2);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all var(--transition);
}

.menu-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
}

.menu-button:active {
  transform: scale(0.95);
}

/* Layout */
.settings-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

/* Sidebar */
.settings-sidebar {
  width: 240px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: 0.25rem;
  transition: var(--transition);
}

.close-button:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.05);
}

/* Navigation */
.nav-sections {
  flex: 1;
  padding: var(--space-4);
}

.nav-item {
  display: block;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  border-radius: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: left;
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: var(--space-1);
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.nav-item.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--brand-primary);
}

/* Main Content */
.settings-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-primary);
  width: 100%;
}

.content-section {
  width: 100%;
  max-width: 800px;
  padding: var(--space-8);
  margin: 0 auto;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--space-8) 0;
}

/* Setting Groups */
.setting-group {
  margin-bottom: var(--space-8);
}

.setting-group-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 var(--space-6) 0;
  color: var(--text-primary);
}

/* Setting Items */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
  border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-content {
  flex: 1;
  padding-right: var(--space-4);
}

.setting-label {
  display: block;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.setting-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.setting-value {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: background-color var(--transition);
  flex-shrink: 0;
}

.toggle-switch:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.toggle-switch.active {
  background: var(--brand-primary);
}

.toggle-switch.active:hover:not(:disabled) {
  background: var(--brand-primary-hover);
}

.toggle-switch:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-thumb {
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform var(--transition);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-switch.active .toggle-thumb {
  transform: translateX(20px);
}

/* Select */
.setting-select {
  padding: var(--space-2) var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-family: inherit;
  cursor: pointer;
  transition: var(--transition);
}

.setting-select:hover {
  border-color: rgba(255, 255, 255, 0.2);
}

.setting-select:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all var(--transition);
  font-family: inherit;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background: var(--brand-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--brand-primary-hover);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.05);
}

.btn-danger {
  background: var(--color-error);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

/* Button Sizes */
.btn-small {
  padding: var(--space-1) var(--space-3);
  font-size: 0.875rem;
}

.btn-medium {
  padding: var(--space-2) var(--space-4);
  font-size: 1rem;
}

.btn-large {
  padding: var(--space-3) var(--space-6);
  font-size: 1.125rem;
}

/* Forms */
.form-field {
  margin-bottom: var(--space-4);
}

.form-field label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-field input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-family: inherit;
  transition: var(--transition);
}

.form-field input:focus {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

.password-form {
  max-width: 400px;
}

/* Password Trigger Button */
.password-trigger-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition);
}

.password-trigger-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.password-trigger-btn:active {
  transform: scale(0.98);
}

/* Mobile Password Content */
.mobile-password-content {
  padding: var(--space-6) var(--space-4);
}

.mobile-password-content .form-field {
  margin-bottom: var(--space-4);
}

.mobile-actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-6);
}

.mobile-actions button {
  flex: 1;
}

/* File Input */
.file-input {
  display: none;
}

.file-label {
  display: inline-block;
  padding: var(--space-2) var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.file-label:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Storage */
.storage-info {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: var(--space-4);
}

.storage-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.storage-label {
  font-weight: 500;
}

.storage-value {
  color: var(--text-secondary);
}

.storage-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: var(--space-2);
}

.storage-fill {
  height: 100%;
  background: var(--brand-primary);
  transition: width 0.3s ease;
}

.storage-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Danger Zone */
.danger-zone {
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  padding: var(--space-4);
}

.danger-zone h4 {
  margin: 0 0 var(--space-2) 0;
  color: var(--color-error);
}

.danger-zone p {
  margin: 0 0 var(--space-4) 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Messages */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: var(--color-error);
  padding: var(--space-3) var(--space-4);
  border-radius: 0.5rem;
  margin-bottom: var(--space-4);
  font-size: 0.875rem;
}

/* Modal */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
}

.modal {
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  padding: var(--space-6) var(--space-6) 0;
}

.modal-content {
  padding: var(--space-6);
}

.modal-content p {
  margin: var(--space-4) 0;
  color: var(--text-secondary);
}

.modal-content ul {
  margin: var(--space-2) 0 var(--space-6) var(--space-6);
  color: var(--text-secondary);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: var(--color-error);
  padding: var(--space-3) var(--space-4);
  border-radius: 0.5rem;
  margin-bottom: var(--space-4);
}

.modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .mobile-header {
    display: flex;
  }
  
  .desktop-only {
    display: none;
  }
  
  .settings-layout {
    position: relative;
    height: 100%;
    padding-top: 60px; /* Account for fixed header */
  }
  
  .settings-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .settings-sidebar.show {
    transform: translateX(0);
  }
  
  .mobile-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99;
  }
  
  .settings-content {
    padding-top: 76px; /* 60px header + 16px spacing */
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .content-section {
    padding: var(--space-4);
    padding-top: 0; /* Remove extra padding since we have it on container */
  }
  
  .section-title {
    font-size: 1.5rem;
    margin-bottom: var(--space-4);
    color: var(--text-primary);
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .setting-content {
    padding-right: 0;
  }
  
  .modal {
    margin: var(--space-4);
  }
  
  /* Mobile Setting Group Improvements */
  .setting-group {
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .setting-group-title {
    font-size: 1.125rem;
    margin-bottom: var(--space-4);
  }
  
  /* Improve button sizes on mobile */
  .btn {
    min-height: 48px;
    font-size: 16px;
  }
  
  /* Ensure form fields are properly sized */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 48px;
  }
}

/* API Keys Section */
.api-keys-link {
  margin-top: var(--space-4);
}

.setup-instructions {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: var(--space-6);
  margin-top: var(--space-4);
}

.setup-steps {
  list-style: decimal;
  padding-left: var(--space-6);
  color: var(--text-secondary);
  line-height: 1.8;
}

.setup-steps li {
  margin-bottom: var(--space-2);
}

.setup-steps code {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', monospace;
  color: var(--brand-primary);
}

.setup-link {
  margin-top: var(--space-4);
  text-align: center;
}

.external-link {
  color: var(--brand-primary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color var(--transition);
}

.external-link:hover {
  color: var(--brand-primary-hover);
  text-decoration: underline;
}
