/* =====================================================
   Auth Debug Console Styles
   ===================================================== */

/* Debug Toggle Button */
.auth-debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid #10b981;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 99999;
  backdrop-filter: blur(8px);
}

.auth-debug-toggle:hover {
  background: rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Debug Console Container */
.auth-debug-console {
  position: fixed;
  top: 0;
  left: 0;
  width: 400px;
  max-width: calc(100vw - 40px);
  max-height: calc(100vh - 40px);
  background: rgba(10, 22, 40, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  z-index: 99999;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
}

.auth-debug-console.is-dragging {
  cursor: move;
  user-select: none;
}

.auth-debug-console.is-minimized {
  height: auto;
}

/* Debug Header */
.auth-debug-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(16, 185, 129, 0.1);
  border-bottom: 1px solid rgba(16, 185, 129, 0.2);
  cursor: move;
  user-select: none;
}

.auth-debug-title {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.auth-debug-controls {
  display: flex;
  gap: 8px;
}

.auth-debug-controls button {
  background: transparent;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-debug-controls button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f3f4f6;
}

/* Debug Content */
.auth-debug-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  font-size: 13px;
  color: #e5e7eb;
  max-height: calc(100vh - 120px);
}

/* Custom Scrollbar */
.auth-debug-content::-webkit-scrollbar {
  width: 6px;
}

.auth-debug-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.auth-debug-content::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.3);
  border-radius: 3px;
}

.auth-debug-content::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.5);
}

/* Debug Sections */
.auth-debug-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(75, 85, 99, 0.2);
}

.auth-debug-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.auth-debug-section h3 {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #10b981;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Issues Section */
.auth-debug-issues {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.auth-debug-issue {
  background: rgba(239, 68, 68, 0.1);
  border-left: 3px solid #ef4444;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #f87171;
}

.auth-debug-issue:last-child {
  margin-bottom: 0;
}

/* Grid Layout */
.auth-debug-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px 16px;
  font-size: 12px;
}

.auth-debug-grid > span:nth-child(odd) {
  color: #9ca3af;
  font-weight: 500;
}

.auth-debug-grid > span:nth-child(even) {
  color: #f3f4f6;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
}

/* List Layout */
.auth-debug-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.auth-debug-tag {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #10b981;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.auth-debug-empty {
  color: #6b7280;
  font-style: italic;
}

/* Code Display */
.auth-debug-code {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 4px;
  padding: 8px 12px;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  font-size: 11px;
  color: #10b981;
  overflow-x: auto;
  white-space: nowrap;
}

/* Measurements */
.auth-debug-measurements {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.auth-debug-measurements > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.auth-debug-measurements strong {
  color: #9ca3af;
  font-weight: 500;
}

.auth-debug-measurements span {
  font-family: 'SF Mono', Monaco, Consolas, monospace;
  color: #f3f4f6;
}

/* CSS Properties */
.auth-debug-props {
  max-height: 200px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(75, 85, 99, 0.2);
  border-radius: 4px;
  padding: 8px;
}

.auth-debug-props > div {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  font-size: 11px;
  border-bottom: 1px solid rgba(75, 85, 99, 0.1);
}

.auth-debug-props > div:last-child {
  border-bottom: none;
}

.auth-debug-prop-name {
  color: #9ca3af;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
}

.auth-debug-prop-value {
  color: #10b981;
  font-family: 'SF Mono', Monaco, Consolas, monospace;
}

/* Collapsed Section */
.auth-debug-collapsed {
  max-height: 300px;
  overflow: hidden;
  position: relative;
}

.auth-debug-collapsed::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(to bottom, transparent, rgba(10, 22, 40, 0.95));
  pointer-events: none;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .auth-debug-console {
    width: calc(100vw - 20px);
    max-height: calc(100vh - 20px);
    left: 10px;
    top: 10px;
  }
  
  .auth-debug-content {
    padding: 12px;
  }
  
  .auth-debug-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
}

/* Touch Device Support */
@media (hover: none) {
  .auth-debug-header {
    padding: 16px;
  }
  
  .auth-debug-controls button {
    padding: 8px;
    min-width: 32px;
    min-height: 32px;
  }
}