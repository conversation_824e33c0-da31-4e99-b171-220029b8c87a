export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      admin_users: {
        Row: {
          granted_at: string | null
          granted_by: string | null
          id: string
          is_active: boolean | null
          revoked_at: string | null
          user_id: string | null
        }
        Insert: {
          granted_at?: string | null
          granted_by?: string | null
          id?: string
          is_active?: boolean | null
          revoked_at?: string | null
          user_id?: string | null
        }
        Update: {
          granted_at?: string | null
          granted_by?: string | null
          id?: string
          is_active?: boolean | null
          revoked_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "admin_users_granted_by_fkey"
            columns: ["granted_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "admin_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      api_keys: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          key_hash: string
          key_preview: string
          last_used_at: string | null
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          key_hash: string
          key_preview: string
          last_used_at?: string | null
          name: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          key_hash?: string
          key_preview?: string
          last_used_at?: string | null
          name?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "api_keys_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      blocks: {
        Row: {
          content: string
          created_at: string
          deleted_at: string | null
          document_id: string
          extracted_tags: string[] | null
          file_path: string | null
          id: string
          language: string | null
          metadata: Json | null
          position: number
          search_vector: unknown | null
          type: string
          updated_at: string
          user_id: string | null
          version_of: string | null
        }
        Insert: {
          content: string
          created_at?: string
          deleted_at?: string | null
          document_id: string
          extracted_tags?: string[] | null
          file_path?: string | null
          id?: string
          language?: string | null
          metadata?: Json | null
          position: number
          search_vector?: unknown | null
          type: string
          updated_at?: string
          user_id?: string | null
          version_of?: string | null
        }
        Update: {
          content?: string
          created_at?: string
          deleted_at?: string | null
          document_id?: string
          extracted_tags?: string[] | null
          file_path?: string | null
          id?: string
          language?: string | null
          metadata?: Json | null
          position?: number
          search_vector?: unknown | null
          type?: string
          updated_at?: string
          user_id?: string | null
          version_of?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "blocks_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "blocks_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "blocks_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "blocks_version_of_fkey"
            columns: ["version_of"]
            isOneToOne: false
            referencedRelation: "blocks"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          created_at: string | null
          stripe_customer_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          stripe_customer_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          stripe_customer_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "customers_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      document_cache: {
        Row: {
          block_count: number | null
          cache_updated_at: string | null
          document_id: string
          last_block_update: string | null
          link_count: number | null
          tag_list: string[] | null
          total_content_length: number | null
        }
        Insert: {
          block_count?: number | null
          cache_updated_at?: string | null
          document_id: string
          last_block_update?: string | null
          link_count?: number | null
          tag_list?: string[] | null
          total_content_length?: number | null
        }
        Update: {
          block_count?: number | null
          cache_updated_at?: string | null
          document_id?: string
          last_block_update?: string | null
          link_count?: number | null
          tag_list?: string[] | null
          total_content_length?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "document_cache_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: true
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_cache_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: true
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
        ]
      }
      document_comments: {
        Row: {
          author_name: string | null
          block_id: string | null
          content: string
          created_at: string
          document_id: string
          id: string
          parent_id: string | null
          resolved: boolean | null
          resolved_at: string | null
          resolved_by: string | null
          share_id: string | null
          thread_id: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          author_name?: string | null
          block_id?: string | null
          content: string
          created_at?: string
          document_id: string
          id?: string
          parent_id?: string | null
          resolved?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          share_id?: string | null
          thread_id?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          author_name?: string | null
          block_id?: string | null
          content?: string
          created_at?: string
          document_id?: string
          id?: string
          parent_id?: string | null
          resolved?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          share_id?: string | null
          thread_id?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_comments_block_id_fkey"
            columns: ["block_id"]
            isOneToOne: false
            referencedRelation: "blocks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_comments_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_comments_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "document_comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_comments_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "document_comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      document_links: {
        Row: {
          created_at: string
          id: string
          source_document_id: string
          target_document_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          source_document_id: string
          target_document_id: string
        }
        Update: {
          created_at?: string
          id?: string
          source_document_id?: string
          target_document_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_links_source_document_id_fkey"
            columns: ["source_document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_links_source_document_id_fkey"
            columns: ["source_document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_links_target_document_id_fkey"
            columns: ["target_document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_links_target_document_id_fkey"
            columns: ["target_document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
        ]
      }
      document_share_users: {
        Row: {
          accepted_at: string | null
          email: string
          id: string
          invited_at: string
          share_id: string
          user_id: string | null
        }
        Insert: {
          accepted_at?: string | null
          email: string
          id?: string
          invited_at?: string
          share_id: string
          user_id?: string | null
        }
        Update: {
          accepted_at?: string | null
          email?: string
          id?: string
          invited_at?: string
          share_id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_share_users_share_id_fkey"
            columns: ["share_id"]
            isOneToOne: false
            referencedRelation: "document_shares"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_share_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      document_shares: {
        Row: {
          created_at: string
          created_by: string
          document_id: string
          expires_at: string | null
          id: string
          is_active: boolean | null
          max_views: number | null
          password_hash: string | null
          permissions: Database["public"]["Enums"]["share_permission"][]
          revoked_at: string | null
          settings: Json | null
          share_code: string
          share_type: Database["public"]["Enums"]["share_type"]
          updated_at: string
          view_count: number | null
        }
        Insert: {
          created_at?: string
          created_by: string
          document_id: string
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          max_views?: number | null
          password_hash?: string | null
          permissions?: Database["public"]["Enums"]["share_permission"][]
          revoked_at?: string | null
          settings?: Json | null
          share_code: string
          share_type?: Database["public"]["Enums"]["share_type"]
          updated_at?: string
          view_count?: number | null
        }
        Update: {
          created_at?: string
          created_by?: string
          document_id?: string
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          max_views?: number | null
          password_hash?: string | null
          permissions?: Database["public"]["Enums"]["share_permission"][]
          revoked_at?: string | null
          settings?: Json | null
          share_code?: string
          share_type?: Database["public"]["Enums"]["share_type"]
          updated_at?: string
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "document_shares_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "document_shares_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_shares_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
        ]
      }
      document_versions: {
        Row: {
          change_summary: string | null
          created_at: string
          created_by: string | null
          document_id: string
          id: string
          metadata: Json | null
          snapshot: Json
          title: string
          version_number: number
        }
        Insert: {
          change_summary?: string | null
          created_at?: string
          created_by?: string | null
          document_id: string
          id?: string
          metadata?: Json | null
          snapshot: Json
          title: string
          version_number: number
        }
        Update: {
          change_summary?: string | null
          created_at?: string
          created_by?: string | null
          document_id?: string
          id?: string
          metadata?: Json | null
          snapshot?: Json
          title?: string
          version_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "document_versions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "document_versions_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_versions_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          created_at: string
          deleted_at: string | null
          folder_id: string | null
          id: string
          is_project: boolean | null
          is_template: boolean | null
          metadata: Json | null
          position: number | null
          project_id: string | null
          search_vector: unknown | null
          share_settings: Json | null
          tags: string[] | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          folder_id?: string | null
          id?: string
          is_project?: boolean | null
          is_template?: boolean | null
          metadata?: Json | null
          position?: number | null
          project_id?: string | null
          search_vector?: unknown | null
          share_settings?: Json | null
          tags?: string[] | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          folder_id?: string | null
          id?: string
          is_project?: boolean | null
          is_template?: boolean | null
          metadata?: Json | null
          position?: number | null
          project_id?: string | null
          search_vector?: unknown | null
          share_settings?: Json | null
          tags?: string[] | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_folder_id_fkey"
            columns: ["folder_id"]
            isOneToOne: false
            referencedRelation: "folders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      folders: {
        Row: {
          color: string | null
          created_at: string | null
          icon: string | null
          id: string
          is_expanded: boolean | null
          is_favorite: boolean | null
          name: string
          parent_id: string | null
          path: string | null
          position: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          icon?: string | null
          id?: string
          is_expanded?: boolean | null
          is_favorite?: boolean | null
          name: string
          parent_id?: string | null
          path?: string | null
          position?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          icon?: string | null
          id?: string
          is_expanded?: boolean | null
          is_favorite?: boolean | null
          name?: string
          parent_id?: string | null
          path?: string | null
          position?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "folders_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "folders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "folders_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      images: {
        Row: {
          block_id: string | null
          cdn_url: string | null
          created_at: string
          data: string
          dimensions: Json | null
          document_id: string | null
          file_size: number | null
          id: string
          mime_type: string
          size_bytes: number
          storage_path: string | null
          user_id: string
        }
        Insert: {
          block_id?: string | null
          cdn_url?: string | null
          created_at?: string
          data: string
          dimensions?: Json | null
          document_id?: string | null
          file_size?: number | null
          id?: string
          mime_type: string
          size_bytes: number
          storage_path?: string | null
          user_id: string
        }
        Update: {
          block_id?: string | null
          cdn_url?: string | null
          created_at?: string
          data?: string
          dimensions?: Json | null
          document_id?: string | null
          file_size?: number | null
          id?: string
          mime_type?: string
          size_bytes?: number
          storage_path?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "images_block_id_fkey"
            columns: ["block_id"]
            isOneToOne: false
            referencedRelation: "blocks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "images_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "images_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "images_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      ip_rate_limit: {
        Row: {
          blocked_until: string | null
          endpoint: string
          ip_address: unknown
          request_count: number | null
          window_start: string | null
        }
        Insert: {
          blocked_until?: string | null
          endpoint?: string
          ip_address: unknown
          request_count?: number | null
          window_start?: string | null
        }
        Update: {
          blocked_until?: string | null
          endpoint?: string
          ip_address?: unknown
          request_count?: number | null
          window_start?: string | null
        }
        Relationships: []
      }
      pending_refreshes: {
        Row: {
          completed_at: string | null
          requested_at: string | null
          view_name: string
        }
        Insert: {
          completed_at?: string | null
          requested_at?: string | null
          view_name: string
        }
        Update: {
          completed_at?: string | null
          requested_at?: string | null
          view_name?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          display_name: string | null
          id: string
          settings: Json | null
          subscription_status: string | null
          subscription_tier: string | null
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          display_name?: string | null
          id: string
          settings?: Json | null
          subscription_status?: string | null
          subscription_tier?: string | null
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          display_name?: string | null
          id?: string
          settings?: Json | null
          subscription_status?: string | null
          subscription_tier?: string | null
          updated_at?: string
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      projects: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          document_count: number | null
          icon: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          document_count?: number | null
          icon?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          document_count?: number | null
          icon?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      query_performance_log: {
        Row: {
          created_at: string | null
          execution_time_ms: number
          id: string
          parameters: Json | null
          query_name: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          execution_time_ms: number
          id?: string
          parameters?: Json | null
          query_name: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          execution_time_ms?: number
          id?: string
          parameters?: Json | null
          query_name?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "query_performance_log_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      rate_limit_log: {
        Row: {
          action: string
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rate_limit_log_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      security_audit_log: {
        Row: {
          created_at: string | null
          details: Json | null
          event_type: string
          id: string
          ip_address: unknown | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          event_type: string
          id?: string
          ip_address?: unknown | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          event_type?: string
          id?: string
          ip_address?: unknown | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "security_audit_log_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      settings: {
        Row: {
          created_at: string
          id: string
          key: string
          updated_at: string
          user_id: string
          value: Json | null
        }
        Insert: {
          created_at?: string
          id?: string
          key: string
          updated_at?: string
          user_id: string
          value?: Json | null
        }
        Update: {
          created_at?: string
          id?: string
          key?: string
          updated_at?: string
          user_id?: string
          value?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "settings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      share_access_log: {
        Row: {
          accessed_at: string | null
          accessed_by: string | null
          id: string
          ip_address: unknown | null
          share_link_id: string
          user_agent: string | null
        }
        Insert: {
          accessed_at?: string | null
          accessed_by?: string | null
          id?: string
          ip_address?: unknown | null
          share_link_id: string
          user_agent?: string | null
        }
        Update: {
          accessed_at?: string | null
          accessed_by?: string | null
          id?: string
          ip_address?: unknown | null
          share_link_id?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "share_access_log_accessed_by_fkey"
            columns: ["accessed_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "share_access_log_share_link_id_fkey"
            columns: ["share_link_id"]
            isOneToOne: false
            referencedRelation: "share_analytics"
            referencedColumns: ["share_link_id"]
          },
          {
            foreignKeyName: "share_access_log_share_link_id_fkey"
            columns: ["share_link_id"]
            isOneToOne: false
            referencedRelation: "share_links"
            referencedColumns: ["id"]
          },
        ]
      }
      share_access_logs: {
        Row: {
          accessed_at: string
          accessed_by: string | null
          action: string
          anonymous_id: string | null
          document_id: string
          id: string
          ip_address: unknown | null
          metadata: Json | null
          share_id: string
          user_agent: string | null
        }
        Insert: {
          accessed_at?: string
          accessed_by?: string | null
          action: string
          anonymous_id?: string | null
          document_id: string
          id?: string
          ip_address?: unknown | null
          metadata?: Json | null
          share_id: string
          user_agent?: string | null
        }
        Update: {
          accessed_at?: string
          accessed_by?: string | null
          action?: string
          anonymous_id?: string | null
          document_id?: string
          id?: string
          ip_address?: unknown | null
          metadata?: Json | null
          share_id?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "share_access_logs_accessed_by_fkey"
            columns: ["accessed_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "share_access_logs_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "share_access_logs_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "share_access_logs_share_id_fkey"
            columns: ["share_id"]
            isOneToOne: false
            referencedRelation: "document_shares"
            referencedColumns: ["id"]
          },
        ]
      }
      share_links: {
        Row: {
          created_at: string | null
          created_by: string
          document_id: string
          expires_at: string | null
          id: string
          is_active: boolean | null
          last_accessed_at: string | null
          max_uses: number | null
          password_hash: string | null
          permission: string
          token: string
          use_count: number | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          document_id: string
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          last_accessed_at?: string | null
          max_uses?: number | null
          password_hash?: string | null
          permission?: string
          token: string
          use_count?: number | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          document_id?: string
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          last_accessed_at?: string | null
          max_uses?: number | null
          password_hash?: string | null
          permission?: string
          token?: string
          use_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "share_links_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "share_links_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "share_links_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          created_at: string | null
          current_period_end: string
          id: string
          price_id: string
          status: string
          tier: string
          trial_end: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end: string
          id: string
          price_id: string
          status: string
          tier: string
          trial_end?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: string
          id?: string
          price_id?: string
          status?: string
          tier?: string
          trial_end?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_rate_limit: {
        Row: {
          blocked_until: string | null
          endpoint: string
          request_count: number | null
          user_id: string
          window_start: string | null
        }
        Insert: {
          blocked_until?: string | null
          endpoint?: string
          request_count?: number | null
          user_id: string
          window_start?: string | null
        }
        Update: {
          blocked_until?: string | null
          endpoint?: string
          request_count?: number | null
          user_id?: string
          window_start?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_rate_limit_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_sessions: {
        Row: {
          created_at: string | null
          expires_at: string
          id: string
          ip_address: unknown | null
          last_activity: string | null
          session_token: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          expires_at: string
          id?: string
          ip_address?: unknown | null
          last_activity?: string | null
          session_token: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          expires_at?: string
          id?: string
          ip_address?: unknown | null
          last_activity?: string | null
          session_token?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_dashboard_stats"
            referencedColumns: ["user_id"]
          },
        ]
      }
    }
    Views: {
      share_analytics: {
        Row: {
          created_at: string | null
          document_id: string | null
          expires_at: string | null
          last_accessed: string | null
          permission: string | null
          share_link_id: string | null
          token: string | null
          total_views: number | null
          unique_visitors: number | null
          use_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "share_links_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "share_links_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "user_documents_with_block_count"
            referencedColumns: ["id"]
          },
        ]
      }
      user_dashboard_stats: {
        Row: {
          block_count: number | null
          document_count: number | null
          last_activity: string | null
          template_count: number | null
          total_content_size: number | null
          total_images: number | null
          total_links: number | null
          unique_tags: number | null
          user_id: string | null
        }
        Relationships: []
      }
      user_documents_with_block_count: {
        Row: {
          block_count: number | null
          created_at: string | null
          id: string | null
          is_template: boolean | null
          preview: string | null
          tags: string[] | null
          title: string | null
          total_content_length: number | null
          updated_at: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      batch_delete_blocks: {
        Args: { block_ids: string[] }
        Returns: number
      }
      batch_insert_blocks: {
        Args: { blocks: Json; doc_id: string }
        Returns: {
          error: string
          id: string
          position: number
          success: boolean
        }[]
      }
      batch_update_blocks: {
        Args: { updates: Json }
        Returns: {
          block_id: string
          error: string
          success: boolean
        }[]
      }
      check_data_integrity: {
        Args: Record<PropertyKey, never>
        Returns: {
          check_name: string
          details: Json
          status: string
        }[]
      }
      check_ip_rate_limit: {
        Args: { p_ip: unknown; p_limit?: number; p_window_minutes?: number }
        Returns: boolean
      }
      check_rate_limit: {
        Args:
          | {
              p_action: string
              p_limit?: number
              p_user_id: string
              p_window_minutes?: number
            }
          | { p_endpoint?: string; p_ip_address: unknown; p_user_id?: string }
        Returns: Json
      }
      check_share_access: {
        Args:
          | { p_password?: string; p_share_code: string; p_user_id?: string }
          | { p_password?: string; p_token: string }
        Returns: {
          document_id: string
          has_access: boolean
          message: string
          permissions: Database["public"]["Enums"]["share_permission"][]
          requires_password: boolean
          share_id: string
        }[]
      }
      check_trial_status: {
        Args: { p_user_id: string }
        Returns: Json
      }
      check_user_access: {
        Args: { p_user_id: string; required_tier: string }
        Returns: boolean
      }
      clean_user_metadata: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_expired_sessions: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_old_backups: {
        Args: { p_days_to_keep?: number }
        Returns: string
      }
      cleanup_rate_limit_log: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_data_backup: {
        Args: { p_reason?: string }
        Returns: string
      }
      create_document_version: {
        Args: { doc_id: string; summary?: string }
        Returns: {
          change_summary: string | null
          created_at: string
          created_by: string | null
          document_id: string
          id: string
          metadata: Json | null
          snapshot: Json
          title: string
          version_number: number
        }
      }
      create_document_with_folder_check: {
        Args: {
          p_folder_id?: string
          p_id: string
          p_is_template?: boolean
          p_metadata?: Json
          p_position?: number
          p_preview?: string
          p_tags?: string[]
          p_title: string
        }
        Returns: {
          created_at: string
          deleted_at: string | null
          folder_id: string | null
          id: string
          is_project: boolean | null
          is_template: boolean | null
          metadata: Json | null
          position: number | null
          project_id: string | null
          search_vector: unknown | null
          share_settings: Json | null
          tags: string[] | null
          title: string
          updated_at: string
          user_id: string
        }
      }
      decrement_block_positions: {
        Args: { p_deleted_position: number; p_document_id: string }
        Returns: undefined
      }
      extend_trial: {
        Args: { p_days: number; p_user_id: string }
        Returns: Json
      }
      extract_tags_from_content: {
        Args: { content: string }
        Returns: string[]
      }
      generate_share_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_share_token: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_block_count: {
        Args: { p_document_id: string }
        Returns: number
      }
      get_blocks_for_documents: {
        Args: { p_document_ids: string[]; p_user_id?: string }
        Returns: {
          blocks: Json
          document_id: string
        }[]
      }
      get_blocks_paginated: {
        Args: { p_document_id: string; p_limit?: number; p_offset?: number }
        Returns: {
          blocks: Json
          total_count: number
        }[]
      }
      get_database_size: {
        Args: Record<PropertyKey, never>
        Returns: {
          size_bytes: number
          size_pretty: string
        }[]
      }
      get_documents_in_folder: {
        Args: {
          p_folder_id: string
          p_limit?: number
          p_offset?: number
          p_recursive?: boolean
        }
        Returns: {
          block_count: number
          created_at: string
          folder_id: string
          folder_name: string
          folder_path: string
          has_ai: boolean
          has_code: boolean
          id: string
          position: number
          preview: string
          tags: string[]
          title: string
          updated_at: string
        }[]
      }
      get_documents_with_stats: {
        Args:
          | {
              p_limit?: number
              p_offset?: number
              p_order_by?: string
              p_order_dir?: string
              p_user_id?: string
            }
          | { p_limit?: number; p_offset?: number; p_user_id: string }
        Returns: {
          block_count: number
          created_at: string
          has_ai: boolean
          has_code: boolean
          id: string
          last_block_type: string
          preview: string
          project_color: string
          project_icon: string
          project_id: string
          project_title: string
          tags: string[]
          title: string
          total_length: number
          updated_at: string
          version: number
        }[]
      }
      get_folder_tree: {
        Args: { p_user_id: string }
        Returns: {
          color: string
          created_at: string
          document_count: number
          icon: string
          id: string
          is_expanded: boolean
          is_favorite: boolean
          level: number
          name: string
          parent_id: string
          path: string
          position: number
          total_size: number
          updated_at: string
        }[]
      }
      get_image_by_path: {
        Args: { p_storage_path: string }
        Returns: {
          cdn_url: string
          content: string
          created_at: string
          dimensions: Json
          file_size: number
          id: string
          storage_path: string
          user_id: string
        }[]
      }
      get_mcp_user_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_projects_with_stats: {
        Args: { p_user_id: string }
        Returns: {
          color: string
          created_at: string
          description: string
          document_count: number
          icon: string
          id: string
          last_document_date: string
          title: string
          updated_at: string
        }[]
      }
      get_shared_document: {
        Args: { p_password?: string; p_share_code: string }
        Returns: {
          created_at: string
          id: string
          metadata: Json
          permissions: Database["public"]["Enums"]["share_permission"][]
          share_settings: Json
          tags: string[]
          title: string
          updated_at: string
          user_id: string
        }[]
      }
      get_shared_document_blocks: {
        Args: { p_document_id: string; p_share_code: string }
        Returns: {
          content: string
          created_at: string
          document_id: string
          id: string
          metadata: Json
          position: number
          type: string
          updated_at: string
        }[]
      }
      get_shared_document_profile: {
        Args: { p_user_id: string }
        Returns: {
          avatar_url: string
          display_name: string
          username: string
        }[]
      }
      get_shared_documents: {
        Args: { p_user_id: string }
        Returns: {
          document_id: string
          expires_at: string
          permissions: Database["public"]["Enums"]["share_permission"][]
          share_type: Database["public"]["Enums"]["share_type"]
          shared_at: string
          shared_by: string
          title: string
        }[]
      }
      get_user_data_size: {
        Args: { p_user_id?: string }
        Returns: {
          blocks_count: number
          blocks_size_bytes: number
          documents_count: number
          documents_size_bytes: number
          images_count: number
          images_size_bytes: number
          total_size_bytes: number
          total_size_pretty: string
        }[]
      }
      get_user_document_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          avg_blocks_per_document: number
          newest_document: string
          oldest_document: string
          total_blocks: number
          total_documents: number
          total_tags: number
          total_templates: number
        }[]
      }
      get_user_subscription_status: {
        Args: { p_user_id: string }
        Returns: {
          current_period_end: string
          has_subscription: boolean
          status: string
          tier: string
          trial_end: string
        }[]
      }
      get_version_diff: {
        Args: { version1_id: string; version2_id: string }
        Returns: Json
      }
      is_admin: {
        Args: { p_user_id?: string }
        Returns: boolean
      }
      is_password_compromised: {
        Args: { password_hash: string }
        Returns: boolean
      }
      list_available_backups: {
        Args: Record<PropertyKey, never>
        Returns: {
          backup_date: string
          backup_suffix: string
          is_complete: boolean
          reason: string
          table_counts: Json
        }[]
      }
      log_query_performance: {
        Args: {
          p_parameters?: Json
          p_query_name: string
          p_start_time: string
        }
        Returns: undefined
      }
      log_security_event: {
        Args: { p_details?: Json; p_event_type: string; p_user_id: string }
        Returns: undefined
      }
      log_share_access: {
        Args: {
          p_action: string
          p_anonymous_id?: string
          p_document_id: string
          p_ip_address?: string
          p_metadata?: Json
          p_share_id: string
          p_user_agent?: string
          p_user_id?: string
        }
        Returns: undefined
      }
      mcp_add_block: {
        Args: {
          p_api_key: string
          p_content: string
          p_document_id: string
          p_metadata?: Json
          p_position?: number
          p_type: string
        }
        Returns: Json
      }
      mcp_create_document: {
        Args: {
          p_api_key: string
          p_folder_id?: string
          p_tags?: string[]
          p_title: string
        }
        Returns: Json
      }
      mcp_create_folder: {
        Args: {
          p_api_key: string
          p_color?: string
          p_icon?: string
          p_name: string
          p_parent_id?: string
        }
        Returns: Json
      }
      mcp_delete_folder: {
        Args: { p_api_key: string; p_folder_id: string; p_recursive?: boolean }
        Returns: Json
      }
      mcp_edit_ai_block: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_operation: string
          p_params: Json
        }
        Returns: Json
      }
      mcp_edit_code_block: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_operation: string
          p_params: Json
        }
        Returns: Json
      }
      mcp_edit_table_block: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_operation: string
          p_params: Json
        }
        Returns: Json
      }
      mcp_edit_text_block: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_operation: string
          p_params: Json
        }
        Returns: Json
      }
      mcp_edit_todo_block: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_operation: string
          p_params: Json
        }
        Returns: Json
      }
      mcp_get_blocks_range: {
        Args: {
          p_api_key: string
          p_document_id: string
          p_end_position: number
          p_include_context?: boolean
          p_start_position: number
        }
        Returns: Json
      }
      mcp_get_document: {
        Args: { p_api_key: string; p_document_id: string; p_semantic?: boolean }
        Returns: Json
      }
      mcp_get_folder_contents: {
        Args: {
          p_api_key: string
          p_folder_id?: string
          p_include_subfolders?: boolean
        }
        Returns: Json
      }
      mcp_insert_blocks_at: {
        Args: {
          p_api_key: string
          p_blocks: Json
          p_document_id: string
          p_position: number
          p_shift_mode?: string
        }
        Returns: Json
      }
      mcp_list_folders: {
        Args: { p_api_key: string; p_parent_id?: string; p_recursive?: boolean }
        Returns: Json
      }
      mcp_move_document: {
        Args: {
          p_api_key: string
          p_document_id: string
          p_folder_id?: string
          p_position?: number
        }
        Returns: Json
      }
      mcp_search_blocks: {
        Args: {
          p_api_key: string
          p_block_type?: string
          p_document_id: string
          p_limit?: number
          p_offset?: number
          p_search_query?: string
        }
        Returns: Json
      }
      mcp_search_documents: {
        Args: { p_api_key: string; p_limit?: number; p_query?: string }
        Returns: Json
      }
      mcp_smart_edit_block: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_operation: string
          p_params: Json
        }
        Returns: Json
      }
      mcp_transform_block_type: {
        Args: {
          p_api_key: string
          p_block_id: string
          p_new_type: string
          p_options?: Json
        }
        Returns: Json
      }
      mcp_update_document: {
        Args: {
          p_api_key: string
          p_blocks?: Json
          p_document_id: string
          p_tags?: string[]
          p_title?: string
        }
        Returns: Json
      }
      mcp_update_folder: {
        Args: {
          p_api_key: string
          p_color?: string
          p_folder_id: string
          p_icon?: string
          p_is_favorite?: boolean
          p_name?: string
          p_parent_id?: string
        }
        Returns: Json
      }
      mcp_user_document_ids: {
        Args: Record<PropertyKey, never>
        Returns: string[]
      }
      move_folder: {
        Args: {
          p_folder_id: string
          p_new_parent_id: string
          p_new_position?: number
        }
        Returns: boolean
      }
      rebuild_user_caches: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      recover_deleted_blocks: {
        Args: { p_deleted_after?: string; p_document_id: string }
        Returns: number
      }
      refresh_stale_caches: {
        Args: { stale_hours?: number }
        Returns: number
      }
      refresh_user_dashboard_stats: {
        Args: { p_user_id?: string }
        Returns: undefined
      }
      restore_document: {
        Args: { doc_id: string }
        Returns: undefined
      }
      restore_document_version: {
        Args: { version_id: string }
        Returns: undefined
      }
      restore_from_backup: {
        Args: { p_backup_suffix: string }
        Returns: {
          restored_count: number
          status: string
          table_name: string
        }[]
      }
      save_document_blocks: {
        Args: { blocks: Json; doc_id: string }
        Returns: undefined
      }
      save_document_blocks_atomic: {
        Args: { p_blocks: Json; p_document_id: string; p_user_id?: string }
        Returns: Json
      }
      save_document_blocks_old: {
        Args: { blocks: Json; doc_id: string }
        Returns: undefined
      }
      save_document_blocks_safe: {
        Args: { blocks: Json; doc_id: string }
        Returns: undefined
      }
      save_document_blocks_v2: {
        Args: { p_blocks: Json; p_document_id: string }
        Returns: Json
      }
      save_document_blocks_v3: {
        Args: { p_blocks: Json; p_document_id: string }
        Returns: Json
      }
      save_document_blocks_with_rate_limit: {
        Args: { p_blocks: Json; p_document_id: string }
        Returns: Json
      }
      save_document_with_blocks_atomic: {
        Args: {
          p_blocks?: Json
          p_document_id: string
          p_folder_id?: string
          p_metadata?: Json
          p_tags?: string[]
          p_title: string
          p_user_id: string
        }
        Returns: Json
      }
      save_image_metadata: {
        Args: {
          p_cdn_url: string
          p_dimensions: Json
          p_file_size: number
          p_storage_path: string
        }
        Returns: string
      }
      search_documents_optimized: {
        Args: {
          p_limit?: number
          p_offset?: number
          p_query: string
          p_tags?: string[]
          p_user_id?: string
        }
        Returns: {
          block_count: number
          id: string
          preview: string
          score: number
          tags: string[]
          title: string
          updated_at: string
        }[]
      }
      soft_delete_document: {
        Args: { p_document_id: string }
        Returns: boolean
      }
      update_api_key_last_used: {
        Args: { p_key_hash: string }
        Returns: undefined
      }
      update_document_cache: {
        Args: { doc_id: string }
        Returns: undefined
      }
      user_document_ids: {
        Args: Record<PropertyKey, never>
        Returns: {
          document_id: string
        }[]
      }
      user_owns_document: {
        Args: { doc_id: string }
        Returns: boolean
      }
      validate_mcp_api_key: {
        Args: { p_api_key: string }
        Returns: {
          is_valid: boolean
          key_name: string
          user_id: string
        }[]
      }
      validate_password_strength: {
        Args: { password: string }
        Returns: Json
      }
      validate_session: {
        Args: { p_token: string }
        Returns: string
      }
    }
    Enums: {
      share_permission: "view" | "comment" | "edit" | "download"
      share_type: "link" | "user" | "team" | "public"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      share_permission: ["view", "comment", "edit", "download"],
      share_type: ["link", "user", "team", "public"],
    },
  },
} as const
