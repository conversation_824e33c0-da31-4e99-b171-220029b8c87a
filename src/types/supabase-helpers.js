/**
 * @fileoverview Type helpers and utilities for Supabase database operations
 * Auto-generated from database schema - DO NOT EDIT MANUALLY
 * Generated on: 2025-08-14T12:54:57.410Z
 * 
 * This file provides helper functions and utilities for working with Supabase.
 * TypeScript definitions are available in database.types.ts for IDE support.
 */

// Import type definitions for JSDoc references
// VS Code and other IDEs will use these for autocompletion
/** @typedef {import('./database.types').Database} Database */
/** @typedef {import('./database.types').Tables<'documents'>} DocumentsTable */
/** @typedef {import('./database.types').Tables<'blocks'>} BlocksTable */
/** @typedef {import('./database.types').Tables<'profiles'>} ProfilesTable */



// ====================
// Type Aliases
// ====================

/**
 * Type aliases for easier access
 * @typedef {DocumentsTable['Row']} Document
 * @typedef {DocumentsTable['Insert']} DocumentInsert
 * @typedef {DocumentsTable['Update']} DocumentUpdate
 * @typedef {BlocksTable['Row']} Block
 * @typedef {BlocksTable['Insert']} BlockInsert
 * @typedef {BlocksTable['Update']} BlockUpdate
 * @typedef {ProfilesTable['Row']} Profile
 * @typedef {ProfilesTable['Insert']} ProfileInsert
 * @typedef {ProfilesTable['Update']} ProfileUpdate
 */

// ====================
// Query Helpers
// ====================

/**
 * Get all documents for the current user
 * @param {import('@supabase/supabase-js').SupabaseClient} supabase
 * @returns {Promise<{data: Document[] | null, error: Error | null}>}
 */
export async function getDocuments(supabase) {
  return await supabase
    .from('documents')
    .select('*')
    .order('updated_at', { ascending: false });
}

/**
 * Get a single document with its blocks
 * @param {import('@supabase/supabase-js').SupabaseClient} supabase
 * @param {string} documentId
 * @returns {Promise<{data: {document: Document, blocks: Block[]} | null, error: Error | null}>}
 */
export async function getDocumentWithBlocks(supabase, documentId) {
  const { data: document, error: docError } = await supabase
    .from('documents')
    .select('*')
    .eq('id', documentId)
    .single();
    
  if (docError) return { data: null, error: docError };
  
  const { data: blocks, error: blocksError } = await supabase
    .from('blocks')
    .select('*')
    .eq('document_id', documentId)
    .order('position');
    
  if (blocksError) return { data: null, error: blocksError };
  
  return { data: { document, blocks }, error: null };
}

/**
 * Get blocks for a document
 * @param {import('@supabase/supabase-js').SupabaseClient} supabase
 * @param {string} documentId
 * @returns {Promise<{data: Block[] | null, error: Error | null}>}
 */
export async function getBlocks(supabase, documentId) {
  return await supabase
    .from('blocks')
    .select('*')
    .eq('document_id', documentId)
    .order('position');
}

/**
 * Create a new document
 * @param {import('@supabase/supabase-js').SupabaseClient} supabase
 * @param {Partial<Document>} document
 * @returns {Promise<{data: Document | null, error: Error | null}>}
 */
export async function createDocument(supabase, document) {
  return await supabase
    .from('documents')
    .insert(document)
    .select()
    .single();
}

/**
 * Update a document
 * @param {import('@supabase/supabase-js').SupabaseClient} supabase
 * @param {string} documentId
 * @param {Partial<Document>} updates
 * @returns {Promise<{data: Document | null, error: Error | null}>}
 */
export async function updateDocument(supabase, documentId, updates) {
  return await supabase
    .from('documents')
    .update(updates)
    .eq('id', documentId)
    .select()
    .single();
}

// ====================
// Form Validation Schemas
// ====================

/**
 * Document validation schema based on database constraints
 */
export const documentSchema = {
  title: { 
    type: 'string', 
    maxLength: 255, 
    required: true,
    minLength: 1,
    pattern: /^[^\s].*[^\s]$/, // No leading/trailing whitespace
  },
  tags: { 
    type: 'array', 
    items: 'string',
    maxItems: 20,
    uniqueItems: true
  },
  folder_id: { 
    type: 'string', 
    format: 'uuid', 
    nullable: true 
  },
  is_template: {
    type: 'boolean',
    default: false
  }
};

/**
 * Block validation schema based on database constraints
 */
export const blockSchema = {
  type: { 
    type: 'enum', 
    values: [
      'text', 'code', 'heading', 'ai', 'table', 
      'filetree', 'todo', 'image', 'inline-image', 
      'version-track', 'issue-tracker'
    ],
    required: true 
  },
  content: { 
    type: 'string', 
    required: true,
    maxLength: 1000000 // 1MB limit for content
  },
  metadata: { 
    type: 'object', 
    default: {},
    properties: {
      language: { type: 'string' }, // For code blocks
      level: { type: 'number', min: 1, max: 6 }, // For heading blocks
      checked: { type: 'boolean' }, // For todo blocks
      images: { type: 'array' }, // For image blocks
    }
  },
  position: { 
    type: 'number', 
    required: true,
    min: 0
  }
};

/**
 * User profile validation schema
 */
export const profileSchema = {
  username: {
    type: 'string',
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9_-]+$/,
    required: false
  },
  full_name: {
    type: 'string',
    maxLength: 255,
    required: false
  },
  avatar_url: {
    type: 'string',
    format: 'url',
    required: false
  }
};

// ====================
// Block Type Constants
// ====================

export const BLOCK_TYPES = {
  TEXT: 'text',
  CODE: 'code',
  HEADING: 'heading',
  AI: 'ai',
  TABLE: 'table',
  FILETREE: 'filetree',
  TODO: 'todo',
  IMAGE: 'image',
  INLINE_IMAGE: 'inline-image',
  VERSION_TRACK: 'version-track',
  ISSUE_TRACKER: 'issue-tracker'
};

// ====================
// Error Types
// ====================

/**
 * @typedef {Object} SupabaseError
 * @property {string} message - Error message
 * @property {string} code - Error code (e.g., 'PGRST116')
 * @property {string} details - Detailed error information
 * @property {string} hint - Suggestion for fixing the error
 */

/**
 * Common Supabase error codes
 */
export const ERROR_CODES = {
  NOT_FOUND: 'PGRST116',
  UNIQUE_VIOLATION: '23505',
  FOREIGN_KEY_VIOLATION: '23503',
  CHECK_VIOLATION: '23514',
  NOT_NULL_VIOLATION: '23502',
  PERMISSION_DENIED: '42501'
};

// ====================
// Utility Functions
// ====================

/**
 * Check if an error is a specific Supabase error
 * @param {Error | SupabaseError} error
 * @param {string} code
 * @returns {boolean}
 */
export function isSupabaseError(error, code) {
  return error?.code === code;
}

/**
 * Format error message for user display
 * @param {Error | SupabaseError} error
 * @returns {string}
 */
export function formatErrorMessage(error) {
  if (!error) return 'An unknown error occurred';
  
  // Handle specific error codes
  switch (error.code) {
    case ERROR_CODES.NOT_FOUND:
      return 'The requested item was not found';
    case ERROR_CODES.UNIQUE_VIOLATION:
      return 'This item already exists';
    case ERROR_CODES.PERMISSION_DENIED:
      return 'You do not have permission to perform this action';
    default:
      return error.message || 'An error occurred';
  }
}
