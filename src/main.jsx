import "./instrument"; // Import Sentry first for early initialization
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import * as Sentry from '@sentry/react'
import './index.css'
import './styles/hero-knowledge-constellation.css'
import App from './App.jsx'

// Initialize Honeycomb observability before React
import { initializeHoneycomb } from './utils/honeycomb'
const honeycombSDK = initializeHoneycomb();

// Initialize global auto-save manager immediately
import './utils/globalAutoSave'

// Initialize Zustand stores and debugging tools
import './stores'

// Load migration test utilities in development
if (import.meta.env.DEV) {
  import('./utils/authMigrationTest');
  import('./utils/testAuthMigration');
  import('./utils/testExpandedViewMigration');
  import('./utils/migrationScanner');
  import('./utils/testTextBlockMigration');
  
  // Initialize system monitor for AI #2
  import('./utils/systemMonitor').then(module => {
    console.log('🚀 System Monitor initialized for AI #2');
  });
  
  // Load critical tests and performance optimizer
  import('./tests/criticalPaths.test');
  import('./utils/performanceOptimizer');
  import('./utils/finalPrePushCheck');
}

const container = document.getElementById('root');
const root = createRoot(container, {
  // React 19 Error Hooks Integration with Sentry
  onUncaughtError: Sentry.reactErrorHandler(),
  onCaughtError: Sentry.reactErrorHandler(),
  onRecoverableError: Sentry.reactErrorHandler(),
});

root.render(
  <StrictMode>
    <App />
  </StrictMode>,
)
