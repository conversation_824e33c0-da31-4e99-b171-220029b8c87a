import { useEffect } from 'react';
import useSettingsStore from '../stores/settingsStore';
import useAuthStore from '../stores/authStore';

/**
 * Compatibility Provider for SettingsContext migration
 * This component initializes the settings store but doesn't provide a context
 * It's a temporary bridge during migration
 */
export function SettingsProvider({ children }) {
  const user = useAuthStore((state) => state.user);
  const initializeSettings = useSettingsStore((state) => state.initializeSettings);
  
  // Initialize settings when component mounts or user changes
  useEffect(() => {
    initializeSettings(user);
  }, [user, initializeSettings]);
  
  // Just render children - no context provider needed with Zustand
  return children;
}

/**
 * For components that still expect a SettingsContext.Provider
 * This is just an alias that does nothing but render children
 */
export const SettingsContext = {
  Provider: SettingsProvider
};