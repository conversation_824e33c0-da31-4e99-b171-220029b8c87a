import React, { createContext, useContext, useEffect } from 'react';
import useAuthStore from '../stores/authStore';
import { supabase } from '../lib/supabaseOptimized';

/**
 * Compatibility layer for gradual migration from AuthContext to authStore
 * This provides the exact same API as AuthContextOptimized
 */

const AuthContext = createContext({});

export function AuthProviderOptimized({ children }) {
  const authStore = useAuthStore();
  
  // Initialize auth on mount
  useEffect(() => {
    authStore.initialize();
    return () => authStore.cleanup();
  }, []);

  // Provide the exact same context value structure as original
  const contextValue = {
    user: authStore.user,
    loading: authStore.loading,
    error: authStore.error,
    signIn: authStore.signIn,
    signUp: authStore.signUp,
    signOut: authStore.signOut,
    supabase
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook with same error handling as original
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Temporary export to maintain compatibility during migration
export { AuthProviderOptimized as AuthProvider };