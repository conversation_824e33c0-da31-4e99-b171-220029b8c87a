import { useEffect } from 'react';
import useUIStore from '../stores/uiStore';

/**
 * Compatibility Provider for SidebarContext migration
 * This component initializes the sidebar state but doesn't provide a context
 * It's a temporary bridge during migration
 */
export function SidebarProvider({ children }) {
  const { setSidebarOpen, toggleSidebar } = useUIStore();
  
  // Initialize from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    if (saved !== null) {
      setSidebarOpen(saved !== 'true');
    }
  }, [setSidebarOpen]);
  
  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Cmd/Ctrl + B to toggle sidebar
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        toggleSidebar();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleSidebar]);
  
  // Just render children - no context provider needed with Zustand
  return children;
}

/**
 * For components that still expect a SidebarContext.Provider
 * This is just an alias that does nothing but render children
 */
export const SidebarContext = {
  Provider: SidebarProvider
};