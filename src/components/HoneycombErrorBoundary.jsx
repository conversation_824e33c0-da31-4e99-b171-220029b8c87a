import React from 'react';
import { recordException } from '../utils/honeycomb';

/**
 * Error Boundary with Honeycomb integration
 * Catches React errors and sends them to Honeycomb for analysis
 */
class HoneycombErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null,
      errorCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Record exception to Honey<PERSON> with full context
    recordException(error, {
      'error.boundary': true,
      'component.stack': errorInfo.componentStack,
      'error.type': error.name,
      'error.message': error.message,
      'error.stack': error.stack,
      'page.url': window.location.href,
      'page.path': window.location.pathname,
      'user.agent': navigator.userAgent,
      'timestamp': new Date().toISOString(),
    });
    
    // Also send to Sentry if available
    if (window.Sentry) {
      window.Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack
          }
        }
      });
    }
    
    // Update state with error details
    this.setState(prevState => ({
      error,
      errorInfo,
      errorCount: prevState.errorCount + 1
    }));
  }

  handleReset = () => {
    // Record recovery attempt
    recordException(new Error('User attempted error recovery'), {
      'error.recovery': true,
      'error.count': this.state.errorCount,
      'original.error': this.state.error?.message,
    });
    
    // Reset error state
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
    
    // Optionally reload the page if errors keep happening
    if (this.state.errorCount > 3) {
      window.location.reload();
    }
  };

  render() {
    if (this.state.hasError) {
      // Fallback UI when error occurs
      return (
        <div className="min-h-screen bg-dark-primary flex items-center justify-center p-8">
          <div className="max-w-2xl w-full bg-dark-secondary/50 backdrop-blur-lg rounded-2xl p-8 border border-dark-secondary">
            <div className="text-center">
              <div className="text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-text-primary mb-4">
                Something went wrong
              </h1>
              <p className="text-text-secondary mb-6">
                We've encountered an unexpected error. The error has been reported 
                and we'll look into it. You can try to recover or reload the page.
              </p>
              
              {/* Show error details in development */}
              {import.meta.env.DEV && this.state.error && (
                <details className="text-left mb-6 p-4 bg-dark-primary/50 rounded-lg">
                  <summary className="cursor-pointer text-text-secondary hover:text-text-primary mb-2">
                    Error Details (Development Only)
                  </summary>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-text-secondary">Error:</span>{' '}
                      <span className="text-red-400 font-mono">
                        {this.state.error.toString()}
                      </span>
                    </div>
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <span className="text-text-secondary">Component Stack:</span>
                        <pre className="text-xs text-text-tertiary mt-1 overflow-x-auto">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
              
              <div className="flex gap-4 justify-center">
                <button
                  onClick={this.handleReset}
                  className="px-6 py-2 bg-accent-primary text-white rounded-lg hover:bg-accent-primary/80 transition-colors"
                >
                  Try to Recover
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-2 bg-dark-primary text-text-secondary rounded-lg hover:bg-dark-primary/80 transition-colors"
                >
                  Reload Page
                </button>
              </div>
              
              {this.state.errorCount > 1 && (
                <p className="text-text-tertiary text-sm mt-4">
                  Error count: {this.state.errorCount}
                </p>
              )}
            </div>
          </div>
        </div>
      );
    }

    // Normal render when no error
    return this.props.children;
  }
}

export default HoneycombErrorBoundary;