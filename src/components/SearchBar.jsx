import { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';

const SearchBar = forwardRef(({ value, onChange }, ref) => {
  const debounceTimer = useRef(null);
  const inputRef = useRef(null);
  
  // Expose focus method to parent
  useImperativeHandle(ref, () => ({
    focus: () => {
      inputRef.current?.focus();
      inputRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }));
  
  // Emit search event after user stops typing
  useEffect(() => {
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }
    
    if (value) {
      debounceTimer.current = setTimeout(() => {
        eventBus.emit(EVENT_TYPES.SEARCH_PERFORMED, { 
          query: value,
          timestamp: Date.now()
        });
      }, 500); // 500ms debounce
    }
    
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [value]);
  
  return (
    <div className="relative flex-grow">
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Search your journey..."
        className="w-full bg-dark-secondary/40 border border-dark-secondary/50 
                   rounded py-2.5 md:py-2 pl-3 pr-3 text-text-primary text-sm
                   placeholder-text-secondary/60 focus:outline-none 
                   focus:border-accent-green/50 focus:bg-dark-secondary/50 
                   transition-all hover:bg-dark-secondary/50 min-h-[44px] md:min-h-0"
      />
    </div>
  );
});

SearchBar.displayName = 'SearchBar';

export default SearchBar;
