/* Issue Tracker Block - Minimal Timeline UI */
/* Inspired by subway maps and circuit diagrams for maximum clarity */

/* CSS Custom Properties for consistent theming */
:root {
  --timeline-bg: #0f1729;
  --timeline-surface: #1a2638;
  --timeline-input-bg: #0d1a2d;
  --timeline-text-primary: #f3f4f6;
  --timeline-text-secondary: rgba(255, 255, 255, 0.7);
  --timeline-line: #2a3648;
  --timeline-dot-bg: #1a2638;
  --timeline-dot-border: #3a4658;
  
  /* Status colors - muted for dark theme */
  --status-active: #ef4444;
  --status-progress: #3b82f6;
  --status-failed: #f59e0b;
  --status-success: #10b981;
  
  /* Spacing */
  --timeline-spacing: 1.5rem;
  --timeline-line-width: 2px;
  --timeline-dot-size: 12px;
  --timeline-dot-size-sm: 8px;
}

/* Main timeline container */
.issue-timeline-container {
  position: relative;
  padding: 1rem 0;
}

/* Vertical timeline line */
.issue-timeline-line {
  position: absolute;
  left: 12px;
  top: 1.5rem;
  bottom: 1.5rem;
  width: var(--timeline-line-width);
  background: var(--timeline-line);
  transform: translateX(-50%);
}

/* Issue wrapper */
.issue-wrapper {
  display: grid;
  grid-template-columns: 24px 1fr;
  gap: var(--timeline-spacing);
  margin-bottom: 2rem;
  position: relative;
}

/* Timeline markers column */
.timeline-marker {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 0.25rem;
}

/* Issue status dot */
.timeline-dot {
  width: var(--timeline-dot-size);
  height: var(--timeline-dot-size);
  border-radius: 50%;
  background: var(--timeline-dot-bg);
  border: 2px solid var(--timeline-dot-border);
  position: relative;
  z-index: 2;
}

/* Status-specific dot colors */
.timeline-dot.status-active {
  border-color: var(--status-active);
}

.timeline-dot.status-in-progress {
  border-color: var(--status-progress);
}

.timeline-dot.status-solved {
  border-color: var(--status-success);
  background: var(--status-success);
}

/* Issue content */
.issue-content {
  padding: 0;
  min-height: 3rem;
}

/* Attempts container */
.attempts-container {
  margin-top: 1rem;
  margin-left: 1.5rem;
  position: relative;
}

/* SVG branch container positioning */
.attempts-container > svg {
  z-index: 1;
}

/* Attempt branch layout */
.attempt-branch {
  display: grid;
  grid-template-columns: 40px 1fr;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  position: relative;
}

/* Attempt status dot */
.attempt-dot {
  width: var(--timeline-dot-size-sm);
  height: var(--timeline-dot-size-sm);
  border-radius: 50%;
  background: var(--timeline-dot-bg);
  border: 2px solid var(--timeline-dot-border);
  margin-top: 0.25rem;
  position: relative;
  z-index: 2;
}

.attempt-dot.status-success {
  border-color: var(--status-success);
  background: var(--status-success);
}

.attempt-dot.status-failed {
  border-color: var(--status-failed);
}

/* Content styling */
.timeline-content {
  background: var(--timeline-surface);
  border: 1px solid var(--timeline-dot-border);
  border-radius: 8px;
  padding: 1rem;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.timeline-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--timeline-text-primary);
  margin: 0;
  flex: 1;
}

.timeline-status-icon {
  width: 1rem;
  height: 1rem;
  color: var(--timeline-text-secondary);
}

.timeline-description {
  color: var(--timeline-text-secondary);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  line-height: 1.5;
}

/* Attempt content */
.attempt-content {
  background: var(--timeline-surface);
  border: 1px solid var(--timeline-dot-border);
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.9375rem;
  color: var(--timeline-text-primary);
}

/* Code blocks */
.code-snippet {
  background: var(--timeline-input-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 0.5rem;
  margin-top: 0.5rem;
  font-family: monospace;
  font-size: 0.8125rem;
  overflow-x: auto;
  color: var(--timeline-text-primary);
}

/* Milestone specific styling for better visibility */
.timeline-milestone {
  color: var(--timeline-text-primary);
  font-weight: 600;
}

/* Form elements */
.timeline-input,
.timeline-textarea,
.timeline-select {
  background: var(--timeline-input-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--timeline-text-primary);
  padding: 0.625rem;
  width: 100%;
  font-size: 0.9375rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: border-color 0.2s ease;
}

.timeline-input:focus,
.timeline-textarea:focus,
.timeline-select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
  background: var(--timeline-bg);
}

/* Placeholder styling for better readability */
.timeline-input::placeholder,
.timeline-textarea::placeholder {
  color: var(--timeline-text-secondary);
  opacity: 0.5;
}

/* Buttons */
.timeline-button {
  background: transparent;
  border: 1px solid var(--timeline-dot-border);
  border-radius: 4px;
  color: var(--timeline-text-secondary);
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: none;
}

.timeline-button:hover {
  color: var(--timeline-text-primary);
  border-color: var(--timeline-text-secondary);
}

.timeline-button-primary {
  background: var(--status-progress);
  border-color: var(--status-progress);
  color: white;
}

.timeline-button-primary:hover {
  background: var(--status-progress);
  opacity: 0.9;
}

/* Add issue button */
.add-issue-button {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  margin-top: 1rem;
  background: transparent;
  border: 1px dashed var(--timeline-dot-border);
  border-radius: 8px;
  color: var(--timeline-text-secondary);
  cursor: pointer;
  font-size: 0.875rem;
}

.add-issue-button:hover {
  border-color: var(--timeline-text-secondary);
  color: var(--timeline-text-primary);
}

/* Empty state */
.timeline-empty {
  grid-column: 1 / -1;
  text-align: center;
  color: var(--timeline-text-secondary);
  padding: 3rem 1rem;
  font-size: 0.875rem;
}

/* Scrollbar styling */
.issue-tracker-scroll {
  scrollbar-width: thin;
  scrollbar-color: var(--timeline-line) transparent;
}

.issue-tracker-scroll::-webkit-scrollbar {
  width: 6px;
}

.issue-tracker-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.issue-tracker-scroll::-webkit-scrollbar-thumb {
  background: var(--timeline-line);
  border-radius: 3px;
}

/* Responsive design */
@media (max-width: 640px) {
  .issue-wrapper {
    grid-template-columns: 20px 1fr;
    gap: 0.75rem;
  }
  
  .attempts-container {
    margin-left: 1rem;
  }
  
  .attempt-branch {
    grid-template-columns: 32px 1fr;
    gap: 0.5rem;
  }
  
  
  .timeline-content,
  .attempt-content {
    padding: 0.75rem;
  }
  
  .issue-timeline-line {
    left: 10px;
  }
}

/* Focus states for accessibility */
.timeline-header:focus {
  outline: 2px solid var(--status-progress);
  outline-offset: 2px;
  border-radius: 4px;
}

.timeline-button:focus,
.timeline-input:focus,
.timeline-textarea:focus,
.timeline-select:focus {
  outline: 2px solid var(--status-progress);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --timeline-line: #4a5668;
    --timeline-dot-border: #5a6678;
    --timeline-text-secondary: rgba(255, 255, 255, 0.85);
    --timeline-input-bg: #1a2638;
    --timeline-text-primary: #ffffff;
  }
}

/* SVG optimization for smooth branching lines */
.issue-timeline-container svg {
  /* Ensure pixel-perfect rendering */
  shape-rendering: geometricPrecision;
  /* Prevent aliasing on retina displays */
  image-rendering: -webkit-optimize-contrast;
}

/* Timeline branch hover effect */
.attempts-container svg path {
  transition: stroke 0.2s ease, stroke-width 0.2s ease;
}

.attempts-container:hover svg path {
  stroke: var(--status-progress);
  stroke-width: 2.5px;
}

/* Ensure crisp vertical lines */
.issue-timeline-line {
  /* Round position to avoid sub-pixel blur */
  transform: translateX(-50%) translateZ(0);
  backface-visibility: hidden;
}

/* GitGraph branching overlay styles */
.gitgraph-branching-overlay {
  /* Ensure it doesn't interfere with interactions */
  pointer-events: none;
}

.gitgraph-branching-overlay canvas {
  /* Match the dark theme background */
  background: transparent !important;
}

/* Hide old SVG branching when GitGraph is active */
.issue-timeline-container .attempts-container > svg {
  display: none;
}