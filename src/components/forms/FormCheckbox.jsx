import { forwardRef } from 'react';
import { cn } from '../../utils/cn';
import { FieldError } from './FormError';

/**
 * Reusable form checkbox component with React Hook Form integration
 * @param {Object} props
 * @param {string} props.label - Checkbox label
 * @param {string} props.name - Checkbox name for form registration
 * @param {Object} props.error - Error object from React Hook Form
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.required - Whether the field is required
 * @param {React.Ref} ref - Forwarded ref from React Hook Form
 */
const FormCheckbox = forwardRef(({
  label,
  name,
  error,
  className,
  required,
  disabled,
  description,
  ...props
}, ref) => {
  return (
    <div className="w-full">
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            ref={ref}
            id={name}
            name={name}
            type="checkbox"
            disabled={disabled}
            aria-invalid={!!error}
            aria-describedby={error ? `${name}-error` : undefined}
            className={cn(
              "h-4 w-4 rounded border-gray-300 text-blue-600",
              "focus:ring-2 focus:ring-blue-500 focus:ring-offset-0",
              "dark:bg-gray-800 dark:border-gray-600",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              error && "border-red-500 dark:border-red-400",
              className
            )}
            {...props}
          />
        </div>
        
        <div className="ml-3 text-sm">
          {label && (
            <label 
              htmlFor={name} 
              className={cn(
                "font-medium text-gray-700 dark:text-gray-300",
                disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          
          {description && (
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              {description}
            </p>
          )}
        </div>
      </div>
      
      <FieldError error={error} className="ml-7" />
    </div>
  );
});

FormCheckbox.displayName = 'FormCheckbox';

/**
 * Checkbox group component for multiple checkboxes
 * @param {Object} props
 * @param {string} props.label - Group label
 * @param {Array<{value: string, label: string}>} props.options - Checkbox options
 * @param {Object} props.errors - Errors object from React Hook Form
 * @param {Function} props.register - Register function from React Hook Form
 */
export const FormCheckboxGroup = ({
  label,
  name,
  options = [],
  errors,
  register,
  required,
  className,
  description
}) => {
  const error = errors?.[name];
  
  return (
    <div className={cn("w-full", className)}>
      {label && (
        <div className="mb-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
          {description && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {description}
            </p>
          )}
        </div>
      )}
      
      <div className="space-y-2">
        {options.map((option) => (
          <div key={option.value} className="flex items-start">
            <div className="flex items-center h-5">
              <input
                {...register(`${name}.${option.value}`)}
                id={`${name}-${option.value}`}
                type="checkbox"
                className={cn(
                  "h-4 w-4 rounded border-gray-300 text-blue-600",
                  "focus:ring-2 focus:ring-blue-500 focus:ring-offset-0",
                  "dark:bg-gray-800 dark:border-gray-600",
                  option.disabled && "opacity-50 cursor-not-allowed"
                )}
                disabled={option.disabled}
              />
            </div>
            <div className="ml-3 text-sm">
              <label 
                htmlFor={`${name}-${option.value}`}
                className={cn(
                  "font-medium text-gray-700 dark:text-gray-300",
                  option.disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                {option.label}
              </label>
            </div>
          </div>
        ))}
      </div>
      
      <FieldError error={error} />
    </div>
  );
};

export default FormCheckbox;