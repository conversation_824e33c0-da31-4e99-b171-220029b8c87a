/**
 * @fileoverview Reusable error display components
 * Handles field errors and form-level errors
 */

import React from 'react';

/**
 * FormError component for displaying form-level errors
 * @param {Object} props - Component props
 * @param {string|string[]} props.error - Error message(s)
 * @param {string} [props.title] - Error title
 * @param {boolean} [props.dismissible] - Can error be dismissed
 * @param {Function} [props.onDismiss] - Dismiss handler
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element|null} FormError component
 */
export function FormError({ 
  error, 
  title = 'There was a problem with your submission',
  dismissible = false,
  onDismiss,
  className = ''
}) {
  if (!error) return null;
  
  const errors = Array.isArray(error) ? error : [error];
  if (errors.length === 0) return null;
  
  return (
    <div className={`rounded-md bg-red-50 p-4 ${className}`} role="alert">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg 
            className="h-5 w-5 text-red-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">
            {title}
          </h3>
          <div className="mt-2 text-sm text-red-700">
            {errors.length === 1 ? (
              <p>{errors[0]}</p>
            ) : (
              <ul className="list-disc pl-5 space-y-1">
                {errors.map((err, index) => (
                  <li key={index}>{err}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * FieldError component for inline field errors
 * @param {Object} props - Component props
 * @param {Object} props.error - Error object from react-hook-form
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element|null} FieldError component
 */
export function FieldError({ error, className = '' }) {
  if (!error) return null;
  
  return (
    <p className={`text-sm text-red-600 mt-1 ${className}`} role="alert">
      {error.message}
    </p>
  );
}

/**
 * Success message component
 * @param {Object} props - Component props
 * @param {string} props.message - Success message
 * @param {boolean} [props.dismissible] - Can message be dismissed
 * @param {Function} [props.onDismiss] - Dismiss handler
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element|null} SuccessMessage component
 */
export function SuccessMessage({ 
  message, 
  dismissible = true,
  onDismiss,
  className = ''
}) {
  if (!message) return null;
  
  return (
    <div className={`rounded-md bg-green-50 p-4 ${className}`} role="status">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg 
            className="h-5 w-5 text-green-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-green-800">
            {message}
          </p>
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Warning message component
 * @param {Object} props - Component props
 * @param {string} props.message - Warning message
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element|null} WarningMessage component
 */
export function WarningMessage({ message, className = '' }) {
  if (!message) return null;
  
  return (
    <div className={`rounded-md bg-yellow-50 p-4 ${className}`} role="alert">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg 
            className="h-5 w-5 text-yellow-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-700">
            {message}
          </p>
        </div>
      </div>
    </div>
  );
}