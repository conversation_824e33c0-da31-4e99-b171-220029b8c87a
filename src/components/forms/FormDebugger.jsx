/**
 * @fileoverview Form debugging component for development
 * Shows real-time form state, errors, and values
 */

import React, { useState } from 'react';

/**
 * FormDebugger component to inspect form state during development
 * @param {Object} props - Component props
 * @param {Object} props.form - Form object from useFormWithValidation
 * @param {boolean} [props.defaultOpen=false] - Start with debugger open
 * @param {string} [props.position='bottom-right'] - Position on screen
 * @returns {JSX.Element|null} FormDebugger component (null in production)
 */
export function FormDebugger({ 
  form, 
  defaultOpen = false,
  position = 'bottom-right'
}) {
  // Only render in development
  if (import.meta.env.PROD) return null;
  
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [isMinimized, setIsMinimized] = useState(false);
  
  // Get form state
  const formState = {
    values: form.watch(),
    errors: form.formState.errors,
    isValid: form.formState.isValid,
    isDirty: form.formState.isDirty,
    isSubmitting: form.formState.isSubmitting,
    isSubmitted: form.formState.isSubmitted,
    submitCount: form.formState.submitCount,
    dirtyFields: form.formState.dirtyFields,
    touchedFields: form.formState.touchedFields
  };
  
  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };
  
  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={`
          fixed ${positionClasses[position]} 
          bg-gray-900 text-white p-2 rounded-full shadow-lg
          hover:bg-gray-800 transition-colors
          z-50
        `}
        title="Open Form Debugger"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      </button>
    );
  }
  
  return (
    <div 
      className={`
        fixed ${positionClasses[position]} 
        bg-gray-900 text-white rounded-lg shadow-2xl
        ${isMinimized ? 'w-48' : 'w-96 max-w-full'}
        max-h-[80vh] overflow-hidden
        z-50
      `}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <h3 className="font-semibold flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
          Form Debugger
        </h3>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 hover:bg-gray-800 rounded"
            title={isMinimized ? 'Expand' : 'Minimize'}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMinimized ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              )}
            </svg>
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className="p-1 hover:bg-gray-800 rounded"
            title="Close"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Content */}
      {!isMinimized && (
        <div className="p-3 space-y-3 overflow-y-auto max-h-[calc(80vh-3rem)]">
          {/* Status badges */}
          <div className="flex flex-wrap gap-2">
            <StatusBadge label="Valid" value={formState.isValid} type="success" />
            <StatusBadge label="Dirty" value={formState.isDirty} type="warning" />
            <StatusBadge label="Submitting" value={formState.isSubmitting} type="info" />
            <StatusBadge label="Submitted" value={formState.isSubmitted} type="info" />
            {formState.submitCount > 0 && (
              <span className="px-2 py-1 bg-blue-900 text-blue-200 text-xs rounded">
                Submits: {formState.submitCount}
              </span>
            )}
          </div>
          
          {/* Form values */}
          <Section title="Form Values" defaultOpen>
            <pre className="text-xs overflow-x-auto">
              {JSON.stringify(formState.values, null, 2)}
            </pre>
          </Section>
          
          {/* Errors */}
          {Object.keys(formState.errors).length > 0 && (
            <Section title="Errors" defaultOpen>
              <pre className="text-xs overflow-x-auto text-red-400">
                {JSON.stringify(formState.errors, null, 2)}
              </pre>
            </Section>
          )}
          
          {/* Dirty fields */}
          {Object.keys(formState.dirtyFields).length > 0 && (
            <Section title="Dirty Fields">
              <div className="text-xs space-y-1">
                {Object.keys(formState.dirtyFields).map(field => (
                  <div key={field} className="text-yellow-400">• {field}</div>
                ))}
              </div>
            </Section>
          )}
          
          {/* Touched fields */}
          {Object.keys(formState.touchedFields).length > 0 && (
            <Section title="Touched Fields">
              <div className="text-xs space-y-1">
                {Object.keys(formState.touchedFields).map(field => (
                  <div key={field} className="text-blue-400">• {field}</div>
                ))}
              </div>
            </Section>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Status badge component
 */
function StatusBadge({ label, value, type = 'default' }) {
  const colors = {
    success: value ? 'bg-green-900 text-green-200' : 'bg-gray-700 text-gray-400',
    warning: value ? 'bg-yellow-900 text-yellow-200' : 'bg-gray-700 text-gray-400',
    info: value ? 'bg-blue-900 text-blue-200' : 'bg-gray-700 text-gray-400',
    default: 'bg-gray-700 text-gray-400'
  };
  
  return (
    <span className={`px-2 py-1 text-xs rounded ${colors[type]}`}>
      {label}: {value ? 'Yes' : 'No'}
    </span>
  );
}

/**
 * Collapsible section component
 */
function Section({ title, children, defaultOpen = false }) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  
  return (
    <div className="border border-gray-700 rounded">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 text-left text-sm font-medium bg-gray-800 hover:bg-gray-750 transition-colors flex items-center justify-between"
      >
        {title}
        <svg className={`w-4 h-4 transform transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      {isOpen && (
        <div className="p-3 bg-gray-850">
          {children}
        </div>
      )}
    </div>
  );
}