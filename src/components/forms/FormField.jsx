/**
 * @fileoverview Reusable form field component with validation
 * Handles text, email, password, and number inputs
 */

import React from 'react';

/**
 * FormField component for text-based inputs
 * @param {Object} props - Component props
 * @param {Function} props.register - react-hook-form register function
 * @param {Object} props.errors - Form errors object
 * @param {string} props.name - Field name
 * @param {string} [props.label] - Field label
 * @param {string} [props.type='text'] - Input type
 * @param {string} [props.placeholder] - Placeholder text
 * @param {boolean} [props.required] - Is field required
 * @param {boolean} [props.disabled] - Is field disabled
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.description] - Help text
 * @param {Object} [props.autoComplete] - Autocomplete attribute
 * @returns {JSX.Element} FormField component
 */
export function FormField({ 
  register, 
  errors, 
  name, 
  label, 
  type = 'text',
  placeholder,
  required = false,
  disabled = false,
  className = '',
  description,
  autoComplete,
  ...props 
}) {
  const error = errors?.[name];
  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const descId = `${fieldId}-description`;
  
  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label 
          htmlFor={fieldId}
          className={`block text-sm font-medium ${
            error ? 'text-red-700' : 'text-gray-700'
          }`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          {...register(name)}
          id={fieldId}
          type={type}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={autoComplete}
          aria-invalid={!!error}
          aria-describedby={`${error ? errorId : ''} ${description ? descId : ''}`}
          className={`
            w-full px-3 py-2 
            border rounded-md 
            shadow-sm
            placeholder-gray-400
            focus:outline-none focus:ring-1
            disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
            ${error 
              ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500'
            }
          `}
          {...props}
        />
        
        {/* Error icon */}
        {error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg 
              className="h-5 w-5 text-red-500" 
              fill="currentColor" 
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>
      
      {/* Description text */}
      {description && !error && (
        <p id={descId} className="text-sm text-gray-500">
          {description}
        </p>
      )}
      
      {/* Error message */}
      {error && (
        <p id={errorId} className="text-sm text-red-600" role="alert">
          {error.message}
        </p>
      )}
    </div>
  );
}

/**
 * Password field with show/hide toggle
 * @param {Object} props - Same as FormField props
 * @returns {JSX.Element} PasswordField component
 */
export function PasswordField(props) {
  const [showPassword, setShowPassword] = React.useState(false);
  
  return (
    <div className="relative">
      <FormField
        {...props}
        type={showPassword ? 'text' : 'password'}
        autoComplete="current-password"
      />
      
      {/* Show/hide toggle button */}
      <button
        type="button"
        className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
        onClick={() => setShowPassword(!showPassword)}
        tabIndex={-1}
      >
        {showPassword ? (
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
          </svg>
        ) : (
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        )}
      </button>
    </div>
  );
}

/**
 * Email field with email-specific attributes
 * @param {Object} props - Same as FormField props
 * @returns {JSX.Element} EmailField component
 */
export function EmailField(props) {
  return (
    <FormField
      {...props}
      type="email"
      autoComplete="email"
      placeholder={props.placeholder || "<EMAIL>"}
    />
  );
}