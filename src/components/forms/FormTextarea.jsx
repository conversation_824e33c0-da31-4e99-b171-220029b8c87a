import { forwardRef } from 'react';
import { cn } from '../../utils/cn';
import { FieldError } from './FormError';

/**
 * Reusable form textarea component with React Hook Form integration
 * @param {Object} props
 * @param {string} props.label - Textarea label
 * @param {string} props.name - Textarea name for form registration
 * @param {string} props.placeholder - Textarea placeholder
 * @param {Object} props.error - Error object from React Hook Form
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.required - Whether the field is required
 * @param {number} props.rows - Number of visible text lines
 * @param {boolean} props.autoResize - Enable automatic height adjustment
 * @param {React.Ref} ref - Forwarded ref from React Hook Form
 */
const FormTextarea = forwardRef(({
  label,
  name,
  placeholder,
  error,
  className,
  required,
  disabled,
  rows = 4,
  autoResize = false,
  maxLength,
  value,
  onChange,
  ...props
}, ref) => {
  const handleAutoResize = (e) => {
    if (autoResize) {
      e.target.style.height = 'auto';
      e.target.style.height = `${e.target.scrollHeight}px`;
    }
    onChange?.(e);
  };

  return (
    <div className="w-full">
      {label && (
        <label 
          htmlFor={name} 
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <textarea
          ref={ref}
          id={name}
          name={name}
          placeholder={placeholder}
          disabled={disabled}
          rows={rows}
          maxLength={maxLength}
          value={value}
          onChange={handleAutoResize}
          aria-invalid={!!error}
          aria-describedby={error ? `${name}-error` : undefined}
          className={cn(
            "w-full px-3 py-2 border rounded-md transition-colors duration-200",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
            "dark:bg-gray-800 dark:text-white",
            autoResize ? "resize-none overflow-hidden" : "resize-y",
            error
              ? "border-red-500 dark:border-red-400"
              : "border-gray-300 dark:border-gray-600",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          {...props}
        />
        
        {maxLength && (
          <div className="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400 pointer-events-none">
            {value?.length || 0}/{maxLength}
          </div>
        )}
      </div>
      
      <FieldError error={error} />
    </div>
  );
});

FormTextarea.displayName = 'FormTextarea';

export default FormTextarea;