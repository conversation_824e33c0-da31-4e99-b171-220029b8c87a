import { forwardRef } from 'react';
import { cn } from '../../utils/cn';
import { FieldError } from './FormError';

/**
 * Reusable form select component with React Hook Form integration
 * @param {Object} props
 * @param {string} props.label - Select label
 * @param {string} props.name - Select name for form registration
 * @param {Array<{value: string, label: string}>} props.options - Select options
 * @param {string} props.placeholder - Placeholder option text
 * @param {Object} props.error - Error object from React Hook Form
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.required - Whether the field is required
 * @param {React.Ref} ref - Forwarded ref from React Hook Form
 */
const FormSelect = forwardRef(({
  label,
  name,
  options = [],
  placeholder = 'Select an option',
  error,
  className,
  required,
  disabled,
  value,
  ...props
}, ref) => {
  return (
    <div className="w-full">
      {label && (
        <label 
          htmlFor={name} 
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <select
          ref={ref}
          id={name}
          name={name}
          disabled={disabled}
          value={value}
          aria-invalid={!!error}
          aria-describedby={error ? `${name}-error` : undefined}
          className={cn(
            "w-full px-3 py-2 pr-10 border rounded-md appearance-none",
            "transition-colors duration-200",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
            "dark:bg-gray-800 dark:text-white",
            error
              ? "border-red-500 dark:border-red-400"
              : "border-gray-300 dark:border-gray-600",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          {...props}
        >
          {placeholder && (
            <option value="" disabled={required}>
              {placeholder}
            </option>
          )}
          
          {options.map((option) => (
            <option 
              key={option.value} 
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>
        
        {/* Dropdown arrow icon */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg 
            className="h-5 w-5 text-gray-400" 
            fill="currentColor" 
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
      
      <FieldError error={error} />
    </div>
  );
});

FormSelect.displayName = 'FormSelect';

export default FormSelect;

/**
 * Helper to create options from an array of strings
 * @param {string[]} values - Array of option values
 * @returns {Array<{value: string, label: string}>} Options array
 */
export function createOptions(values) {
  return values.map(value => ({
    value,
    label: value
  }));
}

/**
 * Helper to create options from an object (enum-like)
 * @param {Object} enumObject - Object with key-value pairs
 * @returns {Array<{value: string, label: string}>} Options array
 */
export function createOptionsFromEnum(enumObject) {
  return Object.entries(enumObject).map(([key, value]) => ({
    value: key,
    label: value
  }));
}