import { forwardRef } from 'react';
import { cn } from '../../utils/cn';
import { FieldError } from './FormError';

/**
 * Reusable form input component with React Hook Form integration
 * @param {Object} props
 * @param {string} props.label - Input label
 * @param {string} props.name - Input name for form registration
 * @param {string} props.type - Input type (text, email, password, etc.)
 * @param {string} props.placeholder - Input placeholder
 * @param {Object} props.error - Error object from React Hook Form
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.required - Whether the field is required
 * @param {React.Ref} ref - Forwarded ref from React Hook Form
 */
const FormInput = forwardRef(({
  label,
  name,
  type = 'text',
  placeholder,
  error,
  className,
  required,
  disabled,
  autoComplete,
  ...props
}, ref) => {
  return (
    <div className="w-full">
      {label && (
        <label 
          htmlFor={name} 
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        ref={ref}
        id={name}
        name={name}
        type={type}
        placeholder={placeholder}
        disabled={disabled}
        autoComplete={autoComplete}
        aria-invalid={!!error}
        aria-describedby={error ? `${name}-error` : undefined}
        className={cn(
          "w-full px-3 py-2 border rounded-md transition-colors duration-200",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
          "dark:bg-gray-800 dark:text-white",
          error
            ? "border-red-500 dark:border-red-400"
            : "border-gray-300 dark:border-gray-600",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        {...props}
      />
      
      <FieldError error={error} />
    </div>
  );
});

FormInput.displayName = 'FormInput';

export default FormInput;