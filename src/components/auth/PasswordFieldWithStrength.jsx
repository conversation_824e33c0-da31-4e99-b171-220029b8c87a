import { useState } from 'react';
import { Eye, EyeOff, Lock } from 'lucide-react';
import { usePasswordStrength } from '../../hooks/usePasswordStrength';

export function PasswordFieldWithStrength({ 
  register, 
  value, 
  error, 
  label, 
  placeholder, 
  disabled, 
  showStrength = false 
}) {
  const [showPassword, setShowPassword] = useState(false);
  const { strength, label: strengthLabel } = usePasswordStrength(value);

  return (
    <div className="auth-field-group">
      <label htmlFor="password">{label}</label>
      <div className="auth-input-wrapper">
        <Lock size={18} className="auth-input-icon" />
        <input
          {...register('password')}
          id="password"
          type={showPassword ? 'text' : 'password'}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={showStrength ? 'new-password' : 'current-password'}
        />
        <button
          type="button"
          className="auth-password-toggle"
          onClick={() => setShowPassword(!showPassword)}
          tabIndex={-1}
        >
          {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
        </button>
      </div>
      
      {error && <span className="auth-field-error">{error.message}</span>}
      
      {showStrength && value && (
        <div className="auth-password-strength">
          <div className="auth-password-strength-bars">
            {[1, 2, 3, 4].map((level) => (
              <div
                key={level}
                className={`auth-password-strength-bar ${
                  strength >= level ? `strength-${strength}` : ''
                }`}
              />
            ))}
          </div>
          <span className="auth-password-strength-text">{strengthLabel}</span>
        </div>
      )}
    </div>
  );
}