/* Slim scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

/* Remove hover state that causes reflow */
.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.12);
}

/* Grid container */
.grid-container {
  position: relative;
  /* Enable GPU acceleration */
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Smooth card transitions */
.virtualized-grid-item {
  transition: left 300ms cubic-bezier(0.4, 0, 0.2, 1), top 300ms cubic-bezier(0.4, 0, 0.2, 1);
  will-change: left, top;
  /* Optimize rendering */
  contain: layout style paint;
}

/* Prevent layout shift during transitions */
.virtualized-grid-container {
  contain: layout style;
  /* Optimize rendering */
  transform: translateZ(0);
  will-change: contents;
}

/* Stable scrollbar - prevents position reset */
.scrollbar-stable {
  /* Preserve scroll position when focus changes */
  overflow-anchor: auto;
  
  /* Prevent layout shift when scrollbar appears/disappears */
  scrollbar-gutter: stable both-edges;
  
  /* Maintain scroll position */
  scroll-behavior: auto;
  will-change: scroll-position;
}

/* Consistent scrollbar appearance - no hover changes */
.scrollbar-stable::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-stable::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 4px;
}

.scrollbar-stable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  border: 1px solid transparent;
  background-clip: padding-box;
}

/* Minimal hover effect without changing size */
.scrollbar-stable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.12);
}

/* AI Messages Container - Prevent scroll jumping */
.ai-messages-container {
  /* Prevent layout shifts during editing */
  overflow-anchor: auto;
  
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

/* Message bubble editing state */
.message-bubble textarea {
  /* Ensure scrollbar doesn't cause layout shift */
  scrollbar-gutter: stable;
}

/* Prevent focus-related scroll jumps */
.message-content {
  /* Maintain layout stability */
  position: relative;
  
  /* Prevent text selection from causing jumps */
  user-select: text;
  -webkit-user-select: text;
}