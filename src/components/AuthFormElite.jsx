import { Loader2 } from 'lucide-react';
import { useFormWithValidation } from '../hooks/useFormWithValidation';
import { useMagneticButton } from '../hooks/useMagneticButton';
import { signInSchema, signUpSchema } from '../utils/forms/validationSchemas';
import { EmailField } from './forms/FormField';
import { PasswordFieldWithStrength } from './auth/PasswordFieldWithStrength';
import { FormError, SuccessMessage } from './forms/FormError';
import { OAuthButtons } from './auth/OAuthButtons';

const AuthFormElite = ({ authView, onViewChange, onAuth, isLoading, error, successMessage }) => {
  const schema = authView === 'sign_up' ? signUpSchema : signInSchema;
  const form = useFormWithValidation(schema);
  const { register, handleSubmit, watch, errors, formState: { isValid } } = form;
  const { buttonRef, magneticStyle, onMouseMove, onMouseLeave } = useMagneticButton();

  const onSubmit = async (data) => {
    await onAuth(authView, { email: data.email, password: data.password });
  };

  return (
    <div className="auth-form-elite">
      <div className="auth-form-glass-layer-1" />
      <div className="auth-form-glass-layer-2" />
      <div className="auth-form-glass-layer-3" />
      
      <div className="auth-form-content">
        <div className="auth-form-header">
          <h2>{authView === 'sign_in' ? 'Welcome back' : 'Start your journey'}</h2>
          <p>{authView === 'sign_in' ? 'Sign in to access your developer knowledge base' : 'Create an account to never lose a solution again'}</p>
        </div>

        <SuccessMessage message={successMessage || form.successMessage} />
        <FormError error={error || errors.root?.message} />
        <OAuthButtons onAuth={onAuth} isLoading={isLoading} />
        
        <div className="auth-divider"><span>or continue with email</span></div>
        
        <form onSubmit={handleSubmit(onSubmit)} className="auth-form-fields">
          <EmailField register={register} errors={errors} name="email" label="Email" required />
          <PasswordFieldWithStrength register={register} value={watch('password')} error={errors.password} 
            label={authView === 'sign_up' ? 'Create password' : 'Password'} 
            placeholder={authView === 'sign_up' ? 'Min 8 characters' : '••••••••'} 
            showStrength={authView === 'sign_up'} />
          
          <button ref={buttonRef} type="submit" className="auth-submit-button" 
            disabled={isLoading || !isValid} onMouseMove={onMouseMove} onMouseLeave={onMouseLeave} style={magneticStyle}>
            {isLoading ? <><Loader2 size={20} className="auth-button-spinner" /><span>{authView === 'sign_in' ? 'Signing in...' : 'Creating account...'}</span></> 
                       : <span>{authView === 'sign_in' ? 'Sign in' : 'Create account'}</span>}
            <div className="auth-button-ripple" />
          </button>
        </form>
        
        <div className="auth-form-footer">
          <p>{authView === 'sign_in' ? <>Don't have an account? <button type="button" onClick={() => onViewChange('sign_up')}>Sign up</button></> : <>Already have an account? <button type="button" onClick={() => onViewChange('sign_in')}>Sign in</button></>}</p>
          <p className="auth-terms">By continuing, you agree to our <a href="/terms">Terms</a> & <a href="/privacy">Privacy</a></p>
        </div>
      </div>
    </div>
  );
};

export default AuthFormElite;