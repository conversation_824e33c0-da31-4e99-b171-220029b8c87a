/**
 * Visual debug panel for monitoring real-time subscriptions
 * Only shows in development mode
 */

import { useState, useEffect } from 'react';
import realtimeDebugger from '../../utils/realtimeDebugger';

export function RealtimeDebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [stats, setStats] = useState(null);
  const [updateCounter, setUpdateCounter] = useState(0);

  // Only render in development
  if (!import.meta.env.DEV) {
    return null;
  }

  useEffect(() => {
    if (!isOpen) return;

    // Update stats every 2 seconds when panel is open
    const interval = setInterval(() => {
      setStats(realtimeDebugger.getStats());
      setUpdateCounter(c => c + 1);
    }, 2000);

    // Initial load
    setStats(realtimeDebugger.getStats());

    return () => clearInterval(interval);
  }, [isOpen]);

  const handleLog = () => {
    realtimeDebugger.logState();
  };

  const handleReset = () => {
    realtimeDebugger.reset();
    setStats(realtimeDebugger.getStats());
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2"
        title="Realtime Subscriptions Debug Panel"
      >
        <span className="text-xl">📡</span>
        {stats && stats.totalActive > 0 && (
          <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
            {stats.totalActive}
          </span>
        )}
      </button>

      {/* Debug Panel */}
      {isOpen && stats && (
        <div className="fixed bottom-16 right-4 bg-gray-900 text-gray-100 rounded-lg shadow-2xl p-4 w-96 max-h-[500px] overflow-auto z-50">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Realtime Subscriptions</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>

          {/* Stats Summary */}
          <div className="grid grid-cols-2 gap-2 mb-4">
            <div className="bg-gray-800 p-2 rounded">
              <div className="text-xs text-gray-400">Active</div>
              <div className="text-xl font-bold text-green-400">{stats.totalActive}</div>
            </div>
            <div className="bg-gray-800 p-2 rounded">
              <div className="text-xs text-gray-400">Total Created</div>
              <div className="text-xl font-bold">{stats.totalCreated}</div>
            </div>
            <div className="bg-gray-800 p-2 rounded">
              <div className="text-xs text-gray-400">Cleaned</div>
              <div className="text-xl font-bold text-blue-400">{stats.totalCleaned}</div>
            </div>
            <div className="bg-gray-800 p-2 rounded">
              <div className="text-xs text-gray-400">Potential Leaks</div>
              <div className="text-xl font-bold text-red-400">{stats.possibleLeaks.length}</div>
            </div>
          </div>

          {/* Active Subscriptions by Table */}
          {stats.totalActive > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-semibold mb-2">By Table</h4>
              <div className="bg-gray-800 p-2 rounded text-xs">
                {Object.entries(stats.byTable).map(([table, count]) => (
                  <div key={table} className="flex justify-between py-1">
                    <span>{table}</span>
                    <span className="text-green-400">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Potential Leaks Warning */}
          {stats.possibleLeaks.length > 0 && (
            <div className="mb-4 bg-red-900 p-2 rounded">
              <h4 className="text-sm font-semibold mb-1 text-red-400">
                ⚠️ Potential Memory Leaks
              </h4>
              <div className="text-xs">
                {stats.possibleLeaks.map((leak, idx) => (
                  <div key={idx} className="py-1">
                    {leak.component}: {leak.table} 
                    ({Math.round((Date.now() - leak.startTime) / 1000)}s)
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={handleLog}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 rounded"
            >
              Log to Console
            </button>
            <button
              onClick={handleReset}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-1 rounded"
            >
              Reset Tracking
            </button>
          </div>

          <div className="text-xs text-gray-500 mt-2">
            Auto-updates every 2s
          </div>
        </div>
      )}
    </>
  );
}