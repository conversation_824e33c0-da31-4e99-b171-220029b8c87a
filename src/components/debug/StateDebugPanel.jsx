/**
 * Visual debug panel for monitoring Zustand store states
 * Only shows in development mode
 */

import { useState, useEffect } from 'react';
import useAuthStore from '../../stores/authStore';
import useSettingsStore from '../../stores/settingsStore';
import useUIStore from '../../stores/uiStore';
import useDocumentStore from '../../stores/documentStore';
import useEditorStore from '../../stores/editorStore';
import useFormStore from '../../stores/formStore';

export function StateDebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState('auth');
  const [updateCounter, setUpdateCounter] = useState(0);

  // Only render in development
  if (!import.meta.env.DEV) {
    return null;
  }

  // Subscribe to all stores
  const authState = useAuthStore();
  const settingsState = useSettingsStore();
  const uiState = useUIStore();
  const documentState = useDocumentStore();
  const editorState = useEditorStore();
  const formState = useFormStore();

  // Force re-render every second to show state changes
  useEffect(() => {
    if (!isOpen) return;
    
    const interval = setInterval(() => {
      setUpdateCounter(c => c + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen]);

  const stores = {
    auth: authState,
    settings: settingsState,
    ui: uiState,
    documents: documentState,
    editor: editorState,
    forms: formState
  };

  const storeInfo = {
    auth: { icon: '🔐', name: 'Auth Store' },
    settings: { icon: '⚙️', name: 'Settings Store' },
    ui: { icon: '🎨', name: 'UI Store' },
    documents: { icon: '📄', name: 'Document Store' },
    editor: { icon: '✏️', name: 'Editor Store' },
    forms: { icon: '📝', name: 'Form Store' }
  };

  const getStatePreview = (state) => {
    // Remove functions and selectors for cleaner display
    const cleanState = {};
    Object.entries(state).forEach(([key, value]) => {
      if (typeof value !== 'function' && key !== 'selectors') {
        cleanState[key] = value;
      }
    });
    return cleanState;
  };

  const handleReset = () => {
    if (window.__APP_STATE__ && window.__APP_STATE__.reset) {
      window.__APP_STATE__.reset(selectedStore);
      setUpdateCounter(c => c + 1);
    }
  };

  const handleLogAll = () => {
    if (window.__APP_STATE__ && window.__APP_STATE__.logAll) {
      window.__APP_STATE__.logAll();
    }
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 left-4 bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2"
        title="Zustand State Debug Panel"
      >
        <span className="text-xl">🏪</span>
        <span className="text-xs">State</span>
      </button>

      {/* Debug Panel */}
      {isOpen && (
        <div className="fixed bottom-16 left-4 bg-gray-900 text-gray-100 rounded-lg shadow-2xl p-4 w-[500px] max-h-[600px] overflow-hidden z-50 flex flex-col">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Zustand State Inspector</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>

          {/* Store Tabs */}
          <div className="flex gap-1 mb-4 overflow-x-auto">
            {Object.entries(storeInfo).map(([key, info]) => (
              <button
                key={key}
                onClick={() => setSelectedStore(key)}
                className={`px-3 py-1 rounded text-sm whitespace-nowrap ${
                  selectedStore === key
                    ? 'bg-indigo-600 text-white'
                    : 'bg-gray-800 text-gray-400 hover:text-white'
                }`}
              >
                <span className="mr-1">{info.icon}</span>
                {info.name}
              </button>
            ))}
          </div>

          {/* State Display */}
          <div className="flex-1 overflow-auto bg-gray-800 rounded p-3 mb-4">
            <pre className="text-xs text-gray-300">
              {JSON.stringify(getStatePreview(stores[selectedStore]), null, 2)}
            </pre>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={handleLogAll}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 rounded"
            >
              Log All Stores
            </button>
            <button
              onClick={handleReset}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-1 rounded"
            >
              Reset {storeInfo[selectedStore].name}
            </button>
          </div>

          <div className="text-xs text-gray-500 mt-2">
            Redux DevTools also available (Ctrl+Shift+E)
          </div>
        </div>
      )}
    </>
  );
}