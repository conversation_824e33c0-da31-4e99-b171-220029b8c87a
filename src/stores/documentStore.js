import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';
import storageWrapper from '../utils/storage/storageWrapper';

/**
 * Document Store - Enhanced version of useDocumentOrganization
 * Manages document state, selection, and operations
 */
const useDocumentStore = create(
  devtools('Documents')(
    (set, get) => ({
      // Document list state
      documents: [],
      filteredDocuments: [],
      isLoading: false,
      error: null,
      
      // Search and filter state
      searchQuery: '',
      filterTags: [],
      sortBy: 'updated_at', // 'updated_at' | 'created_at' | 'title'
      sortOrder: 'desc', // 'asc' | 'desc'
      
      // Selection state (from original)
      selectedDocuments: new Set(),
      lastSelectedId: null,
      
      // Drag state (from original)
      isDragging: false,
      draggedDocuments: [],
      draggedOverProjectId: null,
      
      // Current document state
      currentDocument: null,
      currentBlocks: [],
      expandedDocumentId: null,
      
      // Project state
      projects: [],
      selectedProjectId: null,
      
      // Actions
      /**
       * Load all documents
       * @param {string} userId - User ID
       */
      loadDocuments: async (userId) => {
        set({ isLoading: true, error: null });
        
        try {
          const documents = await storageWrapper.getEntries(userId);
          set({ 
            documents,
            filteredDocuments: documents,
            isLoading: false 
          });
          
          // Apply current filters
          get().applyFilters();
        } catch (error) {
          console.error('Error loading documents:', error);
          set({ 
            error: error.message,
            isLoading: false 
          });
        }
      },
      
      /**
       * Create a new document
       * @param {Object} documentData - Document data
       */
      createDocument: async (documentData) => {
        try {
          const newDoc = await storageWrapper.createDocument(documentData);
          
          set((state) => ({
            documents: [newDoc, ...state.documents],
            filteredDocuments: [newDoc, ...state.filteredDocuments]
          }));
          
          return { data: newDoc, error: null };
        } catch (error) {
          console.error('Error creating document:', error);
          return { data: null, error };
        }
      },
      
      /**
       * Update a document
       * @param {string} docId - Document ID
       * @param {Object} updates - Updates to apply
       */
      updateDocument: async (docId, updates) => {
        try {
          await storageWrapper.updateDocument(docId, updates);
          
          set((state) => ({
            documents: state.documents.map(doc => 
              doc.id === docId ? { ...doc, ...updates } : doc
            ),
            filteredDocuments: state.filteredDocuments.map(doc => 
              doc.id === docId ? { ...doc, ...updates } : doc
            )
          }));
          
          return { error: null };
        } catch (error) {
          console.error('Error updating document:', error);
          return { error };
        }
      },
      
      /**
       * Delete documents
       * @param {string[]} docIds - Document IDs to delete
       */
      deleteDocuments: async (docIds) => {
        try {
          await Promise.all(
            docIds.map(id => storageWrapper.deleteDocument(id))
          );
          
          set((state) => ({
            documents: state.documents.filter(doc => !docIds.includes(doc.id)),
            filteredDocuments: state.filteredDocuments.filter(doc => !docIds.includes(doc.id)),
            selectedDocuments: new Set()
          }));
          
          return { error: null };
        } catch (error) {
          console.error('Error deleting documents:', error);
          return { error };
        }
      },
      
      /**
       * Set search query and filter documents
       * @param {string} query - Search query
       */
      setSearchQuery: (query) => {
        set({ searchQuery: query });
        get().applyFilters();
      },
      
      /**
       * Set filter tags
       * @param {string[]} tags - Tags to filter by
       */
      setFilterTags: (tags) => {
        set({ filterTags: tags });
        get().applyFilters();
      },
      
      /**
       * Set sort options
       * @param {string} sortBy - Field to sort by
       * @param {string} sortOrder - Sort order
       */
      setSort: (sortBy, sortOrder) => {
        set({ sortBy, sortOrder });
        get().applyFilters();
      },
      
      /**
       * Apply all filters and sorting
       */
      applyFilters: () => {
        const { documents, searchQuery, filterTags, sortBy, sortOrder } = get();
        
        let filtered = [...documents];
        
        // Apply search
        if (searchQuery) {
          const query = searchQuery.toLowerCase();
          filtered = filtered.filter(doc => 
            doc.title.toLowerCase().includes(query) ||
            doc.tags?.some(tag => tag.toLowerCase().includes(query))
          );
        }
        
        // Apply tag filters
        if (filterTags.length > 0) {
          filtered = filtered.filter(doc =>
            filterTags.some(tag => doc.tags?.includes(tag))
          );
        }
        
        // Apply sorting
        filtered.sort((a, b) => {
          const aVal = a[sortBy];
          const bVal = b[sortBy];
          
          if (sortBy === 'title') {
            return sortOrder === 'asc' 
              ? aVal.localeCompare(bVal)
              : bVal.localeCompare(aVal);
          }
          
          // Date sorting
          const aDate = new Date(aVal);
          const bDate = new Date(bVal);
          return sortOrder === 'asc' 
            ? aDate - bDate
            : bDate - aDate;
        });
        
        set({ filteredDocuments: filtered });
      },
      
      // Selection methods (from original)
      toggleSelection: (docId) => {
        set(state => {
          const newSelection = new Set(state.selectedDocuments);
          if (newSelection.has(docId)) {
            newSelection.delete(docId);
          } else {
            newSelection.add(docId);
          }
          return { 
            selectedDocuments: newSelection,
            lastSelectedId: docId 
          };
        });
      },
      
      selectRange: (fromId, toId) => {
        const { filteredDocuments } = get();
        const fromIndex = filteredDocuments.findIndex(doc => doc.id === fromId);
        const toIndex = filteredDocuments.findIndex(doc => doc.id === toId);
        
        if (fromIndex === -1 || toIndex === -1) return;
        
        const start = Math.min(fromIndex, toIndex);
        const end = Math.max(fromIndex, toIndex);
        
        const rangeIds = filteredDocuments
          .slice(start, end + 1)
          .map(doc => doc.id);
        
        set({ 
          selectedDocuments: new Set(rangeIds),
          lastSelectedId: toId 
        });
      },
      
      selectAll: () => {
        const { filteredDocuments } = get();
        set({ 
          selectedDocuments: new Set(filteredDocuments.map(doc => doc.id)),
          lastSelectedId: filteredDocuments[filteredDocuments.length - 1]?.id || null
        });
      },
      
      clearSelection: () => {
        set({ 
          selectedDocuments: new Set(),
          lastSelectedId: null 
        });
      },
      
      // Drag methods (from original)
      startDrag: (documents) => {
        set({ 
          isDragging: true,
          draggedDocuments: documents 
        });
      },
      
      endDrag: () => {
        set({ 
          isDragging: false,
          draggedDocuments: [],
          draggedOverProjectId: null
        });
      },
      
      setDraggedOver: (projectId) => {
        set({ draggedOverProjectId: projectId });
      },
      
      // Move operations (from original)
      moveDocuments: async (docIds, targetProjectId, onSuccess, onError) => {
        try {
          const documentIds = Array.isArray(docIds) ? docIds : Array.from(docIds);
          
          const updatePromises = documentIds.map(docId => 
            storageWrapper.updateDocument(docId, { project_id: targetProjectId })
          );
          
          await Promise.all(updatePromises);
          
          // Update local state
          set((state) => ({
            documents: state.documents.map(doc => 
              documentIds.includes(doc.id) 
                ? { ...doc, project_id: targetProjectId }
                : doc
            ),
            selectedDocuments: new Set()
          }));
          
          // Re-apply filters
          get().applyFilters();
          
          if (onSuccess) {
            onSuccess(documentIds.length);
          }
        } catch (error) {
          console.error('Failed to move documents:', error);
          if (onError) {
            onError(error);
          }
        }
      },
      
      // Current document methods
      setCurrentDocument: (document) => {
        set({ currentDocument: document });
      },
      
      setCurrentBlocks: (blocks) => {
        set({ currentBlocks: blocks });
      },
      
      // Expanded document methods
      setExpandedDocumentId: (documentId) => {
        set({ expandedDocumentId: documentId });
      },
      
      // Project methods
      setProjects: (projects) => {
        set({ projects });
      },
      
      loadProjects: async (userId) => {
        try {
          const projects = await storageWrapper.getProjects(userId);
          set({ projects });
          return { data: projects, error: null };
        } catch (error) {
          console.error('Error loading projects:', error);
          return { data: null, error };
        }
      },
      
      createProject: async (projectData) => {
        try {
          const newProject = await storageWrapper.createProject(projectData);
          set((state) => ({
            projects: [...state.projects, newProject]
          }));
          return { data: newProject, error: null };
        } catch (error) {
          console.error('Error creating project:', error);
          return { data: null, error };
        }
      },
      
      updateProject: async (projectId, updates) => {
        try {
          await storageWrapper.updateProject(projectId, updates);
          set((state) => ({
            projects: state.projects.map(p => 
              p.id === projectId ? { ...p, ...updates } : p
            )
          }));
          return { error: null };
        } catch (error) {
          console.error('Error updating project:', error);
          return { error };
        }
      },
      
      deleteProject: async (projectId) => {
        try {
          await storageWrapper.deleteProject(projectId);
          set((state) => ({
            projects: state.projects.filter(p => p.id !== projectId),
            selectedProjectId: state.selectedProjectId === projectId ? null : state.selectedProjectId
          }));
          return { error: null };
        } catch (error) {
          console.error('Error deleting project:', error);
          return { error };
        }
      },
      
      setSelectedProjectId: (projectId) => {
        set({ selectedProjectId: projectId });
      },
      
      /**
       * Update all documents (bulk update)
       * @param {Array} updatedDocuments - All documents to update
       */
      updateAllDocuments: async (updatedDocuments) => {
        try {
          // Update local state immediately
          set({ 
            documents: updatedDocuments,
            filteredDocuments: updatedDocuments 
          });
          
          // Save to storage
          await storageWrapper.saveEntries(updatedDocuments);
          
          // Reapply filters
          get().applyFilters();
          
          return { error: null };
        } catch (error) {
          console.error('Error updating all documents:', error);
          return { error };
        }
      },
      
      // Selectors
      selectors: {
        getSelectedCount: () => get().selectedDocuments.size,
        getDocumentById: (id) => get().documents.find(doc => doc.id === id),
        getFilteredCount: () => get().filteredDocuments.length,
        getTotalCount: () => get().documents.length,
      }
    })
  )
);

// Export the hook
export default useDocumentStore;

// Export convenient selectors
export const useDocuments = () => useDocumentStore((state) => state.filteredDocuments);
export const useSelectedDocuments = () => useDocumentStore((state) => state.selectedDocuments);
export const useDocumentLoading = () => useDocumentStore((state) => state.isLoading);
export const useCurrentDocument = () => useDocumentStore((state) => state.currentDocument);
export const useDocumentSearch = () => useDocumentStore((state) => state.searchQuery);
export const useDocumentSort = () => useDocumentStore((state) => ({ 
  sortBy: state.sortBy, 
  sortOrder: state.sortOrder 
}));