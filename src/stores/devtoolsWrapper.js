/**
 * Production-safe devtools wrapper
 * Ensures devtools are completely stripped in production
 */

// Production-safe no-op middleware
const noopMiddleware = (f) => f;

// Export based on environment
export const devtools = import.meta.env.DEV 
  ? (name) => {
      // Only load actual devtools in development
      if (typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__) {
        return (f) => window.__REDUX_DEVTOOLS_EXTENSION__(f, { name });
      }
      return noopMiddleware;
    }
  : () => noopMiddleware;

// Alternative simple devtools that's guaranteed to be stripped
export const simpleDevtools = import.meta.env.DEV
  ? (name) => (stateCreator) => {
      console.log(`[Zustand Store] ${name} initialized`);
      return stateCreator;
    }
  : () => (f) => f;