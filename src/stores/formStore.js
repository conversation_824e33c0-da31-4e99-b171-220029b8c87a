import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';

/**
 * Form Store - Consolidates form state management
 * Manages form data, validation states, and submission states
 */
const useFormStore = create(
  devtools('Forms')(
    (set, get) => ({
      // Form data storage
      forms: {},
      
      // Form states
      dirtyForms: new Set(),
      submittingForms: new Set(),
      formErrors: {},
      
      // Auto-save states
      autoSaveQueue: new Map(),
      lastSavedTimes: {},
      
      // Actions
      /**
       * Initialize a form
       * @param {string} formId - Unique form identifier
       * @param {Object} initialData - Initial form data
       */
      initializeForm: (formId, initialData = {}) => {
        set((state) => ({
          forms: {
            ...state.forms,
            [formId]: initialData
          }
        }));
      },
      
      /**
       * Update form field
       * @param {string} formId - Form identifier
       * @param {string} field - Field name
       * @param {any} value - Field value
       */
      updateField: (formId, field, value) => {
        set((state) => {
          const newForms = {
            ...state.forms,
            [formId]: {
              ...state.forms[formId],
              [field]: value
            }
          };
          
          const newDirtyForms = new Set(state.dirtyForms);
          newDirtyForms.add(formId);
          
          return {
            forms: newForms,
            dirtyForms: newDirtyForms
          };
        });
      },
      
      /**
       * Update multiple fields at once
       * @param {string} formId - Form identifier
       * @param {Object} updates - Field updates
       */
      updateFields: (formId, updates) => {
        set((state) => {
          const newForms = {
            ...state.forms,
            [formId]: {
              ...state.forms[formId],
              ...updates
            }
          };
          
          const newDirtyForms = new Set(state.dirtyForms);
          newDirtyForms.add(formId);
          
          return {
            forms: newForms,
            dirtyForms: newDirtyForms
          };
        });
      },
      
      /**
       * Reset form to initial state
       * @param {string} formId - Form identifier
       * @param {Object} initialData - Data to reset to
       */
      resetForm: (formId, initialData = {}) => {
        set((state) => {
          const newForms = {
            ...state.forms,
            [formId]: initialData
          };
          
          const newDirtyForms = new Set(state.dirtyForms);
          newDirtyForms.delete(formId);
          
          const newFormErrors = { ...state.formErrors };
          delete newFormErrors[formId];
          
          return {
            forms: newForms,
            dirtyForms: newDirtyForms,
            formErrors: newFormErrors
          };
        });
      },
      
      /**
       * Clear form
       * @param {string} formId - Form identifier
       */
      clearForm: (formId) => {
        set((state) => {
          const newForms = { ...state.forms };
          delete newForms[formId];
          
          const newDirtyForms = new Set(state.dirtyForms);
          newDirtyForms.delete(formId);
          
          const newSubmittingForms = new Set(state.submittingForms);
          newSubmittingForms.delete(formId);
          
          const newFormErrors = { ...state.formErrors };
          delete newFormErrors[formId];
          
          const newLastSavedTimes = { ...state.lastSavedTimes };
          delete newLastSavedTimes[formId];
          
          state.autoSaveQueue.delete(formId);
          
          return {
            forms: newForms,
            dirtyForms: newDirtyForms,
            submittingForms: newSubmittingForms,
            formErrors: newFormErrors,
            lastSavedTimes: newLastSavedTimes
          };
        });
      },
      
      /**
       * Set form submission state
       * @param {string} formId - Form identifier
       * @param {boolean} isSubmitting - Whether form is submitting
       */
      setSubmitting: (formId, isSubmitting) => {
        set((state) => {
          const newSubmittingForms = new Set(state.submittingForms);
          
          if (isSubmitting) {
            newSubmittingForms.add(formId);
          } else {
            newSubmittingForms.delete(formId);
          }
          
          return { submittingForms: newSubmittingForms };
        });
      },
      
      /**
       * Set form errors
       * @param {string} formId - Form identifier
       * @param {Object} errors - Form errors
       */
      setFormErrors: (formId, errors) => {
        set((state) => ({
          formErrors: {
            ...state.formErrors,
            [formId]: errors
          }
        }));
      },
      
      /**
       * Clear form errors
       * @param {string} formId - Form identifier
       */
      clearFormErrors: (formId) => {
        set((state) => {
          const newFormErrors = { ...state.formErrors };
          delete newFormErrors[formId];
          return { formErrors: newFormErrors };
        });
      },
      
      /**
       * Mark form as clean (not dirty)
       * @param {string} formId - Form identifier
       */
      markFormClean: (formId) => {
        set((state) => {
          const newDirtyForms = new Set(state.dirtyForms);
          newDirtyForms.delete(formId);
          
          return {
            dirtyForms: newDirtyForms,
            lastSavedTimes: {
              ...state.lastSavedTimes,
              [formId]: new Date().toISOString()
            }
          };
        });
      },
      
      /**
       * Queue form for auto-save
       * @param {string} formId - Form identifier
       * @param {Function} saveFunction - Function to call for saving
       */
      queueAutoSave: (formId, saveFunction) => {
        const { autoSaveQueue } = get();
        autoSaveQueue.set(formId, saveFunction);
      },
      
      /**
       * Execute auto-save for a form
       * @param {string} formId - Form identifier
       */
      executeAutoSave: async (formId) => {
        const { autoSaveQueue, forms } = get();
        const saveFunction = autoSaveQueue.get(formId);
        
        if (saveFunction && forms[formId]) {
          try {
            await saveFunction(forms[formId]);
            get().markFormClean(formId);
            autoSaveQueue.delete(formId);
          } catch (error) {
            console.error(`Auto-save failed for form ${formId}:`, error);
          }
        }
      },
      
      // Selectors
      selectors: {
        getFormData: (formId) => get().forms[formId] || {},
        getFormErrors: (formId) => get().formErrors[formId] || {},
        isFormDirty: (formId) => get().dirtyForms.has(formId),
        isFormSubmitting: (formId) => get().submittingForms.has(formId),
        getLastSavedTime: (formId) => get().lastSavedTimes[formId],
        hasAutoSavePending: (formId) => get().autoSaveQueue.has(formId)
      }
    })
  )
);

// Export the hook
export default useFormStore;

// Export convenient selectors
export const useFormData = (formId) => useFormStore((state) => state.forms[formId] || {});
export const useFormField = (formId, field) => useFormStore((state) => state.forms[formId]?.[field]);
export const useFormErrors = (formId) => useFormStore((state) => state.formErrors[formId] || {});
export const useIsFormDirty = (formId) => useFormStore((state) => state.dirtyForms.has(formId));
export const useIsFormSubmitting = (formId) => useFormStore((state) => state.submittingForms.has(formId));