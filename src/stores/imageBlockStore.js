import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';

/**
 * Image Block Store - Manages UI state specific to image blocks
 * Complements blockEditorStore with image-specific functionality
 */
const useImageBlockStore = create(
  devtools('ImageBlock')(
    (set, get) => ({
      // Image block UI states (keyed by blockId)
      imageBlockStates: {},
      
      /**
       * Initialize image block state
       * @param {string} blockId - Block ID
       * @param {Object} initialState - Initial state values
       */
      initializeImageBlock: (blockId, initialState = {}) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              isUploading: false,
              uploadProgress: 0,
              error: null,
              editingImageId: null,
              draggedImageId: null,
              lightboxImage: null,
              lightboxIndex: 0,
              ...initialState
            }
          }
        }));
      },
      
      /**
       * Update image block state
       * @param {string} blockId - Block ID
       * @param {Object} updates - State updates
       */
      updateImageBlockState: (blockId, updates) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              ...updates
            }
          }
        }));
      },
      
      /**
       * Set uploading state
       * @param {string} blockId - Block ID
       * @param {boolean} isUploading - Whether uploading
       */
      setUploading: (blockId, isUploading) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              isUploading
            }
          }
        }));
      },
      
      /**
       * Set upload progress
       * @param {string} blockId - Block ID
       * @param {number} progress - Upload progress (0-100)
       */
      setUploadProgress: (blockId, progress) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              uploadProgress: progress
            }
          }
        }));
      },
      
      /**
       * Set error state
       * @param {string} blockId - Block ID
       * @param {string|null} error - Error message
       */
      setError: (blockId, error) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              error
            }
          }
        }));
      },
      
      /**
       * Set editing image ID
       * @param {string} blockId - Block ID
       * @param {string|null} imageId - Image ID being edited
       */
      setEditingImageId: (blockId, imageId) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              editingImageId: imageId
            }
          }
        }));
      },
      
      /**
       * Set dragged image ID
       * @param {string} blockId - Block ID
       * @param {string|null} imageId - Image ID being dragged
       */
      setDraggedImageId: (blockId, imageId) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              draggedImageId: imageId
            }
          }
        }));
      },
      
      /**
       * Set lightbox state
       * @param {string} blockId - Block ID
       * @param {Object|null} image - Lightbox image
       * @param {number} index - Image index
       */
      setLightbox: (blockId, image, index = 0) => {
        set((state) => ({
          imageBlockStates: {
            ...state.imageBlockStates,
            [blockId]: {
              ...state.imageBlockStates[blockId],
              lightboxImage: image,
              lightboxIndex: index
            }
          }
        }));
      },
      
      /**
       * Clear image block state (when unmounting)
       * @param {string} blockId - Block ID
       */
      clearImageBlockState: (blockId) => {
        set((state) => {
          const newStates = { ...state.imageBlockStates };
          delete newStates[blockId];
          return { imageBlockStates: newStates };
        });
      },
      
      /**
       * Get image block state
       * @param {string} blockId - Block ID
       * @returns {Object} Image block state
       */
      getImageBlockState: (blockId) => {
        return get().imageBlockStates[blockId] || {
          isUploading: false,
          uploadProgress: 0,
          error: null,
          editingImageId: null,
          draggedImageId: null,
          lightboxImage: null,
          lightboxIndex: 0
        };
      }
    })
  )
);

export default useImageBlockStore;

// Export convenient selectors
export const useImageBlockUI = (blockId) => 
  useImageBlockStore((state) => state.getImageBlockState(blockId));