import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';

/**
 * Code Block Store - Manages UI state specific to code blocks
 * Complements blockEditorStore with code-specific functionality
 */
const useCodeBlockStore = create(
  devtools('CodeBlock')(
    (set, get) => ({
      // Code block UI states (keyed by blockId)
      codeBlockStates: {},
      
      /**
       * Initialize code block state
       * @param {string} blockId - Block ID
       * @param {Object} initialState - Initial state values
       */
      initializeCodeBlock: (blockId, initialState = {}) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              copied: false,
              isFullscreen: false,
              isExpanded: false,
              viewMode: 'normal',
              showLanguageDropdown: false,
              showFilePathSuggestions: false,
              filePathSuggestions: [],
              ...initialState
            }
          }
        }));
      },
      
      /**
       * Update code block state
       * @param {string} blockId - Block ID
       * @param {Object} updates - State updates
       */
      updateCodeBlockState: (blockId, updates) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              ...updates
            }
          }
        }));
      },
      
      /**
       * Set copied state
       * @param {string} blockId - Block ID
       * @param {boolean} copied - Whether code was copied
       */
      setCopied: (blockId, copied) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              copied
            }
          }
        }));
      },
      
      /**
       * Set fullscreen state
       * @param {string} blockId - Block ID
       * @param {boolean} isFullscreen - Whether in fullscreen mode
       */
      setFullscreen: (blockId, isFullscreen) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              isFullscreen
            }
          }
        }));
      },
      
      /**
       * Set expanded state
       * @param {string} blockId - Block ID
       * @param {boolean} isExpanded - Whether code is expanded
       */
      setExpanded: (blockId, isExpanded) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              isExpanded
            }
          }
        }));
      },
      
      /**
       * Set view mode
       * @param {string} blockId - Block ID
       * @param {string} viewMode - View mode ('normal' or 'compact')
       */
      setViewMode: (blockId, viewMode) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              viewMode
            }
          }
        }));
      },
      
      /**
       * Set language dropdown visibility
       * @param {string} blockId - Block ID
       * @param {boolean} show - Whether to show dropdown
       */
      setShowLanguageDropdown: (blockId, show) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              showLanguageDropdown: show
            }
          }
        }));
      },
      
      /**
       * Set file path suggestions
       * @param {string} blockId - Block ID
       * @param {boolean} show - Whether to show suggestions
       * @param {Array} suggestions - Suggestion list
       */
      setFilePathSuggestions: (blockId, show, suggestions = []) => {
        set((state) => ({
          codeBlockStates: {
            ...state.codeBlockStates,
            [blockId]: {
              ...state.codeBlockStates[blockId],
              showFilePathSuggestions: show,
              filePathSuggestions: suggestions
            }
          }
        }));
      },
      
      /**
       * Clear code block state (when unmounting)
       * @param {string} blockId - Block ID
       */
      clearCodeBlockState: (blockId) => {
        set((state) => {
          const newStates = { ...state.codeBlockStates };
          delete newStates[blockId];
          return { codeBlockStates: newStates };
        });
      },
      
      /**
       * Get code block state
       * @param {string} blockId - Block ID
       * @returns {Object} Code block state
       */
      getCodeBlockState: (blockId) => {
        return get().codeBlockStates[blockId] || {
          copied: false,
          isFullscreen: false,
          isExpanded: false,
          viewMode: 'normal',
          showLanguageDropdown: false,
          showFilePathSuggestions: false,
          filePathSuggestions: []
        };
      }
    })
  )
);

export default useCodeBlockStore;

// Export convenient selectors
export const useCodeBlockUI = (blockId) => 
  useCodeBlockStore((state) => state.getCodeBlockState(blockId));