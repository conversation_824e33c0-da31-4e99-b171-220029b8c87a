/**
 * Central export for all Zustand stores
 * Import stores from here for consistency
 */

// Core stores
export { default as useAuthStore } from './authStore';
export { default as useSettingsStore } from './settingsStore';
export { default as useUIStore } from './uiStore';
export { default as useDocumentStore } from './documentStore';
export { default as useEditorStore } from './editorStore';
export { default as useFormStore } from './formStore';
export { default as useBlockEditorStore } from './blockEditorStore';

// Migration helpers
export { createContextCompatible, migrateFromLocalStorage, syncWithLocalStorage } from './migrationHelper.jsx';

// Re-export specific selectors for convenience
export * from './authStore';
export * from './settingsStore';
export * from './uiStore';
export * from './documentStore';
export * from './editorStore';
export * from './formStore';
export * from './blockEditorStore';

// DevTools helper
export { devtools } from './migrationHelper.jsx';

/**
 * Global state debugger
 * Access all store states for debugging
 */
if (import.meta.env.DEV) {
  window.__APP_STATE__ = {
    get auth() { 
      return useAuthStore.getState(); 
    },
    get settings() { 
      return useSettingsStore.getState(); 
    },
    get ui() { 
      return useUIStore.getState(); 
    },
    get documents() { 
      return useDocumentStore.getState(); 
    },
    get editor() { 
      return useEditorStore.getState(); 
    },
    get forms() { 
      return useFormStore.getState(); 
    },
    get blockEditor() { 
      return useBlockEditorStore.getState(); 
    },
    // Helper to log all states
    logAll() {
      console.group('🏪 App State');
      console.log('Auth:', this.auth);
      console.log('Settings:', this.settings);
      console.log('UI:', this.ui);
      console.log('Documents:', this.documents);
      console.log('Editor:', this.editor);
      console.log('Forms:', this.forms);
      console.log('BlockEditor:', this.blockEditor);
      console.groupEnd();
    },
    // Helper to reset specific store
    reset(storeName) {
      switch(storeName) {
        case 'auth':
          useAuthStore.setState({ user: null, session: null, loading: false });
          break;
        case 'settings':
          useSettingsStore.getState().resetSettings();
          break;
        case 'ui':
          useUIStore.setState({ 
            sidebarOpen: true, 
            mobileMenuOpen: false,
            modals: { settings: false, share: false, delete: false, folder: false },
            toasts: []
          });
          break;
        case 'documents':
          useDocumentStore.setState({ 
            documents: [], 
            selectedDocuments: new Set(),
            searchQuery: '',
            filterTags: []
          });
          break;
        case 'editor':
          useEditorStore.setState({ 
            activeBlockId: null,
            activeBlockType: null,
            isEditing: false,
            history: [],
            historyIndex: -1
          });
          break;
        case 'forms':
          useFormStore.setState({ 
            forms: {},
            dirtyForms: new Set(),
            submittingForms: new Set(),
            formErrors: {},
            autoSaveQueue: new Map()
          });
          break;
        default:
          console.error(`Unknown store: ${storeName}`);
      }
    }
  };
  
  console.log('🏪 Zustand DevTools Ready! Access stores via window.__APP_STATE__');
  console.log('Available commands:');
  console.log('- __APP_STATE__.logAll() - Log all store states');
  console.log('- __APP_STATE__.auth - Access auth store state');
  console.log('- __APP_STATE__.settings - Access settings store state');
  console.log('- __APP_STATE__.ui - Access UI store state');
  console.log('- __APP_STATE__.documents - Access documents store state');
  console.log('- __APP_STATE__.editor - Access editor store state');
  console.log('- __APP_STATE__.forms - Access forms store state');
  console.log('- __APP_STATE__.reset("storeName") - Reset a specific store');
}