import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';
import { supabase } from '../lib/supabase';
import { setInactivityTimeout } from '../lib/supabaseOptimized';

/**
 * Settings store - Replaces SettingsContext
 * Manages user preferences and application settings
 */
const useSettingsStore = create(
  devtools('Settings')(
    (set, get) => ({
      // Settings state
      settings: {
        defaultCodeLanguage: 'javascript',
        autoSaveInterval: 30, // seconds
        showLineNumbers: true,
        enableTextCollapse: true,
        sessionTimeout: 30, // minutes
      },
      
      isLoading: true,
      
      // Actions
      /**
       * Initialize settings from localStorage and database
       * @param {Object} user - Current user object
       */
      initializeSettings: async (user) => {
        // Load from localStorage first for immediate access
        const localSettings = localStorage.getItem('devlogSettings');
        if (localSettings) {
          try {
            const parsed = JSON.parse(localSettings);
            set((state) => ({
              settings: { ...state.settings, ...parsed }
            }));
          } catch (err) {
            console.error('Error parsing local settings:', err);
          }
        }
        
        // If no user, we're done
        if (!user) {
          set({ isLoading: false });
          return;
        }
        
        // Load from database
        try {
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('settings')
            .eq('id', user.id)
            .single();
          
          if (error) {
            console.error('Error loading profile settings:', error);
          } else if (profile?.settings) {
            const profileSettings = profile.settings;
            
            set((state) => ({
              settings: { ...state.settings, ...profileSettings },
              isLoading: false
            }));
            
            // Update local cache
            localStorage.setItem('devlogSettings', JSON.stringify(profileSettings));
            
            // Apply session timeout if set
            if (profileSettings.sessionTimeout !== undefined) {
              setInactivityTimeout(profileSettings.sessionTimeout);
            }
          } else {
            set({ isLoading: false });
          }
        } catch (err) {
          console.error('Error loading settings from profiles:', err);
          set({ isLoading: false });
        }
      },
      
      /**
       * Update a single setting
       * @param {string} key - Setting key
       * @param {any} value - New value
       * @param {Object} user - Current user object
       */
      updateSetting: async (key, value, user) => {
        const newSettings = { ...get().settings, [key]: value };
        
        // Update state immediately
        set({ settings: newSettings });
        
        // Save to localStorage immediately
        localStorage.setItem('devlogSettings', JSON.stringify(newSettings));
        
        // Apply session timeout immediately if changed
        if (key === 'sessionTimeout') {
          setInactivityTimeout(value);
        }
        
        // Save to database if user is authenticated
        if (user) {
          try {
            const { error } = await supabase
              .from('profiles')
              .update({ 
                settings: newSettings,
                updated_at: new Date().toISOString()
              })
              .eq('id', user.id);
            
            if (error) {
              console.error('Error saving settings to profile:', error);
              // Could revert on error if needed
            }
          } catch (err) {
            console.error('Error updating profile settings:', err);
          }
        }
      },
      
      /**
       * Update multiple settings at once
       * @param {Object} updates - Object with setting updates
       * @param {Object} user - Current user object
       */
      updateSettings: async (updates, user) => {
        const newSettings = { ...get().settings, ...updates };
        
        // Update state
        set({ settings: newSettings });
        
        // Save to localStorage
        localStorage.setItem('devlogSettings', JSON.stringify(newSettings));
        
        // Apply session timeout if included
        if (updates.sessionTimeout !== undefined) {
          setInactivityTimeout(updates.sessionTimeout);
        }
        
        // Save to database if user is authenticated
        if (user) {
          try {
            const { error } = await supabase
              .from('profiles')
              .update({ 
                settings: newSettings,
                updated_at: new Date().toISOString()
              })
              .eq('id', user.id);
            
            if (error) {
              console.error('Error saving settings to profile:', error);
            }
          } catch (err) {
            console.error('Error updating profile settings:', err);
          }
        }
      },
      
      /**
       * Reset settings to defaults
       */
      resetSettings: () => {
        const defaults = {
          defaultCodeLanguage: 'javascript',
          autoSaveInterval: 30,
          showLineNumbers: true,
          enableTextCollapse: true,
          sessionTimeout: 30,
        };
        
        set({ settings: defaults });
        localStorage.setItem('devlogSettings', JSON.stringify(defaults));
        setInactivityTimeout(defaults.sessionTimeout);
      },
      
      // Selectors (for optimized subscriptions)
      selectors: {
        getAutoSaveInterval: () => get().settings.autoSaveInterval,
        getDefaultLanguage: () => get().settings.defaultCodeLanguage,
        getSessionTimeout: () => get().settings.sessionTimeout,
        getSetting: (key) => get().settings[key],
      }
    })
  )
);

// Export the hook
export default useSettingsStore;

// Export convenient selectors
export const useAutoSaveInterval = () => useSettingsStore((state) => state.settings.autoSaveInterval);
export const useDefaultLanguage = () => useSettingsStore((state) => state.settings.defaultCodeLanguage);
export const useSessionTimeout = () => useSettingsStore((state) => state.settings.sessionTimeout);
export const useSetting = (key) => useSettingsStore((state) => state.settings[key]);