import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';

/**
 * Editor Store - Consolidates editor-related state
 * Manages cursor position, selection, active block, etc.
 */
const useEditorStore = create(
  devtools('Editor')(
    (set, get) => ({
      // Active document
      activeDocumentId: null,
      
      // Document metadata
      documentTitle: '',
      documentTags: [],
      backlinks: [],
      
      // Active block state
      activeBlockId: null,
      activeBlockType: null,
      focusedBlockId: null,
      hoveredBlockId: null,
      
      // Block selector
      showBlockSelector: false,
      selectorPosition: null,
      
      // Title editing
      isEditingTitle: false,
      
      // Tag editing
      isAddingTag: false,
      newTag: '',
      editingTagIndex: null,
      editingTagValue: '',
      
      // Cursor and selection
      cursorPosition: null,
      selectionRange: null,
      
      // Editor modes
      isEditing: false,
      editMode: 'text', // 'text' | 'code' | 'markdown'
      viewMode: 'blocks', // 'blocks' or 'lines'
      selectedLineBlockId: null,
      linesScrollProgress: { top: 0, bottom: 1 },
      
      // Block manipulation
      isDraggingBlock: false,
      draggedBlockId: null,
      dropTargetId: null,
      dropPosition: 'after', // 'before' or 'after'
      
      // Save state
      saveStatus: null,
      isInternalUpdate: false,
      
      // Undo/Redo state
      history: [],
      historyIndex: -1,
      maxHistorySize: 50,
      
      // Editor preferences
      fontSize: 14,
      fontFamily: 'monospace',
      tabSize: 2,
      wordWrap: true,
      
      // Actions
      /**
       * Set active document
       * @param {string} documentId - Document ID
       */
      setActiveDocument: (documentId) => {
        set({ activeDocumentId: documentId });
      },
      
      /**
       * Clear active document (on unmount)
       */
      clearActiveDocument: () => {
        set({ 
          activeDocumentId: null,
          documentTitle: '',
          documentTags: [],
          backlinks: [],
          saveStatus: null
        });
      },
      
      /**
       * Set document title
       * @param {string} title - Document title
       */
      setDocumentTitle: (title) => {
        set({ documentTitle: title });
      },
      
      /**
       * Set document tags
       * @param {string[]} tags - Document tags
       */
      setDocumentTags: (tags) => {
        set({ documentTags: tags });
      },
      
      /**
       * Set backlinks
       * @param {Array} backlinks - Document backlinks
       */
      setBacklinks: (backlinks) => {
        set({ backlinks });
      },
      
      /**
       * Block selector management
       */
      setShowBlockSelector: (show) => {
        set({ showBlockSelector: show });
      },
      
      setSelectorPosition: (position) => {
        set({ selectorPosition: position });
      },
      
      /**
       * Title editing
       */
      setIsEditingTitle: (editing) => {
        set({ isEditingTitle: editing });
      },
      
      /**
       * Tag editing
       */
      setIsAddingTag: (adding) => {
        set({ isAddingTag: adding });
      },
      
      setNewTag: (tag) => {
        set({ newTag: tag });
      },
      
      setEditingTagIndex: (index) => {
        set({ editingTagIndex: index });
      },
      
      setEditingTagValue: (value) => {
        set({ editingTagValue: value });
      },
      
      /**
       * Block focus
       */
      setFocusedBlockId: (blockId) => {
        set({ focusedBlockId: blockId });
      },
      
      setHoveredBlockId: (blockId) => {
        set({ hoveredBlockId: blockId });
      },
      
      /**
       * View mode
       */
      setViewMode: (mode) => {
        set({ viewMode: mode });
      },
      
      setSelectedLineBlockId: (blockId) => {
        set({ selectedLineBlockId: blockId });
      },
      
      setLinesScrollProgress: (progress) => {
        set({ linesScrollProgress: progress });
      },
      
      /**
       * Drag and drop
       */
      setDraggedBlockId: (blockId) => {
        set({ draggedBlockId: blockId });
      },
      
      setDropTargetId: (targetId) => {
        set({ dropTargetId: targetId });
      },
      
      setDropPosition: (position) => {
        set({ dropPosition: position });
      },
      
      /**
       * Save status
       */
      setSaveStatus: (status) => {
        set({ saveStatus: status });
      },
      
      /**
       * Internal update tracking
       */
      setIsInternalUpdate: (internal) => {
        set({ isInternalUpdate: internal });
      },
      
      /**
       * Set active block
       * @param {string} blockId - Block ID
       * @param {string} blockType - Block type
       */
      setActiveBlock: (blockId, blockType) => {
        set({ 
          activeBlockId: blockId, 
          activeBlockType: blockType 
        });
      },
      
      /**
       * Clear active block
       */
      clearActiveBlock: () => {
        set({ 
          activeBlockId: null, 
          activeBlockType: null,
          isEditing: false 
        });
      },
      
      /**
       * Set cursor position
       * @param {Object} position - Cursor position {line, column}
       */
      setCursorPosition: (position) => {
        set({ cursorPosition: position });
      },
      
      /**
       * Set selection range
       * @param {Object} range - Selection range {start, end}
       */
      setSelectionRange: (range) => {
        set({ selectionRange: range });
      },
      
      /**
       * Toggle edit mode
       */
      toggleEditMode: () => {
        set((state) => ({ isEditing: !state.isEditing }));
      },
      
      /**
       * Set edit mode
       * @param {boolean} editing - Whether editing
       */
      setEditMode: (editing) => {
        set({ isEditing: editing });
      },
      
      /**
       * Start dragging block
       * @param {string} blockId - Block ID being dragged
       */
      startDraggingBlock: (blockId) => {
        set({ 
          isDraggingBlock: true, 
          draggedBlockId: blockId 
        });
      },
      
      /**
       * End dragging block
       */
      endDraggingBlock: () => {
        set({ 
          isDraggingBlock: false, 
          draggedBlockId: null,
          dropTargetId: null 
        });
      },
      
      /**
       * Set drop target
       * @param {string} targetId - Target block ID
       */
      setDropTarget: (targetId) => {
        set({ dropTargetId: targetId });
      },
      
      /**
       * Add to history (for undo/redo)
       * @param {Object} state - State snapshot
       */
      addToHistory: (state) => {
        const { history, historyIndex, maxHistorySize } = get();
        
        // Remove any redo history
        const newHistory = history.slice(0, historyIndex + 1);
        
        // Add new state
        newHistory.push(state);
        
        // Limit history size
        if (newHistory.length > maxHistorySize) {
          newHistory.shift();
        }
        
        set({ 
          history: newHistory,
          historyIndex: newHistory.length - 1
        });
      },
      
      /**
       * Undo last action
       */
      undo: () => {
        const { history, historyIndex } = get();
        
        if (historyIndex > 0) {
          set({ historyIndex: historyIndex - 1 });
          return history[historyIndex - 1];
        }
        
        return null;
      },
      
      /**
       * Redo last undone action
       */
      redo: () => {
        const { history, historyIndex } = get();
        
        if (historyIndex < history.length - 1) {
          set({ historyIndex: historyIndex + 1 });
          return history[historyIndex + 1];
        }
        
        return null;
      },
      
      /**
       * Update editor preferences
       * @param {Object} prefs - Preferences to update
       */
      updatePreferences: (prefs) => {
        set((state) => ({
          fontSize: prefs.fontSize ?? state.fontSize,
          fontFamily: prefs.fontFamily ?? state.fontFamily,
          tabSize: prefs.tabSize ?? state.tabSize,
          wordWrap: prefs.wordWrap ?? state.wordWrap
        }));
      },
      
      // Selectors
      selectors: {
        isBlockActive: (blockId) => get().activeBlockId === blockId,
        canUndo: () => get().historyIndex > 0,
        canRedo: () => get().historyIndex < get().history.length - 1,
        getPreferences: () => ({
          fontSize: get().fontSize,
          fontFamily: get().fontFamily,
          tabSize: get().tabSize,
          wordWrap: get().wordWrap
        })
      }
    })
  )
);

// Export the hook
export default useEditorStore;

// Export convenient selectors
export const useActiveBlock = () => useEditorStore((state) => ({
  id: state.activeBlockId,
  type: state.activeBlockType
}));
export const useIsEditing = () => useEditorStore((state) => state.isEditing);
export const useEditorPreferences = () => useEditorStore((state) => ({
  fontSize: state.fontSize,
  fontFamily: state.fontFamily,
  tabSize: state.tabSize,
  wordWrap: state.wordWrap
}));
export const useDragState = () => useEditorStore((state) => ({
  isDragging: state.isDraggingBlock,
  draggedId: state.draggedBlockId,
  dropTargetId: state.dropTargetId
}));