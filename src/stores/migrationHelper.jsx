import { createContext, useContext as useReactContext } from 'react';

/**
 * Creates Context-compatible wrappers for Zustand stores
 * This allows gradual migration without breaking existing code
 * @param {Function} useStore - Zustand store hook
 * @param {string} displayName - Display name for debugging
 * @returns {Object} Context-compatible Provider and hook
 */
export function createContextCompatible(useStore, displayName = 'Store') {
  // Create a context that holds the store
  const StoreContext = createContext(null);
  StoreContext.displayName = displayName;
  
  /**
   * Provider component that makes the store available
   * Works exactly like Context.Provider for drop-in replacement
   */
  const Provider = ({ children }) => {
    // We pass the hook itself, not the state
    return (
      <StoreContext.Provider value={useStore}>
        {children}
      </StoreContext.Provider>
    );
  };
  
  /**
   * Hook that works like useContext but for Zustand
   * Falls back to direct store usage if no provider
   */
  const useContext = () => {
    const store = useReactContext(StoreContext);
    // If no provider, use store directly (for gradual migration)
    if (!store) {
      console.warn(`${displayName} accessed without Provider. Using direct store access.`);
      return useStore();
    }
    return store();
  };
  
  return { 
    Provider, 
    useContext,
    useStore // Export original for direct access
  };
}

/**
 * Helper to migrate localStorage data to Zustand store
 * @param {string} key - localStorage key
 * @param {Function} setState - Zustand setState function
 * @param {Function} transform - Optional transform function
 */
export function migrateFromLocalStorage(key, setState, transform = (x) => x) {
  try {
    const stored = localStorage.getItem(key);
    if (stored) {
      const data = JSON.parse(stored);
      const transformed = transform(data);
      setState(transformed);
      return transformed;
    }
  } catch (error) {
    console.error(`Failed to migrate ${key} from localStorage:`, error);
  }
  return null;
}

/**
 * Helper to sync Zustand store with localStorage
 * @param {Function} useStore - Zustand store hook
 * @param {string} key - localStorage key
 * @param {Function} selector - Optional state selector
 */
export function syncWithLocalStorage(useStore, key, selector) {
  return useStore.subscribe((state) => {
    const dataToStore = selector ? selector(state) : state;
    localStorage.setItem(key, JSON.stringify(dataToStore));
  });
}

/**
 * DevTools connector for better debugging
 * @param {string} name - Store name for DevTools
 * @returns {Function} Middleware function
 */
export function devtools(name) {
  // Return a no-op in production
  if (!import.meta.env.DEV) {
    return (stateCreator) => stateCreator;
  }
  
  return (set, get, api) => {
    // Only in development
    if (import.meta.env.DEV && window.__REDUX_DEVTOOLS_EXTENSION__) {
      const devtools = window.__REDUX_DEVTOOLS_EXTENSION__.connect({ name });
      
      // Subscribe to DevTools
      devtools.subscribe((message) => {
        if (message.type === 'DISPATCH' && message.state) {
          set(JSON.parse(message.state), false, 'devtools');
        }
      });
      
      // Initial state
      devtools.init(get());
      
      // Wrap set to send actions to DevTools
      const originalSet = set;
      set = (partial, replace, action) => {
        const result = originalSet(partial, replace, action);
        const state = get();
        devtools.send(action || 'setState', state);
        return result;
      };
    }
    
    return { set, get, api };
  };
}