import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';
import { supabase } from '../lib/supabaseOptimized';
import { clearCorruptedAuthStorage } from '../utils/clearAuthStorage';

/**
 * Auth Store - Replaces AuthContext
 * Manages authentication state and user data
 */
const useAuthStore = create(
  devtools('Auth')(
    (set, get) => ({
      // Auth state
      user: null,
      session: null,
      loading: true,
      error: null,
      trialStatus: null,
      
      // Initialization flag to prevent multiple initializations
      isInitialized: false,
      isInitializing: false, // Add this to prevent concurrent initialization
      
      // Subscription for auth state changes
      authSubscription: null,
      
      // Actions
      /**
       * Initialize auth state and listen for changes
       */
      initialize: async () => {
        const state = get();
        
        // Check if already initialized OR currently initializing
        if (state.isInitialized || state.isInitializing) {
          console.log('[AuthStore] Already initialized or initializing, skipping');
          return;
        }
        
        // Mark as initializing
        set({ isInitializing: true });
        
        // Clear any corrupted storage first
        clearCorruptedAuthStorage();
        
        // Set up auth state change listener
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          async (_event, session) => {
            set({ 
              session,
              user: session?.user ?? null 
            });
            
            // Check trial status when user logs in
            if (session?.user?.id) {
              await get().checkTrialStatus(session.user.id);
            } else {
              set({ trialStatus: null });
            }
          }
        );
        
        // Store subscription for cleanup
        set({ authSubscription: subscription });
        
        // Get initial session
        try {
          const sessionPromise = supabase.auth.getSession();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Session timeout')), 5000)
          );
          
          const { data: { session }, error } = await Promise.race([
            sessionPromise,
            timeoutPromise
          ]);
          
          if (error) {
            console.error('Session error:', error);
            set({ error: error.message });
            
            // Try to refresh if we have a session error
            if (error.message.includes('refresh_token') || error.message.includes('expired')) {
              console.log('[AuthStore] Attempting to refresh expired session...');
              try {
                const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
                if (!refreshError && refreshData?.session) {
                  set({ 
                    user: refreshData.session.user,
                    session: refreshData.session,
                    error: null,
                    isInitialized: true,
                    isInitializing: false
                  });
                  
                  if (refreshData.session.user?.id) {
                    await get().checkTrialStatus(refreshData.session.user.id);
                  }
                }
              } catch (refreshErr) {
                console.error('Session refresh failed:', refreshErr);
                set({ 
                  isInitialized: true,
                  isInitializing: false
                });
              }
            } else {
              set({ 
                isInitialized: true,
                isInitializing: false
              });
            }
          } else {
            set({ 
              session,
              user: session?.user ?? null,
              error: null,
              isInitialized: true,
              isInitializing: false
            });
            
            // Check trial status for initial session
            if (session?.user?.id) {
              await get().checkTrialStatus(session.user.id);
            }
          }
        } catch (error) {
          console.error('Auth initialization failed:', error);
          set({ 
            error: error.message,
            isInitialized: false,
            isInitializing: false,
            loading: false
          });
          // Don't throw - handle gracefully
        }
      },
      
      /**
       * Clean up subscriptions
       */
      cleanup: () => {
        const { authSubscription } = get();
        if (authSubscription) {
          authSubscription.unsubscribe();
        }
        // Reset initialization flags so it can be re-initialized if needed
        set({ isInitialized: false, isInitializing: false, authSubscription: null });
      },
      
      /**
       * Sign in with email and password
       * @param {string} email - User email
       * @param {string} password - User password
       */
      signIn: async (email, password) => {
        try {
          set({ error: null });
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
          });
          
          if (error) {
            set({ error: error.message });
            return { data: null, error };
          }
          
          return { data, error: null };
        } catch (err) {
          set({ error: err.message });
          return { data: null, error: err };
        }
      },
      
      /**
       * Sign up with email and password
       * @param {string} email - User email
       * @param {string} password - User password
       */
      signUp: async (email, password) => {
        try {
          set({ error: null });
          const { data, error } = await supabase.auth.signUp({
            email,
            password,
            options: {
              emailRedirectTo: `${window.location.origin}/auth/callback`
            }
          });
          
          if (error) {
            set({ error: error.message });
            return { data: null, error };
          }
          
          return { data, error: null };
        } catch (err) {
          set({ error: err.message });
          return { data: null, error: err };
        }
      },
      
      /**
       * Sign in with OAuth provider
       * @param {'github' | 'google'} provider - OAuth provider
       */
      signInWithProvider: async (provider) => {
        const { data, error } = await supabase.auth.signInWithOAuth({
          provider,
          options: {
            redirectTo: `${window.location.origin}/auth/callback`
          }
        });
        return { data, error };
      },
      
      /**
       * Sign out
       */
      signOut: async () => {
        const { error } = await supabase.auth.signOut();
        if (!error) {
          set({ user: null, session: null, trialStatus: null });
        }
        return { error };
      },
      
      /**
       * Send password reset email
       * @param {string} email - User email
       */
      resetPassword: async (email) => {
        const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/reset-password`
        });
        return { data, error };
      },
      
      /**
       * Update user password
       * @param {string} newPassword - New password
       */
      updatePassword: async (newPassword) => {
        const { data, error } = await supabase.auth.updateUser({
          password: newPassword
        });
        return { data, error };
      },
      
      /**
       * Check trial status
       * @param {string} userId - User ID
       */
      checkTrialStatus: async (userId) => {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('trial_start, trial_end')
            .eq('id', userId)
            .single();
          
          if (error) {
            console.error('Error checking trial status:', error);
            return;
          }
          
          if (data) {
            const now = new Date();
            const trialEnd = data.trial_end ? new Date(data.trial_end) : null;
            const trialStart = data.trial_start ? new Date(data.trial_start) : null;
            
            const status = {
              isActive: trialEnd ? now < trialEnd : false,
              daysRemaining: trialEnd ? Math.ceil((trialEnd - now) / (1000 * 60 * 60 * 24)) : 0,
              trialEnd,
              trialStart
            };
            
            set({ trialStatus: status });
          }
        } catch (error) {
          console.error('Error in checkTrialStatus:', error);
        }
      },
      
      // Selectors
      selectors: {
        isAuthenticated: () => !!get().user,
        isLoading: () => get().loading,
        getUserId: () => get().user?.id,
        getEmail: () => get().user?.email,
        isTrialActive: () => get().trialStatus?.isActive || false,
      }
    })
  )
);

// Export the hook
export default useAuthStore;

// Export convenient selectors
export const useUser = () => useAuthStore((state) => state.user);
export const useSession = () => useAuthStore((state) => state.session);
export const useAuthLoading = () => useAuthStore((state) => state.loading);
export const useTrialStatus = () => useAuthStore((state) => state.trialStatus);
export const useIsAuthenticated = () => useAuthStore((state) => !!state.user);