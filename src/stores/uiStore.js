import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';

/**
 * UI Store - Replaces SidebarContext and consolidates UI state
 * Manages all UI-related state in one place
 */
const useUIStore = create(
  devtools('UI')(
    (set, get) => ({
      // Sidebar state
      sidebarOpen: true,
      sidebarWidth: 280,
      
      // Mobile UI state
      mobileMenuOpen: false,
      showMobileSidebarSheet: false,
      showMobileContextMenu: false,
      
      // UI component states
      showProfileMenu: false,
      contextMenuTarget: null,
      linkCallback: null,
      editingProject: null,
      storageInfo: null,
      
      // Device state
      isMobile: window.innerWidth < 768,
      
      // Modal states
      modals: {
        settings: false,
        share: false,
        delete: false,
        folder: false,
        link: false,
        project: false,
        commandPalette: false,
        deleteDocument: false,
        shareDocument: false,
      },
      
      // View preferences
      viewMode: 'grid', // 'grid' | 'list'
      
      // Loading states
      loadingStates: {
        documents: false,
        save: false,
        delete: false,
        deleteDocument: false,
      },
      
      // Toast/notification state
      toasts: [],
      
      // Actions
      /**
       * Toggle sidebar open/closed
       */
      toggleSidebar: () => set((state) => ({ 
        sidebarOpen: !state.sidebarOpen 
      })),
      
      /**
       * Set sidebar open state
       * @param {boolean} open - Whether sidebar should be open
       */
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      
      /**
       * Set sidebar width
       * @param {number} width - Width in pixels
       */
      setSidebarWidth: (width) => set({ sidebarWidth: width }),
      
      /**
       * Toggle mobile menu
       */
      toggleMobileMenu: () => set((state) => ({ 
        mobileMenuOpen: !state.mobileMenuOpen 
      })),
      
      /**
       * Set mobile menu state
       * @param {boolean} open - Whether mobile menu should be open
       */
      setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),
      
      /**
       * Open a modal
       * @param {string} modalName - Name of the modal to open
       */
      openModal: (modalName) => set((state) => ({
        modals: { ...state.modals, [modalName]: true }
      })),
      
      /**
       * Close a modal
       * @param {string} modalName - Name of the modal to close
       */
      closeModal: (modalName) => set((state) => ({
        modals: { ...state.modals, [modalName]: false }
      })),
      
      /**
       * Toggle a modal
       * @param {string} modalName - Name of the modal to toggle
       */
      toggleModal: (modalName) => set((state) => ({
        modals: { 
          ...state.modals, 
          [modalName]: !state.modals[modalName] 
        }
      })),
      
      /**
       * Set view mode
       * @param {'grid'|'list'} mode - View mode
       */
      setViewMode: (mode) => set({ viewMode: mode }),
      
      /**
       * Set loading state
       * @param {string} key - Loading state key
       * @param {boolean} loading - Whether loading
       */
      setLoading: (key, loading) => set((state) => ({
        loadingStates: { ...state.loadingStates, [key]: loading }
      })),
      
      /**
       * Add a toast notification
       * @param {Object} toast - Toast object with message, type, etc.
       */
      addToast: (toast) => {
        const id = Date.now();
        const newToast = { id, ...toast };
        
        set((state) => ({
          toasts: [...state.toasts, newToast]
        }));
        
        // Auto-remove after duration
        if (toast.duration !== Infinity) {
          setTimeout(() => {
            get().removeToast(id);
          }, toast.duration || 5000);
        }
        
        return id;
      },
      
      /**
       * Remove a toast
       * @param {number} id - Toast ID
       */
      removeToast: (id) => set((state) => ({
        toasts: state.toasts.filter(t => t.id !== id)
      })),
      
      /**
       * Clear all toasts
       */
      clearToasts: () => set({ toasts: [] }),
      
      /**
       * Set profile menu visibility
       * @param {boolean} show - Whether to show profile menu
       */
      setShowProfileMenu: (show) => set({ showProfileMenu: show }),
      
      /**
       * Set mobile sidebar sheet visibility
       * @param {boolean} show - Whether to show mobile sidebar sheet
       */
      setShowMobileSidebarSheet: (show) => set({ showMobileSidebarSheet: show }),
      
      /**
       * Set mobile context menu visibility
       * @param {boolean} show - Whether to show mobile context menu
       */
      setShowMobileContextMenu: (show) => set({ showMobileContextMenu: show }),
      
      /**
       * Set context menu target
       * @param {Object} target - Context menu target data
       */
      setContextMenuTarget: (target) => set({ contextMenuTarget: target }),
      
      /**
       * Set link callback
       * @param {Function} callback - Callback function for link modal
       */
      setLinkCallback: (callback) => set({ linkCallback: callback }),
      
      /**
       * Set editing project
       * @param {Object} project - Project being edited
       */
      setEditingProject: (project) => set({ editingProject: project }),
      
      /**
       * Set storage info
       * @param {Object} info - Storage information
       */
      setStorageInfo: (info) => set({ storageInfo: info }),
      
      /**
       * Set mobile state
       * @param {boolean} isMobile - Whether device is mobile
       */
      setIsMobile: (isMobile) => set({ isMobile }),
      
      // Selectors
      selectors: {
        isModalOpen: (modalName) => get().modals[modalName] || false,
        isLoading: (key) => get().loadingStates[key] || false,
        isSidebarOpen: () => get().sidebarOpen,
        isMobileMenuOpen: () => get().mobileMenuOpen,
      }
    })
  )
);

// Export the hook
export default useUIStore;

// Export convenient selectors
export const useSidebarOpen = () => useUIStore((state) => state.sidebarOpen);
export const useSidebarWidth = () => useUIStore((state) => state.sidebarWidth);
export const useMobileMenuOpen = () => useUIStore((state) => state.mobileMenuOpen);
export const useViewMode = () => useUIStore((state) => state.viewMode);
export const useModal = (modalName) => useUIStore((state) => state.modals[modalName] || false);
export const useLoading = (key) => useUIStore((state) => state.loadingStates[key] || false);
export const useToasts = () => useUIStore((state) => state.toasts);