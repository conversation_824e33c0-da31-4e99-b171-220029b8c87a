import { create } from 'zustand';
import { devtools } from './migrationHelper.jsx';

/**
 * Block Editor Store - Manages state for individual block editing
 * Used by block components (TextBlock, CodeBlock, etc.)
 */
const useBlockEditorStore = create(
  devtools('BlockEditor')(
    (set, get) => ({
      // Active block editing states (keyed by blockId)
      editingBlocks: {},
      
      // Toolbar states (keyed by blockId)
      toolbarStates: {},
      
      // Selection states (keyed by blockId)
      selectionStates: {},
      
      // Collapse states (keyed by blockId)
      collapseStates: {},
      
      // Slash command states
      slashCommandStates: {},
      
      // Hover states
      hoveredBlockId: null,
      focusedBlockId: null,
      
      // Actions
      /**
       * Set editing state for a block
       * @param {string} blockId - Block ID
       * @param {boolean} isEditing - Whether block is being edited
       */
      setBlockEditing: (blockId, isEditing) => {
        set((state) => ({
          editingBlocks: {
            ...state.editingBlocks,
            [blockId]: isEditing
          }
        }));
      },
      
      /**
       * Get editing state for a block
       * @param {string} blockId - Block ID
       * @returns {boolean} Whether block is being edited
       */
      isBlockEditing: (blockId) => {
        return get().editingBlocks[blockId] || false;
      },
      
      /**
       * Set toolbar state for a block
       * @param {string} blockId - Block ID
       * @param {Object} toolbarState - Toolbar state
       */
      setToolbarState: (blockId, toolbarState) => {
        set((state) => ({
          toolbarStates: {
            ...state.toolbarStates,
            [blockId]: toolbarState
          }
        }));
      },
      
      /**
       * Update toolbar visibility
       * @param {string} blockId - Block ID
       * @param {boolean} visible - Whether toolbar is visible
       * @param {Object} position - Toolbar position
       */
      updateToolbar: (blockId, visible, position = null) => {
        set((state) => ({
          toolbarStates: {
            ...state.toolbarStates,
            [blockId]: {
              ...state.toolbarStates[blockId],
              visible,
              position
            }
          }
        }));
      },
      
      /**
       * Set selection state for a block
       * @param {string} blockId - Block ID
       * @param {Object} selection - Selection state
       */
      setSelectionState: (blockId, selection) => {
        set((state) => ({
          selectionStates: {
            ...state.selectionStates,
            [blockId]: selection
          }
        }));
      },
      
      /**
       * Set collapse state for a block
       * @param {string} blockId - Block ID
       * @param {boolean} isCollapsed - Whether block is collapsed
       */
      setBlockCollapsed: (blockId, isCollapsed) => {
        set((state) => ({
          collapseStates: {
            ...state.collapseStates,
            [blockId]: isCollapsed
          }
        }));
      },
      
      /**
       * Toggle collapse state for a block
       * @param {string} blockId - Block ID
       */
      toggleBlockCollapse: (blockId) => {
        const currentState = get().collapseStates[blockId] || false;
        set((state) => ({
          collapseStates: {
            ...state.collapseStates,
            [blockId]: !currentState
          }
        }));
      },
      
      /**
       * Set slash command state for a block
       * @param {string} blockId - Block ID
       * @param {Object} slashState - Slash command state
       */
      setSlashCommandState: (blockId, slashState) => {
        set((state) => ({
          slashCommandStates: {
            ...state.slashCommandStates,
            [blockId]: slashState
          }
        }));
      },
      
      /**
       * Update slash hint
       * @param {string} blockId - Block ID
       * @param {string} hint - Slash hint text
       * @param {Object} position - Hint position
       */
      updateSlashHint: (blockId, hint, position = null) => {
        set((state) => ({
          slashCommandStates: {
            ...state.slashCommandStates,
            [blockId]: {
              hint,
              position
            }
          }
        }));
      },
      
      /**
       * Set hovered block
       * @param {string} blockId - Block ID
       */
      setHoveredBlock: (blockId) => {
        set({ hoveredBlockId: blockId });
      },
      
      /**
       * Set focused block
       * @param {string} blockId - Block ID
       */
      setFocusedBlock: (blockId) => {
        set({ focusedBlockId: blockId });
      },
      
      /**
       * Clear all state for a block (when unmounting)
       * @param {string} blockId - Block ID
       */
      clearBlockState: (blockId) => {
        set((state) => {
          const newEditingBlocks = { ...state.editingBlocks };
          const newToolbarStates = { ...state.toolbarStates };
          const newSelectionStates = { ...state.selectionStates };
          const newCollapseStates = { ...state.collapseStates };
          const newSlashCommandStates = { ...state.slashCommandStates };
          
          delete newEditingBlocks[blockId];
          delete newToolbarStates[blockId];
          delete newSelectionStates[blockId];
          delete newCollapseStates[blockId];
          delete newSlashCommandStates[blockId];
          
          return {
            editingBlocks: newEditingBlocks,
            toolbarStates: newToolbarStates,
            selectionStates: newSelectionStates,
            collapseStates: newCollapseStates,
            slashCommandStates: newSlashCommandStates
          };
        });
      },
      
      /**
       * Clear all states (reset store)
       */
      clearAllStates: () => {
        set({
          editingBlocks: {},
          toolbarStates: {},
          selectionStates: {},
          collapseStates: {},
          slashCommandStates: {},
          hoveredBlockId: null,
          focusedBlockId: null
        });
      },
      
      // Selectors
      selectors: {
        getBlockState: (blockId) => {
          const state = get();
          return {
            isEditing: state.editingBlocks[blockId] || false,
            toolbar: state.toolbarStates[blockId] || { visible: false, position: null },
            selection: state.selectionStates[blockId] || null,
            isCollapsed: state.collapseStates[blockId] || false,
            slashCommand: state.slashCommandStates[blockId] || { hint: '', position: null }
          };
        },
        getToolbarState: (blockId) => {
          return get().toolbarStates[blockId] || { visible: false, position: null };
        },
        getSlashCommandState: (blockId) => {
          return get().slashCommandStates[blockId] || { hint: '', position: null };
        }
      }
    })
  )
);

// Export the hook
export default useBlockEditorStore;

// Export convenient selectors
export const useBlockEditing = (blockId) => 
  useBlockEditorStore((state) => state.editingBlocks[blockId] || false);

export const useBlockToolbar = (blockId) => 
  useBlockEditorStore((state) => state.toolbarStates[blockId] || { visible: false, position: null });

export const useBlockCollapsed = (blockId) => 
  useBlockEditorStore((state) => state.collapseStates[blockId] || false);

export const useBlockSlashCommand = (blockId) => 
  useBlockEditorStore((state) => state.slashCommandStates[blockId] || { hint: '', position: null });