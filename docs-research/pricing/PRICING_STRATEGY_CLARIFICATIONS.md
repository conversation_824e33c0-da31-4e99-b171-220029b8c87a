# Pricing Communication Strategy - Clarifications

Thank you for the thoughtful questions! Here are the specific directions:

## 1. Pricing Model Focus
**Primary focus**: SaaS subscription tiers (monthly/annual) - this is our current model
- We're keeping the subscription model with Personal ($9/mo) and Professional ($19/mo) tiers
- **Not interested in**: One-time purchases or lifetime deals
- **However**: Brief insights on freemium communication would be valuable for future consideration (but not the main focus)

## 2. Target Developer Segment
**Primary target**: Indie developers and small teams (1-5 people)
- Individual developers who need better documentation habits
- Freelancers managing multiple projects
- Small development teams wanting to share knowledge
- **Secondary**: Growing startups (5-20 developers)
- **Not targeting**: Large enterprises (yet)

This means focusing on tools like:
- Linear, Notion, Obsidian (indie/small team pricing)
- Rather than <PERSON>ra, Confluence (enterprise focus)

## 3. Timeline and Scope
**Immediate need**: Improve copy/communication of existing tiers
- We're keeping the 2-tier structure (Personal + Professional)
- Need to better communicate what each tier offers
- Make features understandable without prior knowledge
- **Timeline**: Implementation within 1-2 weeks

**What we need**:
1. **Tactical**: How to describe our existing features clearly
2. **Copy improvements**: Better ways to explain technical features
3. **Value clarity**: Help users understand why they need each feature
4. **Examples**: From tools that nail developer communication

**Not needed right now**:
- Complete pricing strategy overhaul
- New tier structures
- Enterprise pricing strategies

## Key Context
- We have working features but struggle to explain their value
- Users don't understand terms like "Version Track Block" or why "AI conversation preservation" matters
- We need to transform feature lists into clear benefits
- Focus on clarity over comprehensiveness

## Priority Research Areas
1. **Feature description best practices** - How to explain complex features simply
2. **Limit communication** - How to present restrictions positively
3. **Value progression** - Making the upgrade path obvious
4. **Copy examples** - Specific phrases that work for developer tools

The goal is practical, implementable improvements to our pricing page copy that we can apply immediately to increase conversions.