# Value Articulation Research Prompt for SaaS Pricing

## Research Objective
I need you to conduct deep research on how successful SaaS companies (especially developer tools) articulate their value propositions to convert technical features into compelling purchase decisions. Focus on methodologies that drive conversion and make pricing tiers crystal clear.

## Key Research Areas

### 1. Value Articulation Methodologies
Research and analyze:
- **Jobs-to-be-Done (JTBD) Framework** - How companies like Intercom use it
- **Value Proposition Canvas** - How it's applied in SaaS pricing pages
- **Feature-Benefit-Value Ladder** - Examples from successful developer tools
- **Outcome-Driven Innovation** - How to focus on outcomes vs features
- **The "10x Better" Rule** - How companies prove they're worth switching to

### 2. Successful Examples to Analyze
Study these companies' pricing pages and documentation:

**Developer Tools:**
- Linear (project management for developers)
- Vercel (deployment platform)
- Railway (infrastructure platform)
- Raycast (productivity tool)
- Notion (documentation/notes)
- Obsidian (knowledge management)
- Superhuman (email client)

**What to extract:**
- How they describe features (word choice, length)
- How they differentiate tiers
- What triggers upgrade decisions
- How they handle technical vs non-technical language

### 3. Psychological Triggers in Pricing

Research these specific techniques:
- **Loss Aversion** - How companies frame "what you're missing"
- **Social Proof** - How they show who uses which tier
- **Anchoring** - How they position pricing tiers
- **FOMO Creation** - Time-sensitive or availability-based triggers
- **Value Stacking** - How they make higher tiers feel like bargains

### 4. Where to Look in Code for True Value

When analyzing a codebase to extract value props, focus on:

**Core Innovation Areas:**
```
/components/[unique-feature-names]/
/services/[core-business-logic]/
/hooks/[custom-functionality]/
/utils/[proprietary-algorithms]/
```

**Skip These Areas:**
```
/auth/ (standard authentication)
/api/crud/ (basic operations)
/components/ui/ (generic UI elements)
/utils/helpers/ (common utilities)
```

**Value Indicators in Code:**
- Complex state management = solving workflow problems
- Custom algorithms = unique value proposition
- Integration points = ecosystem value
- Offline/sync logic = reliability value
- Permission systems = collaboration value
- Export functions = data ownership value

### 5. Conversion-Focused Copy Formulas

Research these proven formulas:
1. **Problem-Agitation-Solution (PAS)**
2. **Before-After-Bridge (BAB)**
3. **Feature-Advantage-Benefit (FAB)**
4. **AIDA (Attention-Interest-Desire-Action)**

Find examples of each in developer tool pricing.

### 6. Tier Differentiation Strategies

Research how companies create clear upgrade paths:
- **Usage Limits** vs **Feature Gates**
- **Team Size** vs **Advanced Features**
- **Support Levels** vs **Performance Tiers**
- **The Rule of 3x Value** - Each tier should offer 3x the value of the previous

### 7. Common Mistakes to Avoid

Research what DOESN'T work:
- Technical jargon without context
- Too many features per tier
- Unclear differentiation
- Missing the "why upgrade" trigger
- Focusing on features instead of outcomes

## Specific Questions to Answer

1. **The Clarity Test**: How do successful companies ensure a developer can understand their pricing in under 30 seconds?

2. **The Upgrade Trigger**: What specific phrases/features consistently trigger tier upgrades?

3. **The Value Stack**: How do companies make a $19/mo tool feel like a bargain?

4. **The Simplicity Rule**: How many features should each tier actually have? (Find data)

5. **The Language Bridge**: How do technical companies speak to both technical and business buyers?

## Deliverables Needed

1. **Top 5 Value Articulation Frameworks** with real examples
2. **20 Power Phrases** that convert (from real pricing pages)
3. **Tier Differentiation Playbook** with 3-5 proven models
4. **Feature-to-Benefit Translation Guide** with examples
5. **Psychological Pricing Checklist** for developer tools

## Additional Context

The product is a developer documentation tool with these unique aspects:
- Saves AI conversations (ChatGPT/Claude) in context
- Git-style version control for documentation
- Offline-first architecture
- Block-based content system
- Designed specifically for developers

Current challenge: Making these technical features feel essential and worth paying for, without overselling or using marketing fluff.

Please provide:
1. Actionable methodologies (not just theory)
2. Real examples from successful companies
3. Specific copy formulas that work
4. Clear do's and don'ts
5. A framework I can immediately apply

Focus on what actually drives conversions, not what sounds good in theory.