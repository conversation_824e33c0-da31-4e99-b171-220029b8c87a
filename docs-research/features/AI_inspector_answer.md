<html lang="en" class="hentry"><plasmo-csui></plasmo-csui><plasmo-csui id="plasmo-inspector"></plasmo-csui><head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/devlog-favicon.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    
    <!-- Mobile specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    
    <title>Developer Knowledge Base - Capture Your Coding Journey | DevLog</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Build your personal developer knowledge base with DevLog. Save code snippets, track versions, preserve AI conversations, and never forget why your code works. Try free for 14 days.">
    <meta name="keywords" content="developer knowledge base, code snippet manager, developer documentation tool, personal wiki for developers, offline documentation, AI conversation saver, code version tracking, developer note taking app, programming knowledge management, developer second brain, block-based documentation">
    <meta name="author" content="Devlog Team">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.devlog.design/">
    
    <!-- Google Site Verification -->
    <meta name="google-site-verification" content="google75c2ec069aed359d">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="DevLog - Developer Knowledge Base That Remembers Why Your Code Works">
    <meta property="og:description" content="The only developer documentation tool with AI conversation preservation, code version tracking, and offline-first architecture. Build your second brain for coding.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.devlog.design/">
    <meta property="og:image" content="https://www.devlog.design/og-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="DevLog">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@devlogapp">
    <meta name="twitter:title" content="DevLog - Developer Knowledge Base &amp; Code Documentation">
    <meta name="twitter:description" content="Capture, organize, and retrieve your coding solutions with AI conversation preservation and offline-first architecture. Start free.">
    <meta name="twitter:image" content="https://www.devlog.design/twitter-image.png">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#10b981">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">
    
    <!-- Enhanced Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "DevLog",
      "description": "Developer knowledge base and documentation tool with AI conversation preservation, code version tracking, and offline-first architecture",
      "url": "https://www.devlog.design",
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Web Browser",
      "browserRequirements": "Requires JavaScript enabled",
      "softwareVersion": "2.0",
      "offers": {
        "@type": "AggregateOffer",
        "priceCurrency": "USD",
        "highPrice": "19",
        "lowPrice": "0",
        "offerCount": "3",
        "offers": [
          {
            "@type": "Offer",
            "name": "Starter",
            "price": "0",
            "priceCurrency": "USD",
            "description": "10 documents, Basic features"
          },
          {
            "@type": "Offer",
            "name": "Pro",
            "price": "9",
            "priceCurrency": "USD",
            "description": "Unlimited documents, All features"
          },
          {
            "@type": "Offer",
            "name": "Team",
            "price": "19",
            "priceCurrency": "USD",
            "description": "Everything in Pro + Collaboration"
          }
        ]
      },
      "creator": {
        "@type": "Organization",
        "name": "DevLog Team",
        "url": "https://www.devlog.design"
      },
      "featureList": [
        "AI Conversation Preservation",
        "Code Version Tracking",
        "Offline-First Architecture",
        "Block-Based Editor",
        "Wiki-Style Linking",
        "Personal Knowledge Graphs",
        "Syntax Highlighting",
        "Markdown Support",
        "Tag Organization",
        "Lightning-Fast Search"
      ],
      "screenshot": "https://www.devlog.design/screenshot.png",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "127"
      }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a developer knowledge base?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A developer knowledge base is a personal documentation system that helps programmers capture, organize, and retrieve their coding solutions, learning notes, and technical knowledge. DevLog specializes in this by offering features like code version tracking and AI conversation preservation."
          }
        },
        {
          "@type": "Question",
          "name": "How is DevLog different from Notion or Obsidian?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "DevLog is built specifically for developers with features like syntax highlighting, code version tracking, AI conversation blocks, and offline-first architecture. Unlike general-purpose tools, DevLog understands developer workflows and provides specialized features for code documentation."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use DevLog offline?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes! DevLog features offline-first architecture with IndexedDB storage, allowing you to access and edit your documentation without internet connection. Changes sync automatically when you're back online."
          }
        },
        {
          "@type": "Question",
          "name": "What is AI conversation preservation?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "AI conversation preservation allows you to save and organize your ChatGPT, Claude, or other AI tool conversations directly in your documentation. This unique feature helps you retain valuable AI-generated solutions and explanations alongside your code."
          }
        }
      ]
    }
    </script>
    
    <!-- Organization Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "DevLog",
      "url": "https://www.devlog.design",
      "logo": "https://www.devlog.design/devlog-favicon.svg",
      "sameAs": [
        "https://github.com/devlog-app",
        "https://twitter.com/devlogapp"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer support"
      }
    }
    </script>
    <script type="module" crossorigin="" src="/assets/index-BVTwrFBN.js"></script>
    <link rel="stylesheet" crossorigin="" href="/assets/style-ClkX8rz4.css">
  <style></style><style></style><meta name="readermode-active" content="true"></head>
  <div class="spoken-word spoken-word--active"><div class="spoken-word-playback-controls"><legend class="spoken-word-playback-controls__legend rm-d-none-i">Text to Speech</legend><button type="button" class="spoken-word-playback-controls__button wp-exclude-emoji spoken-word-playback-controls__button--emoji rm-d-none-i" aria-label="Play">▶</button><button type="button" class="spoken-word-playback-controls__button wp-exclude-emoji spoken-word-playback-controls__button--emoji rm-d-none-i" aria-label="Previous">⏪</button><button type="button" class="spoken-word-playback-controls__button wp-exclude-emoji spoken-word-playback-controls__button--emoji rm-d-none-i" aria-label="Forward">⏩</button><button type="button" class="spoken-word-playback-controls__button wp-exclude-emoji spoken-word-playback-controls__button--emoji rm-d-none-i" aria-label="Settings">⚙</button><dialog class="spoken-word-playback-controls__dialog"><div class="spoken-word-playback-controls__heading"><label>Voice options</label></div><p><label class="spoken-word-playback-controls__label" for="input1-rate">Rate:</label> <input id="input1-rate" class="spoken-word-playback-controls__input form-control rm-input" type="number" data-prop="rate" step="0.1" min="0.1" max="10"></p><p><label class="spoken-word-playback-controls__label" for="input1-pitch">Pitch:</label> <input id="input1-pitch" class="spoken-word-playback-controls__input form-control rm-input" type="number" data-prop="pitch" min="0" max="2" step="0.1"></p><p><label class="spoken-word-playback-controls__label" for="input1-voice-en">Voice:</label> <select id="input1-voice-en" class="spoken-word-playback-controls__input rm-select-placeholder" data-language="en"><option value="Microsoft David - English (United States)">🇺🇸 Microsoft David - English (United States)</option><option value="Microsoft Hortense - French (France)">🇫🇷 Microsoft Hortense - French (France)</option><option value="Microsoft Julie - French (France)">🇫🇷 Microsoft Julie - French (France)</option><option value="Microsoft Mark - English (United States)">🇺🇸 Microsoft Mark - English (United States)</option><option value="Microsoft Paul - French (France)">🇫🇷 Microsoft Paul - French (France)</option><option value="Microsoft Zira - English (United States)">🇺🇸 Microsoft Zira - English (United States)</option></select></p><button class="btn btn-sm btn-primary rm-btn-base rm-btn-primary p-l-20 p-r-20">Close</button></dialog></div></div><body data-rm-theme="light" class="entry-content" lang="en">
    <div id="root"><div class="h-full bg-dark-primary flex flex-col"><div class="h-0.5 bg-accent-green/80 flex-shrink-0"></div><div class="flex-1 min-h-0 overflow-hidden pb-16 md:pb-0"><div class="h-screen overflow-hidden grid grid-cols-1 lg:grid-cols-[auto,1fr]" style="--sidebar-width: 80px; transition: grid-template-columns 200ms cubic-bezier(0.4, 0, 0.2, 1); will-change: grid-template-columns; contain: layout style;"><div class="
            fixed lg:relative inset-y-0 left-0 z-40 w-[280px] lg:w-auto
            bg-dark-primary lg:bg-transparent
            flex flex-col
            transition-all duration-200 ease-out
            h-full overflow-hidden
            -translate-x-full lg:translate-x-0
            lg:col-start-1
          "><div class="flex-1 min-h-0 pt-20 pb-7 flex flex-col"><div class="bg-gradient-to-br from-dark-primary to-dark-lighter h-full flex flex-col flex-1 min-h-0 ml-2 mr-2 rounded-2xl shadow-2xl overflow-hidden"><div class="px-4 py-3"><button class="w-full p-2 hover:bg-surface-1/50 rounded-xl transition-all duration-200 hover:scale-105 group" title="Expand sidebar (Ctrl+B)"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-text-secondary mx-auto" aria-hidden="true"><path d="m9 18 6-6-6-6"></path></svg></button></div></div></div></div><main class="flex flex-col min-w-0 overflow-hidden lg:col-start-2"><div class="fixed top-6 right-6 z-50"><button class="
          relative 
          w-10 h-10 rounded-xl
          bg-dark-secondary/40 backdrop-blur-xl
          border border-dark-secondary/30
          flex items-center justify-center
          text-text-secondary/70
          hover:text-text-primary
          hover:bg-dark-secondary/60
          hover:border-accent-green/30
          hover:shadow-lg hover:shadow-accent-green/5
          transition-all duration-300 ease-out
          group
          opacity-100 scale-100
          
        " title="Quick actions"><div class="absolute inset-0 rounded-xl bg-gradient-to-br from-accent-green/0 to-accent-green/10 
                        opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left relative z-10 transition-all duration-300 
                     group-hover:text-accent-green group-hover:-translate-x-0.5" aria-hidden="true"><path d="m15 18-6-6 6-6"></path></svg></button><div class="
          absolute top-0 right-0
          min-w-[200px]
          bg-dark-primary/80 backdrop-blur-2xl
          border border-dark-secondary/20
          rounded-2xl shadow-2xl shadow-black/50
          transition-all duration-300 cubic-bezier(0.25, 0.46, 0.45, 0.94)
          opacity-0 translate-x-4 scale-95 pointer-events-none
        "><div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/[0.02] to-transparent pointer-events-none"></div><div class="relative p-4"><button class="absolute top-3 right-3 p-1.5 
                     text-text-secondary/60 hover:text-text-primary
                     hover:bg-dark-secondary/30 rounded-lg 
                     transition-all duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" aria-hidden="true"><path d="m15 18-6-6 6-6"></path></svg></button><div class="flex flex-col gap-3 pr-6"><div class="flex items-center gap-0.5 bg-dark-secondary/30 rounded-lg p-0.5"><button class="flex items-center gap-1.5 px-3 py-1.5 rounded-md transition-all duration-200 bg-dark-primary text-accent-green shadow-sm"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-grid" aria-hidden="true"><rect width="7" height="7" x="3" y="3" rx="1"></rect><rect width="7" height="7" x="14" y="3" rx="1"></rect><rect width="7" height="7" x="14" y="14" rx="1"></rect><rect width="7" height="7" x="3" y="14" rx="1"></rect></svg><span class="text-xs font-medium">Blocks</span></button><button class="flex items-center gap-1.5 px-3 py-1.5 rounded-md transition-all duration-200 text-text-secondary/70 hover:text-text-primary hover:bg-dark-secondary/20"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-list" aria-hidden="true"><rect width="7" height="7" x="3" y="3" rx="1"></rect><rect width="7" height="7" x="3" y="14" rx="1"></rect><path d="M14 4h7"></path><path d="M14 9h7"></path><path d="M14 15h7"></path><path d="M14 20h7"></path></svg><span class="text-xs font-medium">Lines</span></button></div><div class="h-px bg-gradient-to-r from-transparent via-dark-secondary/30 to-transparent"></div><button class="flex items-center gap-2 px-3 py-2
                       text-text-secondary/70 hover:text-text-primary
                       hover:bg-dark-secondary/20 rounded-lg 
                       transition-all duration-200 group"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 lucide-share-2 group-hover:text-blue-400/70 transition-colors" aria-hidden="true"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg><span class="text-xs font-medium">Share</span></button><button class="flex items-center gap-2 px-3 py-2
                       text-text-secondary/70 hover:text-text-primary
                       hover:bg-dark-secondary/20 rounded-lg 
                       transition-all duration-200 group"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 lucide-trash-2 group-hover:text-red-400/70 transition-colors" aria-hidden="true"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" x2="10" y1="11" y2="17"></line><line x1="14" x2="14" y1="11" y2="17"></line></svg><span class="text-xs font-medium">Delete</span></button></div></div></div></div><div class="h-full overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-stable"><div class="max-w-4xl mx-auto fade-in px-8 py-8"><div class="flex items-start gap-4 mb-6"><button class="mt-1 p-2 text-text-secondary hover:text-text-primary 
                     hover:bg-dark-secondary/50 rounded-lg transition-all
                     group flex items-center gap-2" title="Back to dashboard"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left group-hover:-translate-x-0.5 transition-transform" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></button><div class="flex-1"><div class="flex items-center justify-between"><div class="text-text-secondary text-sm mb-2">Document</div><div class="flex items-center gap-3"><button class="p-1.5 text-text-secondary hover:text-blue-400 
                           hover:bg-blue-400/10 rounded transition-all" title="Share document"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-share2 lucide-share-2" aria-hidden="true"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line><line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line></svg></button><button class="p-1.5 text-text-secondary hover:text-red-400 
                           hover:bg-red-400/10 rounded transition-all" title="Delete document"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash2 lucide-trash-2" aria-hidden="true"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path><line x1="10" x2="10" y1="11" y2="17"></line><line x1="14" x2="14" y1="11" y2="17"></line></svg></button><div class="relative"><div class="absolute inset-0 bg-gradient-to-r from-accent-green/20 to-accent-green/10 
                                rounded-lg blur-xl opacity-50"></div><div class="relative flex items-center gap-1 bg-dark-secondary/50 backdrop-blur-sm
                                rounded-lg p-1 border border-dark-secondary/50"><button class="relative p-1.5 rounded transition-all duration-200 bg-dark-primary text-accent-green shadow-lg" title="Blocks view"><div class="absolute inset-0 bg-accent-green/20 rounded blur-sm"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-grid relative z-10" aria-hidden="true"><rect width="7" height="7" x="3" y="3" rx="1"></rect><rect width="7" height="7" x="14" y="3" rx="1"></rect><rect width="7" height="7" x="14" y="14" rx="1"></rect><rect width="7" height="7" x="3" y="14" rx="1"></rect></svg></button><button class="relative p-1.5 rounded transition-all duration-200 text-text-secondary hover:text-text-primary" title="Lines view"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-layout-list relative z-10" aria-hidden="true"><rect width="7" height="7" x="3" y="3" rx="1"></rect><rect width="7" height="7" x="3" y="14" rx="1"></rect><path d="M14 4h7"></path><path d="M14 9h7"></path><path d="M14 15h7"></path><path d="M14 20h7"></path></svg></button></div></div></div></div><h1 class="text-text-primary text-2xl font-medium cursor-text
                         hover:bg-dark-secondary/30 rounded px-2 py-1 -ml-2
                         transition-colors">test 1010</h1></div></div><div class="space-y-4 mb-8 min-h-[400px] relative pl-8"><div class="relative pl-8"><div class="group block-wrapper relative transition-all duration-200   " data-block-id="ba406e15-8737-4502-aa18-a3e8c758feae" draggable="false" style="position: relative; z-index: auto;"><div class="relative opacity-40 transition-opacity duration-200"><h2 class="text-text-primary cursor-text hover:bg-dark-secondary/30 
                  rounded px-2 py-1 transition-colors text-2xl font-semibold" style="font-size: var(--step-3); line-height: var(--line-height-tight);">fdf</h2></div></div><div class="relative h-10 -my-2 group cursor-pointer"><div class="absolute inset-0 flex items-center justify-center"><div class="absolute inset-x-8 h-px transition-opacity duration-150 bg-gradient-to-r from-transparent via-dark-secondary/20 to-transparent"></div><div class="bg-dark-primary border border-dark-secondary/40 
                     text-text-secondary rounded-full w-7 h-7 
                     flex items-center justify-center
                     transition-all duration-150 ease-out
                     shadow-sm
                     opacity-0 scale-90" aria-label="Add block"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></div></div></div></div><div class="relative pl-8"><div class="group block-wrapper relative transition-all duration-200   " data-block-id="5dc91b81-7b4b-4a2a-87a0-83b0406ebdb9" draggable="false" style="position: relative; z-index: auto;"><div class="relative opacity-40 transition-opacity duration-200"><div class="group relative overflow-visible" data-block-id="5dc91b81-7b4b-4a2a-87a0-83b0406ebdb9"><div class="absolute top-2 right-2 flex items-center gap-2 z-20"><div class="relative"><button class="flex items-center gap-1 text-text-secondary text-xs bg-dark-primary/80 
                       px-2 py-1 rounded hover:bg-dark-secondary/80 transition-colors">javascript<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down transition-transform" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></div><button class="p-1 hover:bg-dark-secondary rounded opacity-0 group-hover:opacity-100 transition-opacity bg-dark-primary/80" title="Copy code"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy text-text-secondary" aria-hidden="true"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button></div><div class="bg-dark-primary rounded-lg overflow-hidden"><div class="cursor-text hover:ring-1 hover:ring-dark-secondary transition-all "><div class="flex"><div class="select-none text-text-secondary text-sm font-mono p-4 pr-0 text-right border-r border-dark-secondary/50"><div class="leading-6">1</div></div><pre class="prism-code language-javascript flex-1 p-4 pl-4 overflow-x-auto" style="color: rgb(214, 222, 235); background-color: rgb(1, 22, 39);"><code class="font-mono text-sm"><div class="leading-6" style="color: rgb(214, 222, 235);"><span class="token plain">dfdfdf</span></div></code></pre></div></div></div></div></div></div><div class="relative h-10 -my-2 group cursor-pointer"><div class="absolute inset-0 flex items-center justify-center"><div class="absolute inset-x-8 h-px transition-opacity duration-150 bg-gradient-to-r from-transparent via-dark-secondary/20 to-transparent"></div><div class="bg-dark-primary border border-dark-secondary/40 
                     text-text-secondary rounded-full w-7 h-7 
                     flex items-center justify-center
                     transition-all duration-150 ease-out
                     shadow-sm
                     opacity-0 scale-90" aria-label="Add block"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></div></div></div></div><div class="relative pl-8"><div class="group block-wrapper relative transition-all duration-200   " data-block-id="a6fc0c91-bdbe-4992-8a7f-9556933811e4" draggable="false" style="position: relative; z-index: auto;"><div class="relative opacity-100 transition-opacity duration-200"><div class="group relative"><div class="absolute -left-8 top-0 text-text-secondary/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-table" aria-hidden="true"><path d="M12 3v18"></path><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path></svg></div><div class="absolute -top-8 right-0 flex items-center gap-1.5 opacity-60 hover:opacity-100"><button class="px-2.5 py-1 text-xs rounded-md font-medium transition-all bg-accent-green/20 text-accent-green border border-accent-green/30">Header</button><button class="p-1.5 rounded-md bg-dark-secondary/50 text-text-secondary hover:bg-dark-secondary/80 hover:text-text-primary transition-all" title="Copy as Markdown"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy" aria-hidden="true"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button><button class="p-1.5 rounded-md bg-dark-secondary/50 text-text-secondary hover:bg-dark-secondary/80 hover:text-text-primary transition-all" title="Export as CSV"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download" aria-hidden="true"><path d="M12 15V3"></path><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><path d="m7 10 5 5 5-5"></path></svg></button></div><div class="bg-dark-primary/50 backdrop-blur-sm rounded-lg overflow-hidden border border-dark-secondary/30 shadow-lg shadow-dark-primary/20"><style>
    .table-scroll::-webkit-scrollbar {
      height: 6px;
    }
    .table-scroll::-webkit-scrollbar-track {
      background: rgba(30, 58, 95, 0.2);
      border-radius: 3px;
    }
    .table-scroll::-webkit-scrollbar-thumb {
      background: rgba(30, 58, 95, 0.5);
      border-radius: 3px;
    }
    .table-scroll::-webkit-scrollbar-thumb:hover {
      background: rgba(30, 58, 95, 0.7);
    }
  </style><div class="overflow-x-auto table-scroll"><table class="w-full border-collapse table-auto"><thead class="opacity-0 hover:opacity-100 transition-opacity duration-200"><tr class="border-b border-dark-secondary/20"><th class="w-10"></th><th class="relative p-0"><div class="absolute top-0 right-0 flex items-center gap-0.5 p-1 z-10 bg-dark-primary/90 backdrop-blur-sm rounded"><button class="p-1 rounded transition-all bg-accent-green/20 text-accent-green" title="Align left"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-left" aria-hidden="true"><path d="M15 12H3"></path><path d="M17 18H3"></path><path d="M21 6H3"></path></svg></button><button class="p-1 rounded transition-all hover:bg-dark-secondary/50 text-text-secondary/60 hover:text-text-secondary" title="Align center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-center" aria-hidden="true"><path d="M17 12H7"></path><path d="M19 18H5"></path><path d="M21 6H3"></path></svg></button><button class="p-1 rounded transition-all hover:bg-dark-secondary/50 text-text-secondary/60 hover:text-text-secondary" title="Align right"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-right" aria-hidden="true"><path d="M21 12H9"></path><path d="M21 18H7"></path><path d="M21 6H3"></path></svg></button><button class="p-1 hover:bg-red-500/20 rounded text-text-secondary/60 hover:text-red-400 ml-0.5 transition-all" title="Delete column"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></th><th class="relative p-0"><div class="absolute top-0 right-0 flex items-center gap-0.5 p-1 z-10 bg-dark-primary/90 backdrop-blur-sm rounded"><button class="p-1 rounded transition-all bg-accent-green/20 text-accent-green" title="Align left"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-left" aria-hidden="true"><path d="M15 12H3"></path><path d="M17 18H3"></path><path d="M21 6H3"></path></svg></button><button class="p-1 rounded transition-all hover:bg-dark-secondary/50 text-text-secondary/60 hover:text-text-secondary" title="Align center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-center" aria-hidden="true"><path d="M17 12H7"></path><path d="M19 18H5"></path><path d="M21 6H3"></path></svg></button><button class="p-1 rounded transition-all hover:bg-dark-secondary/50 text-text-secondary/60 hover:text-text-secondary" title="Align right"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-right" aria-hidden="true"><path d="M21 12H9"></path><path d="M21 18H7"></path><path d="M21 6H3"></path></svg></button><button class="p-1 hover:bg-red-500/20 rounded text-text-secondary/60 hover:text-red-400 ml-0.5 transition-all" title="Delete column"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></th><th class="relative p-0"><div class="absolute top-0 right-0 flex items-center gap-0.5 p-1 z-10 bg-dark-primary/90 backdrop-blur-sm rounded"><button class="p-1 rounded transition-all bg-accent-green/20 text-accent-green" title="Align left"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-left" aria-hidden="true"><path d="M15 12H3"></path><path d="M17 18H3"></path><path d="M21 6H3"></path></svg></button><button class="p-1 rounded transition-all hover:bg-dark-secondary/50 text-text-secondary/60 hover:text-text-secondary" title="Align center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-center" aria-hidden="true"><path d="M17 12H7"></path><path d="M19 18H5"></path><path d="M21 6H3"></path></svg></button><button class="p-1 rounded transition-all hover:bg-dark-secondary/50 text-text-secondary/60 hover:text-text-secondary" title="Align right"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-align-right" aria-hidden="true"><path d="M21 12H9"></path><path d="M21 18H7"></path><path d="M21 6H3"></path></svg></button><button class="p-1 hover:bg-red-500/20 rounded text-text-secondary/60 hover:text-red-400 ml-0.5 transition-all" title="Delete column"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></th><th class="w-10"><button class="p-1 hover:bg-dark-secondary/50 rounded text-text-secondary/60 hover:text-accent-green transition-all" title="Add column"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></th></tr></thead><thead><tr class="bg-gradient-to-r from-dark-secondary/20 to-dark-secondary/10 border-b border-dark-secondary/30"><td class="w-10"></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/20
                                     font-medium text-text-primary/90 transition-colors">Column 1</div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/20
                                     font-medium text-text-primary/90 transition-colors">Column 2</div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/20
                                     font-medium text-text-primary/90 transition-colors">Column 3</div></td><td class="w-10"></td></tr></thead><tbody><tr class="border-b border-dark-secondary/20 hover:bg-dark-secondary/10 transition-colors"><td class="w-10 relative group/row"><div class="opacity-0 group-hover/row:opacity-100 transition-opacity"><button class="p-1 hover:bg-red-500/20 rounded text-text-secondary/40 hover:text-red-400 transition-all" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="w-10"></td></tr><tr class="border-b border-dark-secondary/20 hover:bg-dark-secondary/10 transition-colors"><td class="w-10 relative group/row"><div class="opacity-0 group-hover/row:opacity-100 transition-opacity"><button class="p-1 hover:bg-red-500/20 rounded text-text-secondary/40 hover:text-red-400 transition-all" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="w-10"></td></tr><tr class="border-b border-dark-secondary/20 hover:bg-dark-secondary/10 transition-colors"><td class="w-10 relative group/row"><div class="opacity-0 group-hover/row:opacity-100 transition-opacity"><button class="p-1 hover:bg-red-500/20 rounded text-text-secondary/40 hover:text-red-400 transition-all" title="Delete row"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x" aria-hidden="true"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg></button></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="relative text-left"><div class="px-3 py-2 cursor-text bg-transparent hover:bg-dark-secondary/10
                                       text-text-primary/80 min-h-[40px] transition-colors
                                       flex items-center"><span class="text-text-secondary/40 text-sm italic">Empty</span></div></td><td class="w-10"></td></tr><tr class="hover:bg-dark-secondary/5 transition-colors"><td colspan="5" class="text-center py-2"><button class="px-4 py-1.5 bg-dark-secondary/30 hover:bg-dark-secondary/50 
                               rounded-md text-text-secondary/70 hover:text-text-primary
                               text-sm flex items-center gap-1.5 mx-auto transition-all
                               border border-dark-secondary/30 hover:border-dark-secondary/50"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg><span class="font-medium">Add Row</span></button></td></tr></tbody></table></div></div><div class="text-center text-text-secondary/50 text-xs mt-3 font-light"><span class="inline-flex items-center gap-3"><span>Click to edit</span><span class="text-text-secondary/30">•</span><span>Tab to navigate</span><span class="text-text-secondary/30">•</span><span>Enter for new row</span></span></div></div></div></div><div class="relative h-10 -my-2 group cursor-pointer"><div class="absolute inset-0 flex items-center justify-center"><div class="absolute inset-x-8 h-px transition-opacity duration-150 bg-gradient-to-r from-transparent via-dark-secondary/20 to-transparent"></div><div class="bg-dark-primary border border-dark-secondary/40 
                     text-text-secondary rounded-full w-7 h-7 
                     flex items-center justify-center
                     transition-all duration-150 ease-out
                     shadow-sm
                     opacity-0 scale-90" aria-label="Add block"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></div></div></div></div><div class="relative pt-4"><button class="w-full py-8 border-2 border-dashed border-dark-secondary/50
                         rounded-lg text-text-secondary hover:text-text-primary
                         hover:border-dark-secondary/50 transition-all
                         flex items-center justify-center gap-2 group"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus group-hover:scale-110 transition-transform" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg><span>Add a block</span></button></div></div><div class="flex items-center gap-3 flex-wrap mb-8"><button class="px-4 py-2 border border-dashed border-dark-secondary/50
                       rounded-full text-text-secondary text-sm
                       hover:border-text-secondary hover:text-text-primary
                       transition-all opacity-60 hover:opacity-100">Add tag...</button></div></div></div></main></div></div><nav class="md:hidden fixed bottom-0 left-0 right-0 bg-dark-primary border-t border-dark-secondary/50 z-50"><div class="flex items-center justify-around h-16"><a aria-current="page" class="flex flex-col items-center justify-center flex-1 h-full transition-colors text-accent-green active" href="/dashboard"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house stroke-2" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg><span class="text-xs mt-1">Home</span></a><a aria-current="page" class="flex flex-col items-center justify-center flex-1 h-full transition-colors text-text-secondary hover:text-text-primary active" href="/dashboard?search=true"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search stroke-1.5" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg><span class="text-xs mt-1">Search</span></a><a aria-current="page" class="flex flex-col items-center justify-center flex-1 h-full transition-colors text-text-secondary hover:text-text-primary active" href="/dashboard?new=true"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus stroke-1.5" aria-hidden="true"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg><span class="text-xs mt-1">New</span></a><a aria-current="page" class="flex flex-col items-center justify-center flex-1 h-full transition-colors text-accent-green active" href="/dashboard"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-grid3x3 lucide-grid-3x3 stroke-2" aria-hidden="true"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M3 15h18"></path><path d="M9 3v18"></path><path d="M15 3v18"></path></svg><span class="text-xs mt-1">Projects</span></a><a class="flex flex-col items-center justify-center flex-1 h-full transition-colors text-text-secondary hover:text-text-primary" href="/settings"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user stroke-1.5" aria-hidden="true"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg><span class="text-xs mt-1">Profile</span></a></div></nav></div><div class="fixed bottom-4 right-4 z-50 space-y-2"></div></div>
    <div id="portal-root"><button class="rounded-xl bg-dark-secondary/60 backdrop-blur-xl border border-dark-secondary/30 flex items-center justify-center hover:bg-dark-secondary/80 hover:border-accent-green/30 hover:shadow-lg hover:shadow-accent-green/5 group" title="Back to top" aria-label="Scroll to top" style="position: fixed; inset: auto 32px 96px auto; width: 40px; height: 40px; z-index: 9999; opacity: 1; pointer-events: auto; transform: translateY(0px); transition: 300ms cubic-bezier(0.4, 0, 0.2, 1);"><div class="absolute inset-0 rounded-xl bg-gradient-to-br from-accent-green/0 to-accent-green/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up relative z-10 text-text-secondary/70 group-hover:text-accent-green transition-all duration-300 group-hover:-translate-y-0.5" aria-hidden="true"><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div>
    <script src="/manifest-loader.js"></script>

  

<svg id="zohocomponents__svg" style="display: none" class="zh-dnone"><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--add"> <polygon points="13,7.3 8.7,7.3 8.7,3 7.3,3 7.3,7.3 3,7.3 3,8.7 7.3,8.7 7.3,13 8.7,13 8.7,8.7 13,8.7 "></polygon> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--arrow"> <polygon points="6.5,12.5 5.5,11.5 9,8 5.5,4.5 6.5,3.5 11,8"></polygon> </symbol> <symbol id="zc__svg--arrowbottom" viewBox="0 0 10.70711 6.06066"> <polyline points="10.354 0.354 5.354 5.354 0.354 0.354"></polyline> </symbol> 	 <symbol id="zc__svg--arrowleft" viewBox="0 0 6.06066 10.70711"> <polyline points="5.707 10.354 0.707 5.354 5.707 0.354"></polyline> </symbol> 	 <symbol id="zc__svg--arrowright" viewBox="0 0 6.06066 10.70711"> <polyline points="0.354 0.354 5.354 5.354 0.354 10.354"></polyline> </symbol> <symbol id="zc__svg--arrowup" viewBox="0 0 10.70711 6.06066"> <polyline points="0.354 5.707 5.354 0.707 10.354 5.707"></polyline> </symbol> 	 	<symbol id="zc__svg--backward" viewBox="0 0 12 12" width="100%" height="100%"><polygon points="6.6,10.3 2.9,6.5 6.6,2.7 7.4,3.4 4.3,6.5 7.4,9.6"></polygon></symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--buttonarrow"> <polygon points="3.5,6.5 4.5,5.5 8,9 11.5,5.5 12.5,6.5 8,11"></polygon> </symbol><symbol viewBox="0 0 14 14" width="100%" height="100%" id="zc__svg--calendaricon"> <rect x="-22.5" y="3.5" width="13" height="10"></rect> <path d="M-10,4v9h-12V4H-10 M-9,3h-14v11h14V3L-9,3z"></path> <rect x="-20.6" y="1.4" width="1.2" height="3.2"></rect> <path d="M-19.8,1.8v2.5h-0.5V1.8H-19.8 M-19,1h-2v4h2V1L-19,1z"></path> <rect x="-20.6" y="7.4" width="1.2" height="1.2"></rect> <path d="M-19.8,7.8v0.5h-0.5V7.8H-19.8 M-19,7h-2v2h2V7L-19,7z"></path> <rect x="-16.6" y="1.4" width="1.2" height="3.2"></rect> <path d="M-15.8,1.8v2.5h-0.5V1.8H-15.8 M-15,1h-2v4h2V1L-15,1z"></path> <rect x="-12.6" y="1.4" width="1.2" height="3.2"></rect> <path d="M-11.8,1.8v2.5h-0.5V1.8H-11.8 M-11,1h-2v4h2V1L-11,1z"></path> <rect x="-16.6" y="7.4" width="1.2" height="1.2"></rect> <path d="M-15.8,7.8v0.5h-0.5V7.8H-15.8 M-15,7h-2v2h2V7L-15,7z"></path> <rect x="-12.6" y="7.4" width="1.2" height="1.2"></rect> <path d="M-11.8,7.8v0.5h-0.5V7.8H-11.8 M-11,7h-2v2h2V7L-11,7z"></path> <rect x="-20.6" y="10.4" width="1.2" height="1.2"></rect> <path d="M-19.8,10.8v0.5h-0.5v-0.5H-19.8 M-19,10h-2v2h2V10L-19,10z"></path> <rect x="-16.6" y="10.4" width="1.2" height="1.2"></rect> <path d="M-15.8,10.8v0.5h-0.5v-0.5H-15.8 M-15,10h-2v2h2V10L-15,10z"></path> <rect x="-12.6" y="10.4" width="1.2" height="1.2"></rect> <path d="M-11.8,10.8v0.5h-0.5v-0.5H-11.8 M-11,10h-2v2h2V10L-11,10z"></path> <path d="M4,3c0.6,0,1-0.4,1-1V1H3v1C3,2.6,3.4,3,4,3z"></path> <rect x="2" y="7" width="2" height="2"></rect> <rect x="2" y="10" width="2" height="2"></rect> <rect x="6" y="7" width="2" height="2"></rect> <rect x="6" y="10" width="2" height="2"></rect> <rect x="10" y="7" width="2" height="2"></rect> <rect x="10" y="10" width="2" height="2"></rect> <path d="M12.8,2c0,1-0.8,1.8-1.8,1.8S9.2,3,9.2,2H5.8C5.8,3,5,3.8,4,3.8S2.2,3,2.2,2H0v3v9h14V5V2H12.8z M13,13H1V6h12V13z"></path> <path d="M11,3c0.6,0,1-0.4,1-1V1h-2v1C10,2.6,10.4,3,11,3z"></path> </symbol><symbol viewBox="0 0 21.9 21.9" width="100%" height="100%" id="zc__svg--clear"> <path d="M14.1,11.3c-0.2-0.2-0.2-0.5,0-0.7l7.5-7.5c0.2-0.2,0.3-0.5,0.3-0.7s-0.1-0.5-0.3-0.7l-1.4-1.4C20,0.1,19.7,0,19.5,0 c-0.3,0-0.5,0.1-0.7,0.3l-7.5,7.5c-0.2,0.2-0.5,0.2-0.7,0L3.1,0.3C2.9,0.1,2.6,0,2.4,0S1.9,0.1,1.7,0.3L0.3,1.7C0.1,1.9,0,2.2,0,2.4 s0.1,0.5,0.3,0.7l7.5,7.5c0.2,0.2,0.2,0.5,0,0.7l-7.5,7.5C0.1,19,0,19.3,0,19.5s0.1,0.5,0.3,0.7l1.4,1.4c0.2,0.2,0.5,0.3,0.7,0.3 s0.5-0.1,0.7-0.3l7.5-7.5c0.2-0.2,0.5-0.2,0.7,0l7.5,7.5c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3l1.4-1.4c0.2-0.2,0.3-0.5,0.3-0.7 s-0.1-0.5-0.3-0.7L14.1,11.3z"></path> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--close"> <rect x="1.5" y="7.4" transform="matrix(0.7071 -0.7071 0.7071 0.7071 -3.3137 8)" class="st0" width="13" height="1.1"></rect> <rect x="1.5" y="7.4" transform="matrix(0.7071 0.7071 -0.7071 0.7071 8 -3.3137)" class="st0" width="13" height="1.1"></rect> </symbol><symbol viewBox="0 0 600 598" width="100%" height="100%" id="zc__svg--zcolorpicker-advancedpicker"> <path d="M299.7-0.2C128.3-0.2-7,134,0.2,305.2C7.4,476.9,138,577.3,259.7,595.9c88.2,13.4,107.8-45.1,87.3-67.3 c-35.5-38.3-22.6-68.6-7.3-82.5c17.4-15.7,50.3-17.3,77.3-13.8c65.5,8.5,175-40,182.2-144.8C611,116.5,471.1-0.2,299.7-0.2z M118.1,299.7c-27.7,0-50.1-22.4-50.1-50.1c0-27.7,22.4-50.1,50.1-50.1c27.7,0,50.1,22.4,50.1,50.1 C168.2,277.3,145.8,299.7,118.1,299.7z M217.6,166.6c-27.7,0-50.1-22.4-50.1-50.1c0-27.7,22.4-50.1,50.1-50.1 c27.7,0,50.1,22.4,50.1,50.1C267.7,144.1,245.2,166.6,217.6,166.6z M384.9,166.9c-27.7,0-50.1-22.4-50.1-50.1 c0-27.7,22.4-50.1,50.1-50.1c27.7,0,50.1,22.4,50.1,50.1C435,144.4,412.6,166.9,384.9,166.9z M485.2,299.9 c-27.7,0-50.1-22.4-50.1-50.1c0-27.7,22.4-50.1,50.1-50.1c27.7,0,50.1,22.4,50.1,50.1C535.2,277.5,512.8,299.9,485.2,299.9z"> </path> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--zcolorpicker-nocolor"> <path d="M8,0C3.6,0,0,3.6,0,8s3.6,8,8,8s8-3.6,8-8S12.4,0,8,0z M8,1c1.8,0,3.3,0.7,4.6,1.7l-9.9,9.9C1.7,11.3,1,9.8,1,8 C1,4.1,4.1,1,8,1z M8,15c-1.8,0-3.3-0.7-4.6-1.7l9.9-9.9C14.3,4.7,15,6.2,15,8C15,11.9,11.9,15,8,15z"></path> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--confirm"> <g> <path d="M24,0C10.7,0,0,10.7,0,24s10.7,24,24,24s24-10.7,24-24C48,10.8,37.3,0,24,0z M24,44.1c-11,0-20-9-20-20s9-20,20-20s20,9,20,20C44,35.2,35,44.1,24,44.1z"></path> <g> <path d="M22.8,31.9c-1.7,0-2.6,0.9-2.6,2.7c0,0.9,0.2,1.5,0.7,2c0.4,0.5,1.1,0.7,1.9,0.7c0.8,0,1.5-0.2,1.9-0.7c0.5-0.5,0.7-1.1,0.7-2c0-0.8-0.2-1.5-0.7-2C24.3,32.2,23.6,31.9,22.8,31.9z"></path> <path d="M29.3,12.4c-1.3-1.1-3.2-1.7-5.5-1.7s-4.4,0.5-6.5,1.4c-1.1,0.5-0.6,1.7-0.6,1.7l0.5,1c0,0,0.5,0.9,1.7,0.5l0,0c0.5-0.2,1-0.4,1.5-0.6c0.9-0.3,2-0.5,3.1-0.5c1.2,0,2.2,0.3,2.8,0.8c0.7,0.5,1,1.3,1,2.3c0,0.8-0.2,1.6-0.6,2.2c-0.4,0.6-1.3,1.5-2.7,2.6c-1.2,0.9-2,1.8-2.5,2.7c-0.5,0.9-0.7,1.9-0.7,3.2c0,0.9,1.1,1.1,1.1,1.1h0.7c0,0,1.4,0,1.6-1.2l0,0c0-0.8,0.2-1.4,0.5-1.8c0.4-0.6,1.1-1.3,2.2-2.1c1.3-1,2.2-1.8,2.7-2.4s0.9-1.3,1.2-2s0.4-1.5,0.4-2.3C31.4,15.1,30.7,13.5,29.3,12.4z"></path> </g> </g> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--decrement"> <path class="cls-1" d="M11.76,13L24,26.521,37.147,13,41,16.9,24,35,7,16.9Z"></path> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--downarrow"> <polygon points="3.5,6.5 4.5,5.5 8,9 11.5,5.5 12.5,6.5 8,11"></polygon> </symbol><symbol viewBox="0 0 17 16" id="zc__svg--download"> <path class="zpreview__fill" d="M17,16H0v-3h1v2h15v-2h1V16z M12.6,6.6L9,10.3V1H8v9.3L4.4,6.6L3.6,7.4l4.9,4.9l4.9-4.9L12.6,6.6z"></path> </symbol><symbol viewBox="-3 -3 22 22" width="100%" height="100%" id="zc__svg--downscroller"> <path d="M16,4l-8,8L0,4H16z"></path> </symbol><symbol viewBox="0 0 17 16" id="zc__svg--edit"> <path class="zpreview__fill" d="M13,0L1,12v4h3.8L17,4L13,0z M15.6,4l-2.5,2.4l-2.5-2.6L13,1.4L15.6,4z M4.3,15H2v-2.6l7.8-7.8l2.6,2.6L4.3,15z"></path> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--error"> <path d="M41.2,7.2C31.9-2.2,16.7-2.5,7.3,6.9c-9.5,9.3-9.7,24.5-0.4,33.9c9.3,9.5,24.5,9.7,33.9,0.4C50.3,31.9,50.4,16.8,41.2,7.2z M37.9,38.2C30,46,17.4,45.9,9.7,38S2.1,17.5,10,9.8S30.5,2.1,38.2,10C45.9,17.9,45.8,30.5,37.9,38.2z"></path> <path d="M31.7,17.5l-1.1-1.1c-0.8-0.8-1.7-0.1-2,0.1L24,21l-4.6-4.7c-0.3-0.3-1.1-0.8-1.9,0l-1.1,1.1c-0.8,0.8-0.1,1.7,0.1,2L21,24l-4.7,4.6c-0.9,0.9-0.3,1.6-0.1,1.8l1.3,1.3c0.2,0.2,0.9,0.8,1.8-0.1L24,27l4.6,4.7c0.9,0.9,1.6,0.3,1.8,0.1l1.3-1.3c0.2-0.2,0.8-0.9-0.1-1.8L27,24l4.7-4.6C32,19.1,32.5,18.4,31.7,17.5z"></path> </symbol><symbol viewBox="0 0 12 12" id="zc__svg--fastbackward" width="100%" height="100%"><polygon points="5.6,10.3 1.9,6.5 5.6,2.6 6.3,3.4 3.3,6.5 6.4,9.6"></polygon><polygon points="9.6,10.3 5.9,6.5 9.6,2.6 10.3,3.4 7.3,6.5 10.4,9.6"></polygon></symbol><symbol viewBox="1 0 12 12" id="zc__svg--fastforward" width="100%" height="100%"><polygon points="7.4,10.3 6.7,9.6 9.7,6.5 6.6,3.4 7.4,2.7 11.1,6.5"></polygon><polygon points="3.4,10.3 2.7,9.6 5.7,6.5 2.6,3.4 3.4,2.7 7.1,6.5"></polygon></symbol><symbol viewBox="0 0 19 10" id="zc__svg--filmstrip"> <path class="zpreview__fill" d="M4,4v5H1V4H4 M5,3H0v7h5V3L5,3z M0,0h19v1H0V0z M18,4v5h-3V4H18 M19,3h-5v7h5V3L19,3z M11,4v5H8V4H11 M12,3H7v7h5V3L12,3z"> </path> </symbol><symbol viewBox="1 0 12 12" id="zc__svg--forward" width="100%" height="100%"><polygon points="4.4,10.3 3.7,9.6 6.7,6.5 3.6,3.4 4.4,2.7 8.1,6.5"></polygon></symbol><symbol viewBox="0 0 17 16" id="zc__svg--fullscreen"> <path class="zpreview__fill" d="M6.9,10.9L2.7,15H5v1H1v-4h1v2.3l4.1-4.1C6.3,10,6.7,10,6.9,10.1S7,10.7,6.9,10.9z M15,12v2.3l-4.1-4.2c-0.2-0.1-0.6-0.1-0.8,0s-0.1,0.6,0,0.8l4.1,4.1H12v1h4v-4H15z M12,1v1h2.3l-4.2,4.1c-0.1,0.2-0.1,0.6,0,0.8C10.2,7,10.4,7,10.5,7s0.3,0,0.4-0.1L15,2.7V5h1V1H12z M6.9,6.1L2.7,2H5V1H1v4h1V2.7l4.1,4.1C6.2,7,6.4,7,6.5,7s0.3,0,0.4-0.1C7,6.7,7,6.3,6.9,6.1z"> </path></symbol><symbol viewBox="0 0 5 10" width="100%" height="100%" id="zc__svg--grippy"> <rect width="2" height="2"></rect><rect x="3" width="2" height="2"></rect> <rect y="4" width="2" height="2"></rect><rect x="3" y="4" width="2" height="2"></rect> <rect x="3" y="8" width="2" height="2"></rect><rect y="8" width="2" height="2"></rect> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--help"><g><path d="M24,0C10.7,0,0,10.7,0,24s10.7,24,24,24s24-10.7,24-24C48,10.8,37.3,0,24,0z M24,44.1c-11,0-20-9-20-20s9-20,20-20s20,9,20,20C44,35.2,35,44.1,24,44.1z"></path><g><path d="M22.8,31.9c-1.7,0-2.6,0.9-2.6,2.7c0,0.9,0.2,1.5,0.7,2c0.4,0.5,1.1,0.7,1.9,0.7c0.8,0,1.5-0.2,1.9-0.7c0.5-0.5,0.7-1.1,0.7-2c0-0.8-0.2-1.5-0.7-2C24.3,32.2,23.6,31.9,22.8,31.9z"></path><path d="M29.3,12.4c-1.3-1.1-3.2-1.7-5.5-1.7s-4.4,0.5-6.5,1.4c-1.1,0.5-0.6,1.7-0.6,1.7l0.5,1c0,0,0.5,0.9,1.7,0.5l0,0c0.5-0.2,1-0.4,1.5-0.6c0.9-0.3,2-0.5,3.1-0.5c1.2,0,2.2,0.3,2.8,0.8c0.7,0.5,1,1.3,1,2.3c0,0.8-0.2,1.6-0.6,2.2c-0.4,0.6-1.3,1.5-2.7,2.6c-1.2,0.9-2,1.8-2.5,2.7c-0.5,0.9-0.7,1.9-0.7,3.2c0,0.9,1.1,1.1,1.1,1.1h0.7c0,0,1.4,0,1.6-1.2l0,0c0-0.8,0.2-1.4,0.5-1.8c0.4-0.6,1.1-1.3,2.2-2.1c1.3-1,2.2-1.8,2.7-2.4s0.9-1.3,1.2-2s0.4-1.5,0.4-2.3C31.4,15.1,30.7,13.5,29.3,12.4z"></path></g></g></symbol><symbol viewBox="1 2 9 9" width="100%" height="100%" id="zc__svg--horizontaldecrement"> <polygon points="6.6,10.3 2.9,6.5 6.6,2.7 7.4,3.4 4.3,6.5 7.4,9.6"></polygon> </symbol><symbol viewBox="1 2 9 9" width="100%" height="100%" id="zc__svg--horizontalincrement"> <polygon points="4.4,10.3 3.7,9.6 6.7,6.5 3.6,3.4 4.4,2.7 8.1,6.5"></polygon> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--increment"> <path class="cls-1" d="M6,32l5,4L24,22,38,36l4-4L24,13Z"></path> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--info"> <g> <g> <path d="M23.9,17.4c1.5,0,2.5-1.1,2.4-2.3c0-1.3-0.9-2.4-2.3-2.4s-2.4,1.1-2.4,2.4C21.6,16.3,22.6,17.4,23.9,17.4z"></path> <path d="M26.2,35.5V21.8c0-1.2-1.4-1.3-1.8-1.3h-0.7c0,0-1.7,0-1.7,1.1v14.3c0,0.3,0.2,0.9,1.3,0.9h1.6C25.3,36.7,26.2,36.5,26.2,35.5z"></path> </g> <path d="M24,0C10.7,0,0,10.7,0,24s10.7,24,24,24s24-10.7,24-24S37.3,0,24,0z M24,44C13,44,4,35,4,24S13,4,24,4s20,9,20,20S35,44,24,44z"></path> </g> </symbol><symbol id="zc__svg--lines" viewBox="0 0 12 12" width="100%" height="100%"> <line x1="11" y1="1" x2="1" y2="11"></line> <line x1="11" y1="5" x2="5" y2="11"></line> <line x1="11" y1="9" x2="9" y2="11"></line> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--maximize"> <g transform="translate(0,-1036.3622)"> <g> <path d="M13,1040.4v8H3v-8H13 M14,1039.4H2v10h12V1039.4L14,1039.4z"></path> </g> </g> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--minimize"> <rect x="2" y="12" width="12" height="1"></rect> </symbol><symbol viewBox="0 0 12 10" width="100%" height="100%" id="zc__svg--minus"> <rect x="2" y="5" width="9" height="1"></rect> </symbol><symbol viewBox="0 0 4 16" id="zc__svg--more"> <path class="zpreview__fill" d="M2,1c0.6,0,1,0.4,1,1S2.6,3,2,3S1,2.6,1,2S1.4,1,2,1 M2,0C0.9,0,0,0.9,0,2s0.9,2,2,2s2-0.9,2-2S3.1,0,2,0L2,0z"> </path><path class="zpreview__fill" d="M2,13c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S1.4,13,2,13 M2,12c-1.1,0-2,0.9-2,2s0.9,2,2,2s2-0.9,2-2S3.1,12,2,12L2,12z"> </path><path class="zpreview__fill" d="M2,7c0.6,0,1,0.4,1,1S2.6,9,2,9S1,8.6,1,8S1.4,7,2,7 M2,6C0.9,6,0,6.9,0,8s0.9,2,2,2s2-0.9,2-2S3.1,6,2,6L2,6z"> </path> </symbol><symbol viewBox="0 0 12 20" id="zc__svg--navigationnext"> <path class="zpreview__fill" d="M1.5,19.5c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4L9,10L0.8,2.2c-0.4-0.4-0.4-1,0-1.4c0.4-0.4,1-0.4,1.4,0l9,8.5c0.2,0.2,0.3,0.5,0.3,0.7c0,0.3-0.1,0.5-0.3,0.7l-9,8.5C2,19.4,1.7,19.5,1.5,19.5z"> </path> </symbol><symbol viewBox="0 0 12 20" id="zc__svg--navigationprevious"> <path class="zpreview__fill" d="M9.8,19.2l-9-8.5c-0.2-0.2-0.3-0.4-0.3-0.7c0-0.2,0.1-0.5,0.3-0.7l9-8.5c0.4-0.4,1-0.4,1.4,0c0.4,0.4,0.4,1,0,1.4L3,10l8.2,7.8c0.4,0.4,0.4,1,0,1.4c-0.2,0.2-0.4,0.3-0.7,0.3C10.3,19.5,10,19.4,9.8,19.2z"></path> </symbol><symbol viewBox="0 0 25 25" id="zc__svg--notfound"> <path class="zpreview__fill" d="M12.5,1.8c5.9,0,10.7,4.8,10.7,10.7s-4.8,10.7-10.7,10.7S1.8,18.4,1.8,12.5S6.6,1.8,12.5,1.8 M12.5,0C5.6,0,0,5.6,0,12.5S5.6,25,12.5,25S25,19.4,25,12.5S19.4,0,12.5,0L12.5,0z"> </path><line class="zpreview__stroke" stroke-width="2" x1="8" y1="8" x2="17" y2="17"> </line><line class="zpreview__stroke" stroke-width="2" x1="17" y1="8" x2="8" y2="17"> </line> </symbol><symbol viewBox="0 0 12 12" width="100%" height="100%" id="zc__svg--plus"> <polygon points="11,6 7,6 7,2 6,2 6,6 2,6 2,7 6,7 6,11 7,11 7,7 11,7 "></polygon> </symbol><symbol viewBox="0 0 16 16" id="zc__svg--print"> <path class="zpreview__fill" d="M16,3h-3.1L12,0H4L3.1,3H0v9h2.8L2,16h12l-0.8-4H16V3z M4.7,1h6.5l0.6,2H4.1L4.7,1z M3.2,15l1.1-6h7.3l1.1,6 H3.2z M15,11h-1.9l-0.6-3h-9l-0.6,3H1V4h14V11z"> </path><rect x="13" y="5" class="zpreview__fill" width="1" height="1"> </rect> </symbol><symbol viewBox="0 0 16 16" width="100%" height="100%" id="zc__svg--restore"> <g transform="translate(0,-1036.3622)"> <path d="M2,1041.4v8h10v-8H2z M11,1048.4H3v-6h8V1048.4z"></path> </g> <g transform="translate(0,-1036.3622)"> <path d="M4,1039.4v3h1v-2h8v6h-2v1h3v-8H4z"></path> </g> </symbol><symbol viewBox="0 0 5 12" id="zc__svg--rightarrow"> <polygon points="4.4,10.3 3.7,9.6 6.7,6.5 3.6,3.4 4.4,2.7 8.1,6.5"></polygon> </symbol><symbol viewBox="-7 -7 64 64" width="100%" height="100%" id="zc__svg--round"> <circle cx="24.5" cy="24.5" r="15.5"></circle> </symbol><symbol viewBox="0 0 15 15" width="100%" height="100%" id="zc__svg--search"> <g id="Ellipse_50" data-name="Ellipse 50" fill="none" stroke="gray" stroke-width="1.5"> <circle cx="6" cy="6" r="6" stroke="none"></circle> <circle cx="6" cy="6" r="5.25" fill="none"></circle> </g> <path id="Rectangle_270" data-name="Rectangle 270" d="M0,0H5A1,1,0,0,1,6,1V1A1,1,0,0,1,5,2H0A0,0,0,0,1,0,2V0A0,0,0,0,1,0,0Z" transform="translate(10.757 9.343) rotate(45)" fill="gray"></path> </symbol><symbol viewBox="0 0 16 16" id="zc__svg--share"> <path class="zpreview__fill" d="M13.5,0.8c0.9,0,1.7,0.8,1.7,1.7s-0.8,1.7-1.7,1.7s-1.7-0.8-1.7-1.7S12.6,0.8,13.5,0.8 M13.5,0C12.1,0,11,1.1,11,2.5S12.1,5,13.5,5S16,3.9,16,2.5S14.9,0,13.5,0L13.5,0z"> </path> <path class="zpreview__fill" d="M2.5,6.3c0.9,0,1.7,0.8,1.7,1.7S3.4,9.7,2.5,9.7S0.8,8.9,0.8,8S1.6,6.3,2.5,6.3 M2.5,5.5C1.1,5.5,0,6.6,0,8s1.1,2.5,2.5,2.5S5,9.4,5,8S3.9,5.5,2.5,5.5L2.5,5.5z"> </path> <path class="zpreview__fill" d="M13.5,11.8c0.9,0,1.7,0.8,1.7,1.7s-0.8,1.7-1.7,1.7s-1.7-0.8-1.7-1.7S12.6,11.8,13.5,11.8 M13.5,11c-1.4,0-2.5,1.1-2.5,2.5s1.1,2.5,2.5,2.5s2.5-1.1,2.5-2.5S14.9,11,13.5,11L13.5,11z"> </path> <rect x="7.5" y="7" transform="matrix(0.4472 -0.8944 0.8944 0.4472 -5.2373 13.1252)" class="zpreview__fill" width="1" height="7.6"></rect> <rect x="4.2" y="4.8" transform="matrix(0.8944 -0.4472 0.4472 0.8944 -1.5256 4.1374)" class="zpreview__fill" width="7.6" height="1"></rect> </symbol><symbol viewBox="0 0 18 17" id="zc__svg--slideshow"> <path class="zpreview__fill" d="M19,2V1h-9V0H9v1H0v1h1v11H0v1h9v0.7l-4,3.7L5.6,19L9,15.8V18h1v-2.2l3.4,3.2l0.5-0.6l-4-3.7V14h9v-1h-1V2H19zM17,13H2V2h15V13z M8,5v5l4.5-2.5L8,5z"></path> </symbol><symbol viewBox="0 0 9 16" id="zc__svg--slideshow__next"> <polyline class="zpreview__fill" points="1,1 8,8 1,15"></polyline> </symbol><symbol viewBox="0 0 10 14" width="100%" height="100%" id="zc__svg--slideshow__pause"> <path class="zpreview__fill" d="M3,1v12H1V1H3 M3,0H1C0.4,0,0,0.4,0,1v12c0,0.6,0.4,1,1,1h2c0.6,0,1-0.4,1-1V1C4,0.4,3.6,0,3,0L3,0z"> </path><path class="zpreview__fill" d="M9,1v12H7V1H9 M9,0H7C6.4,0,6,0.4,6,1v12c0,0.6,0.4,1,1,1h2c0.6,0,1-0.4,1-1V1C10,0.4,9.6,0,9,0L9,0z"> </path> </symbol><symbol viewBox="0 0 12 14" id="zc__svg--slideshow__play"> <path fill="white" d="M1,1.2L11,7L1,12.8L1,1.2 M1,0.2c-0.5,0-1,0.4-1,1v11.5c0,0.6,0.5,1,1,1c0.2,0,0.3,0,0.5-0.1l10-5.8c0.7-0.4,0.7-1.3,0-1.7l-10-5.8C1.3,0.3,1.2,0.2,1,0.2L1,0.2z"> </path> </symbol><symbol viewBox="0 0 9 16" id="zc__svg--slideshow__previous"> <polyline class="zpreview__fill" points="8,1 1,8 8,15"></polyline> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--success"> <g> <path d="M41.2,7.2C31.9-2.3,16.7-2.5,7.3,6.8c-9.5,9.3-9.7,24.5-0.4,33.9c9.3,9.5,24.5,9.7,33.9,0.4C50.3,31.9,50.4,16.7,41.2,7.2z M37.9,38.2c-7.9,7.7-20.5,7.6-28.2-0.3S2.1,17.4,10,9.7S30.5,2.1,38.2,10C45.9,17.8,45.8,30.5,37.9,38.2z"></path> <path d="M36,19.3L23.1,32.2c-1.1,1.1-2.9,1.1-4,0l-1-1l-4.5-4.6c-0.2-0.2-1-1.1-0.1-2l1.1-1.1c0.8-0.8,1.6-0.3,1.9,0l4.6,4.7l12-11.8c0.2-0.2,1.1-1,2-0.1l1.1,1.1C36.8,18.2,36.3,18.9,36,19.3z"></path> </g> </symbol><symbol viewBox="-2 -2 20 20" width="100%" height="100%" id="zc__svg--tick"> <polygon points="14.6,2.4 4.9,12.1 1.2,8.5 0.4,9.3 4.9,13.8 15.4,3.2 "></polygon> </symbol><symbol viewBox="0 0 16 16" id="zc__svg--time" width="100%" height="100%"> <path d="M8,0.6C3.9,0.6,0.6,3.9,0.6,8s3.3,7.4,7.4,7.4s7.4-3.3,7.4-7.4S12.1,0.6,8,0.6z M8,13.9c-3.3,0-5.9-2.6-5.9-5.9c0-3.3,2.6-5.9,5.9-5.9c3.3,0,5.9,2.6,5.9,5.9C13.9,11.3,11.3,13.9,8,13.9z"> </path> <polygon points="8.5,7.5 8.5,4.5 6.5,4.5 6.5,9.5 11.5,9.5 11.5,7.5"> </polygon> </symbol><symbol viewBox="0 0 25 25" id="zc__svg--unableload"> <path class="zpreview__fill" d="M12.5,2c0.7,0,1.5,0.4,1.8,1.1l8.5,15c0.7,1.3-0.3,3-1.8,3H4.1c-1.6,0-2.6-1.7-1.8-3l8.4-15C11.1,2.3,11.8,2,12.5,2 M12.5,0.1c-1.4,0-2.7,0.7-3.4,2l-8.4,15c-0.7,1.2-0.7,2.6,0,3.9c0.7,1.2,2,2,3.5,2h16.8c1.4,0,2.7-0.7,3.4-1.9c0.7-1.2,0.7-2.6,0.1-3.8l-8.5-15C15.3,0.9,13.9,0.1,12.5,0.1L12.5,0.1z"> </path><path class="zpreview__fill" d="M13,14h-1l-0.8-6.5C11.1,6.7,11.7,6,12.5,6h0c0.8,0,1.4,0.7,1.3,1.5L13,14z"> </path><circle class="zpreview__fill" cx="12.5" cy="17.5" r="1.5"> </circle> </symbol><symbol viewBox="0 0 17 16" id="zc__svg--upload"> <path class="zpreview__fill" d="M13.8,4.1C13.2,1.7,11.1,0,8.5,0S3.8,1.7,3.2,4.1C1.4,4.5,0,6.1,0,8c0,1.2,0.5,2.3,1.4,3C2.1,11.6,3,12,4,12h4v4h1v-4h4c1,0,1.9-0.4,2.6-1c0.8-0.7,1.4-1.8,1.4-3C17,6.1,15.6,4.5,13.8,4.1z M13,11h-2.6H9V5.7l2.6,2.7l0.8-0.8L8.5,3.8L4.6,7.6l0.8,0.8L8,5.7V11H6.6H4c-1.7,0-3-1.4-3-3c0-1.3,0.8-2.4,2-2.8C3.3,5.1,3.7,5,4,5c0-0.3,0.1-0.7,0.2-1c0.6-1.8,2.3-3,4.2-3c1.9,0,3.6,1.3,4.2,3c0.1,0.3,0.2,0.6,0.2,1c0.3,0,0.7,0.1,1,0.2c1.2,0.4,2,1.5,2,2.8C16,9.6,14.7,11,13,11z"> </path> </symbol><symbol viewBox="-3 -3 22 22" width="100%" height="100%" id="zc__svg--upscroller"> <path d="M0,12l8-8l8,8H0z"></path> </symbol><symbol viewBox="0 0 48 48" width="100%" height="100%" id="zc__svg--warning"> <g> <path d="M47.3,36.6L27.7,2.5c-0.9-1.4-2.4-2.2-3.9-2.1c-1.6,0-2.9,0.9-3.7,2.2L0.7,36.5c-0.9,1.3-1,3-0.2,4.4c0.8,1.5,2.3,2.4,4,2.4h39c1.6,0,3.1-0.8,3.9-2.2C48.2,39.7,48.2,38,47.3,36.6z M43.9,39.2c-0.1,0.1-0.2,0.2-0.4,0.2h-39c-0.3,0-0.4-0.2-0.5-0.3c-0.1-0.2,0-0.3,0-0.4L23.5,4.6c0,0,0.1-0.2,0.3-0.2c0,0,0,0,0,0c0.1,0,0.3,0,0.4,0.2l19.6,34C44,38.8,44,39,43.9,39.2z"></path> </g> <path d="M24.1,28.4L24.1,28.4c-0.8,0-1.3-0.4-1.4-1l-1-10c-0.1-1.1,1-2,2.4-2l0,0c1.4,0,2.5,0.9,2.4,2l-1,10C25.4,27.9,24.9,28.4,24.1,28.4z"></path> <circle cx="24" cy="33.4" r="2"></circle> </symbol></svg><div id="rm-highlighter-container" class="rm-reset-flex no-print">
	<div id="rm-highlighter-menu" style="display: none;">
		<!--
			<div data-rm-action="clear-all" style="display:">Clear all</div>
		-->
	<div id="rm-highlighter-menu-list" style="">
		<div class="rm-highlighter-menu-li rm-m-t-1" data-menu="highlight" data-menu-details="#rm-highlight-form">
			<div id="rm-highlight-color-selected" class="rm-highlight-color" data-highlight-color="red" data-rm-action="select-color"></div>
		</div>
		<div class="rm-highlighter-menu-li" data-rm-action="view-selection" data-menu="view-selection">		
			<div data-rm-img="view-selection" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/view-selection.svg"></div> 
		</div>
		<div class="rm-highlighter-menu-li" data-rm-action="tts-toggle-start-hm" data-menu="tts">
			<div data-rm-img="play-round" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/play-round.svg"></div> 
		</div>
		<div class="rm-highlighter-menu-li" data-rm-action="google-translate" data-menu="google-translate">		
			<div data-rm-img="google-translate" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/google-translate.svg"></div> 
		</div>
		<div class="rm-highlighter-menu-li" data-rm-action="tweet" data-menu="tweet">
			<div data-rm-img="twitter" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/twitter.svg"></div> 
		</div>	
		<div class="rm-highlighter-menu-li" data-rm-action="open-page" data-page-name="options" data-menu="settings">
			<div data-rm-img="settings" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/settings.svg"></div> 
		</div>	
	</div><div id="rm-highlighter-menu-details" style="display:none">
		<div id="rm-highlight-form" class="rm-highlighter-menu-details" data-position-bottom="-200px" style="display:none">
			<div class="rm-p-7 rm-p-b-1i">
				<textarea id="rm-highlight-note" placeholder="Take a note..." autocomplete="off" spellcheck="false"></textarea>

				<div id="rm-highlighter-colors" class="rm-m-t-4 rm-m-l-0">
					<div class="rm-highlight-color rm-m-l-3i" data-rm-img="pencil" data-rm-img-ext="svg" data-rm-action="toggle-highlight-settings-iframe"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/pencil.svg"></div>
				</div>
			</div>

			<div id="rm-highlight-form-footer" class="p-7">
				<div id="rm-highlight-folder" data-rm-action="toggle-bookmark-iframe">
					<div class="rm-display-inline-block" data-rm-img="folder" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/folder.svg"></div> 
					<div class="rm-highlight-folder-name">Everything</div>
				</div> 
				<div class="btn-update btn-xs rm-btn rm-btn-light" data-rm-action="update" style="display:none">Update</div>
			</div>	
		</div>		
	</div></div>
	<div id="rm-highlighter-menu-update">
	</div>

	

		
</div><div id="rm-toolbar" class="rm-toolbar rm-toolbar-dark rm-toolbar-expand rm-fixed-top no-print" style="display: none;">
	<div class="rm-toolbar-nav rm-mr-auto no-print">
		<div class="rm-toolbar-nav-logo rm-cursor-pointer" data-rm-img="icon" data-rm-img-ext="png" data-rm-action="redirect-to-home"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/png/icon.png"></div>

      	<div class="rm-btn-group" role="group" style="display:none">
			<div class="rm-btn rm-btn-sm btn-secondary" data-rm-action="tts-start">
				<i class="fas fa-play"></i>
			</div>
			<div class="rm-btn rm-btn-sm btn-secondary" data-rm-action="tts-stop">
				<i class="fas fa-stop"></i>
			</div>
		</div>
    </div>

	<div class="rm-ul rm-toolbar-nav rm-ml-auto">
		<div class="rm-li tlite-tooltip" title="Listen">
			<button type="button" class="rm-btn-nav rm-img-22 rm-m-t--1i" data-rm-img="headphones" data-rm-img-ext="svg" data-rm-action="tts-toggle-start"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/headphones.svg"></button>		
		</div>		
		<div class="rm-li tlite-tooltip" title="Edit">
		  <button type="button" class="rm-btn-nav rm-img-20" data-rm-img="edit-text-file-2" data-rm-img-ext="svg" data-rm-action="editing-mode-start"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/edit-text-file-2.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Delete items">
		  <button type="button" class="rm-btn-nav rm-img-21" data-rm-img="erase" data-rm-img-ext="svg" data-rm-action="deletion-mode-start"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/erase.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Translate" id="rm-translate-toggle" data-toggle="true" data-target="#rm-translate">
		  <button type="button" class="rm-btn-nav" data-rm-img="translation" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/translation.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Print">
		  <button type="button" class="rm-btn-nav" data-rm-img="print" data-rm-img-ext="svg" data-rm-action="print"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/print.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Scroll">
		  <button type="button" class="rm-btn-nav rm-img" data-rm-img="scroll-down" data-rm-img-ext="svg" data-rm-action="autoscroll"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/scroll-down.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Settings" id="rm-main-settings-toggle" data-toggle="true" data-target="#rm-main-settings">
		    <button type="button" class="rm-btn-nav" data-rm-img="tune" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/tune.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Save Bookmark">
		  <button type="button" class="rm-btn-nav" data-rm-img="bookmark-2" data-rm-img-ext="svg" data-rm-job="toggle-iframe" data-rm-params="{&quot;iframe_name&quot;:&quot;bookmark&quot;}"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/bookmark-2.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Fullscreen">
		  <button type="button" class="rm-btn-nav" data-rm-action="fullscreen-toggle" data-rm-img="expand" data-rm-img-ext="svg"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/expand.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Close">
		  <button type="button" class="rm-btn-nav" data-rm-img="close" data-rm-img-ext="svg" data-rm-job="hide-iframe" data-rm-params="{&quot;iframe_name&quot;:&quot;reader&quot;}"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/close.svg"></button>
		</div>
	</div>

	<div id="rm-toolbar-toggle" class="rm-cursor-pointer">
		<i class="fas fa-chevron-up"></i>
	</div>	
</div>

<div id="rm-toolbar-tts" class="rm-toolbar rm-toolbar-dark rm-toolbar-expand rm-fixed-top no-print" style="display:none">
	<div class="rm-toolbar-nav rm-mr-auto no-print">
		<div class="rm-tts-wave" style="display:">
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		</div>

		<div class="rm-tts-wave-active" style="display:none">
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		    <span class="rm-tts-wave-stroke"></span>
		</div>
	</div>
		
	<div class="rm-ul rm-toolbar-nav rm-toolbar-nav-center rm-ml-auto">
		<div class="rm-li tlite-tooltip" title="Previous">
			<button type="button" class="rm-btn-nav rm-img-20" data-rm-img="skip-to-start" data-rm-img-ext="svg" data-rm-action="tts-previous"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/skip-to-start.svg"></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Play/Pause">
			<button type="button" class="rm-btn-nav rm-img-20" data-rm-img="play" data-rm-img-ext="svg" data-rm-action="tts-start"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/play.svg"></button>
			<button type="button" class="rm-btn-nav rm-img-20" data-rm-img="pause" data-rm-img-ext="svg" data-rm-action="tts-stop" style="display:none"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/pause.svg"></button>
		</div>	
		<div class="rm-li tlite-tooltip" title="Forward">
			<button type="button" class="rm-btn-nav rm-img-20" data-rm-img="end" data-rm-img-ext="svg" data-rm-action="tts-forward"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/end.svg"></button>
		</div>	
	</div>	

	<div class="rm-ul rm-toolbar-nav rm-ml-auto">
		<div class="rm-li">
			<button type="button" class="rm-btn-nav rm-img-20" data-rm-img="audio" data-rm-img-ext="svg" data-rm-img-classes="rm-float-left rm-m-t-2 rm-m-r-2" data-rm-action="tts-settings"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/audio.svg" class="rm-float-left rm-m-t-2 rm-m-r-2"><span class="rm-m-l-5 rm-btn-nav-text rm-display-inline-block rm-float-left">Voice options</span></button>
		</div>
		<div class="rm-li tlite-tooltip" title="Close TTS">
		  <button type="button" class="rm-btn-nav" data-rm-img="close" data-rm-img-ext="svg" data-rm-action="tts-toggle-stop"><img src="chrome-extension://llimhhconnjiflfimocjggfjdlmlhblm/img/svg/close.svg"></button>
		</div>
	</div>	
</div>	

<div id="rm-main-settings" class="rm-toolbar-aside" data-toggle-target="true" data-parent="#rm-main-settings-toggle" style="display:none">
	<div class="rm-accordion">
	  	<div class="rm-card">
		    <div class="rm-card-header rm-hover-brightness" data-target="#rm-text-settings">
		    	<span class="rm-m-l--3 rm-m-r-3"><b>Aa</b></span> TYPOGRAPHY
		    </div>
		    <div id="rm-text-settings" class="rm-card-body show" data-parent="#rm-main-settings">
				<label class="rm-label">Font</label>
				<select class="custom-select rm-custom-select rm-border-0i" name="font_family" data-css="true" data-css-elem="*">
					<option value="arial">Arial</option>
					<option value="arial black">Arial Black</option>
					<option value="athelas">Athelas</option>
					<option value="comic sans ms">Comic Sans MS</option>
					<option value="courier new">Courier New</option>
					<option value="courier">Courier</option>
					<option value="didot">Didot</option>
					<option value="georgia">Georgia</option>
					<option value="gill sans">Gill Sans</option>
					<option value="helvetica" selected="">Helvetica</option>
					<option value="impact">Impact</option>
					<option value="iowan old style">Iowan Old Style</option>
					<option value="palatino">Palatino</option>
					<option value="optima">Optima</option>
					<option value="sans-serif">Sans-serif</option>
					<option value="sassoon-primary">Sassoon Primary</option>
					<option value="seravek">Seravek</option>
					<option value="serif">Serif</option>
					<option value="times new roman">Times New Roman</option>
					<option value="trebuchet ms">Trebuchet MS</option>
					<option value="opendyslexic">OpenDyslexic</option>
					<option value="opendyslexic-mono">OpenDyslexic Mono</option>
					<option value="opendyslexic-bold">OpenDyslexic Bold</option>
					<option value="opendyslexic-italic">OpenDyslexic Italic</option>
					<option value="opendyslexic-bold-italic">OpenDyslexic BoldItalic</option>
					<option value="lexend-deca">LexendDeca</option>
					<option value="lexend-exa">Lexend Exa</option>
					<option value="lexend-giga">Lexend Giga</option>
					<option value="lexend-mega">Lexend Mega</option>
					<option value="lexend-peta">Lexend Peta</option>
					<option value="lexend-tera">Lexend Tera</option>
					<option value="lexend-zetta">Lexend Zetta</option>
					<option value="lexie-readable">LexieReadable</option>
					<option value="lexie-readable-bold">LexieReadable Bold</option>
					<option value="lora">Lora</option>
					<option value="lora-bold">Lora Bold</option>
					<option value="lora-italic">Lora Italic</option>
					<option value="lora-bold-italic">Lora BoldItalic</option>
					<option value="verdana">Verdana</option>
				</select>

				<label class="rm-label rm-m-t-22 rm-m-b-12">Text Align</label>
				<table id="rm-text-align" class="rm-full-width">
					<tbody><tr>
						<td class="rm-width-15 active">
							<i class="fas fa-align-left"></i> <br>
							<input type="radio" name="text_align" data-css="true" data-css-elem="*" value="left" checked="checked" style="display:none">
						</td>
						<td class="rm-width-15">
							<i class="fas fa-align-center"></i> <br>
							<input type="radio" name="text_align" data-css="true" data-css-elem="*" value="center" style="display:none">
						</td>	
						<td class="rm-width-15">
							<i class="fas fa-align-right"></i> <br>
							<input type="radio" name="text_align" data-css="true" data-css-elem="*" value="right" style="display:none">
						</td>	
						<td>
							<i class="fas fa-align-justify"></i> <br>
							<input type="radio" name="text_align" data-css="true" data-css-elem="*" value="justify" style="display:none">
						</td>	
					</tr>		
				</tbody></table>	

				<label class="rm-label rm-m-t-22 rm-m-b-12">Text Size</label>
				<div class="rm-range-control">
				    <input type="range" name="font_size" data-css="true" data-css-elem="*" data-css-postfix="px" min="12" max="100" step="1" value="19" data-thumbwidth="19">
				    <output name="rangeVal">19</output>
			  	</div>

			  	<label class="rm-label rm-m-t-22 rm-m-b-12">Line Height</label>
				<div class="rm-range-control">
				    <input type="range" name="line_height" data-css="true" data-css-elem="*" data-css-postfix="px" min="10" max="100" step="1" value="30" data-thumbwidth="30">
				    <output name="rangeVal">30</output>
			  	</div>

			  	<label class="rm-label rm-m-t-25 rm-m-b-12">Letter Spacing</label>
				<div class="rm-range-control">
				    <input type="range" name="letter_spacing" data-css="true" data-css-elem="*" data-css-postfix="px" min="0" max="10" step="0.1" value="0.2" data-thumbwidth="0.2">
				    <output name="rangeVal">0.2</output>
			  	</div>

			  	<label class="rm-label rm-m-t-25 rm-m-b-12">Width</label>
				<div class="rm-range-control">
				    <input type="range" name="width" data-elem="#rm-article" data-css="true" data-css-postfix="px" min="100" max="1280" step="10" value="680" data-thumbwidth="100">
			  	</div>	

			  	<div class="rm-m-t-25 rm-m-b-10">
		        	<button class="rm-btn rm-btn-primary rm-btn-save rm-btn-xs" data-rm-action="save" data-rm-loading="Saving" data-rm-alert="Saved!">Save</button>
		    	</div>
		    </div>
	  	</div>
	  	<div class="rm-card">
		    <div class="rm-card-header rm-hover-brightness" data-target="#rm-themes-settings">
		        <i class="fas fa-swatchbook rm-m-r-5"></i> THEMES
		    </div>
			<div id="rm-themes-settings" class="rm-card-body rm-p-b-7i" data-parent="#rm-main-settings">
			  	<label class="rm-label rm-m-t-10">Current Theme <button class="rm-btn rm-btn-light btn-delete rm-btn-xxs rm-m-t--1 rm-m-l-3" style="display:none;">Delete</button></label>
			  	<!--<input type="text" name="themes" class="form-control"/>-->
			  	<div class="rm-ul rm-list-unstyled rm-list-inline rm-m-b-15">
			  		<div name="theme" data-value="custom" class="rm-li rm-list-inline-item tooltip-s" title="Add custom theme" data-toggle="true" data-target="#rm-new-theme"></div>
			  	</div>

			  	<div id="test">
			  	</div>	

			  	

			  	<p>
		        	<small>
		        		To change the extension and app theme, go to the
		        		<a href="#/" data-rm-action="open-page" data-page-name="options" class="rm-color-primary-i">
		        			<b>Settings page <i class="fas fa-external-link-square-alt"></i></b>
		        		</a> 
		        	</small>
		        </p> 						
			</div>
	  	</div>
	 	<div class="rm-card">
		    <div class="rm-card-header rm-hover-brightness" data-target="#rm-display-settings">
		        <i class="fas fa-toggle-on rm-m-r-5"></i> DISPLAY
		    </div>
			<div id="rm-display-settings" class="rm-card-body" data-parent="#rm-main-settings">
				<label class="rm-label rm-label-isolate">
					<input type="checkbox" class="rm-m-r-3" name="rm_article_outline" data-toggle="true" data-elem-prefix="#"> 
					<span>Outline</span>
				</label>
				<label class="rm-label rm-label-isolate">
					<input type="checkbox" class="rm-m-r-3" name="img" data-toggle="true" checked=""> 
					<span>Images</span>
				</label>
				<label class="rm-label rm-label-isolate">
					<input type="checkbox" class="rm-m-r-3" name="rm_article_meta" data-toggle="true" data-elem-prefix="#" checked=""> 
					<span>Meta</span> 
				</label>
				<label class="rm-label rm-label-isolate">
					<input type="checkbox" class="rm-m-r-3" name="rm_article_author" data-toggle="true" data-elem-prefix="#" checked=""> 
					<span>Author</span> 
				</label>
				<label class="rm-label rm-label-isolate">
					<input type="checkbox" class="rm-m-r-3" name="rm_article_reading_time" data-toggle="true" data-elem-prefix="#" checked=""> 
					<span>Reading Time</span> 
				</label>
				<label class="rm-label rm-label-isolate">
					<input type="checkbox" class="rm-m-r-3" name="rm_article_source" data-toggle="true" data-elem-prefix="#" checked=""> 
					<span>Source URL</span> 
				</label>

				<div class="rm-m-t-10 rm-m-b-10 rm-m-l-6">
		        	<div class="rm-btn rm-btn-primary rm-btn-save rm-btn-xs" data-rm-action="save" data-rm-loading="Saving" data-rm-alert="Saved!">Save</div>
		    	</div>
			</div>
	  	</div>
		<div class="rm-card">
		    <div class="rm-card-header rm-hover-brightness" data-target="#rm-dyslexia-ruler-settings">
		    	<i class="fas fa-ruler-horizontal rm-m-r-5"></i> READING RULER
		    </div>
		    <div id="rm-dyslexia-ruler-settings" class="rm-card-body" data-parent="#rm-main-settings">
		        <label class="rm-label rm-label-isolate">
	      			<input type="checkbox" class="rm-m-r-3" name="rm_reading_ruler" data-toggle="true" data-elem-prefix="#"> 
	      			<span>Display Ruler</span>
	      		</label>

	      		<table class="rm-m-t-10">
			  		<tbody><tr>
			  			<td class="rm-p-r-10">Color</td>
			  			<td>
			  				<input type="color" name="reading_ruler_bg_color" data-css="true" data-css-attr="background-color" data-elem="#rm-reading-ruler" value="#ADADAD">
			  			</td>
			  		</tr>	
			  	</tbody></table>

			  	<label class="rm-label rm-m-t-20">Opacity</label>
				<div class="rm-range-control rm-m-b-10">
				    <input type="range" name="reading_ruler_opacity" data-elem="#rm-reading-ruler" data-css="true" data-css-attr="opacity" min="0.1" max="1" step="0.1" value="0.5" data-thumbwidth="0.5">
				    <output name="rangeVal">0.5</output>
			  	</div>

				<label class="rm-label rm-m-t-15">Height</label>
				<div class="rm-range-control">
				    <input type="range" name="reading_ruler_height" data-elem="#rm-reading-ruler" data-css="true" data-css-attr="height" data-css-postfix="px" min="10" max="250" step="2" value="40" data-thumbwidth="40">
				    <output name="rangeVal">40</output>
			  	</div>

			  	<label class="rm-label rm-m-t-20">Position</label>
				<div class="rm-range-control rm-m-b-10">
				    <input type="range" name="reading_ruler_top" data-elem="#rm-reading-ruler" data-css="true" data-css-attr="top" data-css-postfix="%" min="10" max="90" step="5" value="45" data-thumbwidth="45">
				    <output name="rangeVal">45</output>
			  	</div>

			  	<div class="rm-m-t-25 rm-m-b-10">
		        	<div class="rm-btn rm-btn-primary rm-btn-save rm-btn-xs" data-rm-action="save" data-rm-loading="Saving" data-rm-alert="Saved!">Save</div>
		    	</div>
		    </div>
		</div>
	  	<div class="rm-card">
		    <div class="rm-card-header rm-hover-brightness" data-target="#rm-auto-run-rules">
		    	<span class="rm-m-r-5"><i class="fas fa-globe rm-m-r-1"></i></span> AUTO-RUN RULES 
		    	<span class="rm-pill rm-pill-premium rm-m-l-5" data-rm-action="upgrade"><small>PREMIUM</small></span>
		    </div>
		    <div id="rm-auto-run-rules" class="rm-card-body" data-parent="#rm-main-settings">
		        <textarea name="auto_run_rules" class="rm-form-control rm-textarea rm-m-b-10i" rows="5" spellcheck="false" placeholder="Add regex expressions here (seperated by commas)" readonly=""></textarea>

		        <p>
		        	<small>
		        		Automatically activate Reader Mode on specific websites by using regex. 
		        		<a href="https://readermode.io/features/auto-run" target="_blank" class="rm-color-primary-i">
		        			<b>Learn more <i class="fas fa-external-link-square-alt"></i></b>
		        		</a> 
		        	</small>
		        </p> 

		        <div class="rm-m-t-10 rm-m-b-10">
		        	<div class="rm-btn rm-btn-primary rm-btn-save rm-btn-xs" data-rm-action="save" data-rm-loading="Saving">Save</div>
		    	</div>
		    </div>
	  	</div>		
	  	<div class="rm-card">
		    <div class="rm-card-header rm-hover-brightness" data-target="#rm-custom-css-settings">
		    	<span class="rm-m-r-5">{ }</span> CUSTOM CSS 
		    	<span class="rm-pill rm-pill-premium rm-m-l-5" data-rm-action="upgrade"><small>PREMIUM</small></span>
		    </div>
		    <div id="rm-custom-css-settings" class="rm-card-body" data-parent="#rm-main-settings">
		        <textarea name="custom_css" class="rm-form-control rm-textarea rm-m-b-10i" rows="5" spellcheck="false" placeholder="Add your custom css..." readonly=""></textarea>
		        <div>
		        	<input type="checkbox" class="rm-m-r-3" name="use_custom_css" checked="" disabled=""> Use custom css
		    	</div>

		        <p>
		        	<small>
		        		Add your own CSS to customize the article's layout or design
		        		<a href="https://help.readermode.io/using-reader-mode/custom-css" target="_blank" class="rm-color-primary-i">
		        			<b>Learn more <i class="fas fa-external-link-square-alt"></i></b>
		        		</a> 
		        	</small>
		        </p> 

		        <div class="rm-m-t-10 rm-m-b-10">
		        	<div class="rm-btn rm-btn-primary rm-btn-save rm-btn-xs" data-rm-action="save" data-rm-loading="Saving">Save</div>
		    	</div>
		    </div>
	  	</div>
	</div>
</div>

<div id="rm-translate" class="rm-toolbar-aside" data-toggle-target="true" data-parent="#rm-translate-toggle" style="display:none">
	<div class="rm-card">
	    <div class="rm-card-header">
	    	<i class="fas fa-language rm-m-r-5"></i> TRANSLATE
	    </div>
	    <div class="rm-card-body">
			<label class="rm-label rm-display-block rm-m-b-10">Default translation language:</label>
			<select class="custom-select rm-custom-select rm-border-0i" name="default_translation_language">
				<option value="af">🇿🇦 Afrikaans</option>
				<option value="sq">🇦🇱 Albanian</option>
				<option value="ar">🇸🇦 Arabic</option>
				<option value="az">🇦🇿 Azerbaijani</option>
				<option value="eu">🏴󠁥󠁳󠁰󠁶󠁿 Basque</option>
				<option value="bn">🇧🇩 Bengali</option>
				<option value="be">🇧🇾 Belarusian</option>
				<option value="bg">🇧🇬 Bulgarian</option>
				<option value="ca">🏴󠁥󠁳󠁰󠁶󠁿 Catalan</option>
				<option value="zh-CN">🇨🇳 Chinese Simplified</option>
				<option value="zh-TW">🇨🇳 Chinese Traditional</option>
				<option value="hr">🇭🇷 Croatian</option>
				<option value="cs">🇨🇿 Czech</option>
				<option value="da">🇩🇰 Danish</option>
				<option value="nl">🇳🇱 Dutch</option>
				<option value="en" selected="">🇬🇧 English</option>
				<option value="eo">🏴󠁥󠁳󠁰󠁶󠁿 Esperanto</option>
				<option value="et">🇪🇪 Estonian</option>
				<option value="tl">🇵🇭 Filipino</option>
				<option value="fi">🇫🇮 Finnish</option>
				<option value="fr">🇫🇷 French</option>
				<option value="gl">🇪🇸 Galician</option>
				<option value="ka">🇬🇪 Georgian</option>
				<option value="de">🇩🇪 German</option>
				<option value="el">🇬🇷 Greek</option>
				<option value="gu">🇮🇳 Gujarati</option>
				<option value="ht">🇭🇹 Haitian Creole</option>
				<option value="iw">🇮🇱 Hebrew</option>
				<option value="hi">🇮🇳 Hindi</option>
				<option value="hu">🇭🇺 Hungarian</option>
				<option value="is">🇮🇸 Icelandic</option>
				<option value="id">🇮🇩 Indonesian</option>
				<option value="ga">🇮🇪 Irish</option>
				<option value="it">🇮🇹 Italian</option>
				<option value="ja">🇯🇵 Japanese</option>
				<option value="kn">🇮🇳 Kannada</option>
				<option value="ko">🇰🇷 Korean</option>
				<option value="la">🏴󠁥󠁳󠁰󠁶󠁿 Latin</option>
				<option value="lv">🇱🇻 Latvian</option>
				<option value="lt">🇱🇹 Lithuanian</option>
				<option value="mk">🇲🇰 Macedonian</option>
				<option value="ms">🇲🇾 Malay</option>
				<option value="mt">🇲🇹 Maltese</option>
				<option value="no">🇳🇴 Norwegian</option>
				<option value="fa">🇮🇷 Persian</option>
				<option value="pl">🇵🇱 Polish</option>
				<option value="pt">🇵🇹 Portuguese</option>
				<option value="ro">🇷🇴 Romanian</option>
				<option value="ru">🇷🇺 Russian</option>
				<option value="sr">🇷🇸 Serbian</option>
				<option value="sk">🇸🇰 Slovak</option>
				<option value="sl">🇸🇮 Slovenian</option>
				<option value="es">🇪🇸 Spanish</option>
				<option value="sw">🏴󠁥󠁳󠁰󠁶󠁿 Swahili</option>
				<option value="sv">🇸🇪 Swedish</option>
				<option value="ta">🇮🇳 Tamil</option>
				<option value="te">🇮🇳 Telugu</option>
				<option value="th">🇹🇭 Thai</option>
				<option value="tr">🇹🇷 Turkish</option>
				<option value="uk">🇺🇦 Ukrainian</option>
				<option value="ur">🇵🇰 Urdu</option>
				<option value="vi">🇻🇳 Vietnamese</option>
				<option value="cy">🇬🇧 Welsh</option>
				<option value="yi">🇮🇱 Yiddish</option>
			</select>	

			<div class="rm-m-t-10 rm-m-b-10">
				<div class="rm-btn rm-btn-light rm-btn-save rm-btn-xs rm-m-r-5 rm-p-l-10i rm-p-r-10i" data-rm-loading="Setting" data-rm-alert="Set!">Set</div>
				<div class="rm-btn rm-btn-primary rm-btn-save rm-btn-xs" data-rm-action="translate">Translate</div>
			</div>
	    </div>
	</div>	
</div>

<div id="rm-deletion-mode" class="rm-toolbar-aside" style="display:none">
	<div class="rm-card">
	    <div class="rm-card-body text-center rm-p-0i rm-p-t-5i rm-p-b-5i">
	   		<center>
	   			<a href="#/" class="rm-deletion-mode-undo rm-m-r-10" title="Undo" style="display:none">
	        		<i class="fas fa-undo"></i> 
	        	</a>

		        <div class="rm-btn rm-btn-xs rm-btn-light rm-p-l-25i rm-p-r-25i rm-m-r-3">Cancel</div>
		        <div class="rm-btn rm-btn-xs rm-btn-primary rm-p-l-30i rm-p-r-30i">Save</div>
	    	</center>
	    </div>
	</div>	
</div>

<div id="rm-editing-mode" class="rm-toolbar-aside" style="display:none">
	<div class="rm-card">
	    <div class="rm-card-body text-center rm-p-0i rm-p-t-5i rm-p-b-5i">
	    	<center>
		        <div class="rm-btn rm-btn-xs rm-btn-light rm-p-l-25i rm-p-r-25i rm-m-r-3">Cancel</div>
		        <div class="rm-btn rm-btn-xs rm-btn-primary rm-p-l-30i rm-p-r-30i">Save</div>
	    	</center>
	    </div>
	</div>	
</div>

<div id="rm-reading-ruler" class="no-print" style="display:none">
</div>

<div id="rm-article-outline" class="rm-toolbar-aside" style="display:none">
	<div class="rm-card">
	    <div class="rm-card-header">
	    	<i class="fas fa-list-ul rm-m-r-5"></i> OUTLINE
	    	<i class="fas fa-chevron-up rm-card-body-toggle rm-float-right rm-m-t-1"></i>
	    </div>
	    <div class="rm-card-body">
	        <div class="rm-ul rm-list-unstyled rm-m-b-5">
	        </div>	
	    </div>
	</div>	
</div>

<div id="give-freely-root-mbnbehikldjhnfehhnaidhjhoofhpehk" class="give-freely-root" data-extension-id="mbnbehikldjhnfehhnaidhjhoofhpehk" data-extension-name="CSS Peeper" style="display: block;"></div></body></html>