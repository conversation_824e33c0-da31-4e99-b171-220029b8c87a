## 📋 Pre-Push Quality Assurance & Error Detection Instructions

### **Context for the QA Assistant**

You're performing a critical quality check before pushing major architectural changes to GitHub. The project has undergone:
- Phase 1: Type system implementation (Supabase types)
- Phase 2: Form handling migration (React Hook Form + Zod)
- Phase 3: State management consolidation (Context → Zustand)
- Phase 4: Memory leak fixes (Real-time subscriptions)

Your job is to find any breaking changes, inconsistencies, or errors that could cause the application to fail in production.

---

## **Task 1: Critical Path Testing (1 hour)**

### **1.1 Check Core Application Flow**

Test these critical paths in order:

```javascript
// Create test checklist in console
const criticalPaths = {
  auth: {
    login: false,
    logout: false,
    sessionPersistence: false,
    protectedRoutes: false
  },
  documents: {
    create: false,
    read: false,
    update: false,
    delete: false,
    realtimeSync: false
  },
  ui: {
    navigation: false,
    modals: false,
    themes: false,
    responsive: false
  }
};

// Test each path and update the checklist
window.criticalPathTest = criticalPaths;
```

**Manual Testing Steps:**

1. **Authentication Flow**
   - [ ] Can login with email/password
   - [ ] OAuth login works (GitHub/Google)
   - [ ] Logout clears all stores
   - [ ] Refresh maintains session
   - [ ] Protected routes redirect when logged out

2. **Document Operations**
   - [ ] Create new document
   - [ ] Open existing document
   - [ ] Edit and auto-save works
   - [ ] Tags can be added/removed
   - [ ] Delete document works
   - [ ] Real-time sync updates

3. **UI Interactions**
   - [ ] Sidebar toggle works
   - [ ] Theme switching persists
   - [ ] Search functionality works
   - [ ] All modals open/close properly

---

## **Task 2: Dependency & Import Verification (30 minutes)**

### **2.1 Check for Missing Imports**

```javascript
// Run this script to check for common import issues
const importChecker = {
  patterns: [
    // Old Context imports that should be removed
    /import.*from ['"].*\/contexts\//g,
    // Direct useState that should be migrated
    /useState\(/g,
    // Supabase imports should use optimized version
    /from ['"]@supabase\/supabase-js['"]/g,
    // Check for missing dependencies
    /import.*from ['"].*undefined['"]/g
  ],
  
  async checkFile(filepath) {
    // This would need actual file reading
    console.log(`Checking ${filepath} for import issues...`);
  },
  
  criticalFiles: [
    'src/App.jsx',
    'src/main.jsx',
    'src/hooks/useAuth.js',
    'src/stores/authStore.js',
    'src/components/ExpandedViewEnhanced.jsx'
  ]
};
```

### **2.2 Verify Package.json Dependencies**

```bash
# Check all imports are in package.json
npm ls

# Check for missing peer dependencies
npm audit

# Verify no conflicting versions
npm dedupe --dry-run
```

---

## **Task 3: Store & State Consistency (45 minutes)**

### **3.1 Store Health Check**

```javascript
// Comprehensive store verification
const storeHealthCheck = {
  // Check all stores are initialized
  checkStoresExist() {
    const requiredStores = [
      'authStore', 'settingsStore', 'uiStore', 
      'documentStore', 'editorStore', 'formStore'
    ];
    
    const missing = [];
    requiredStores.forEach(store => {
      try {
        const storeState = window.__APP_STATE__?.get(store.replace('Store', ''));
        if (!storeState) missing.push(store);
      } catch (e) {
        missing.push(store);
      }
    });
    
    if (missing.length) {
      console.error('❌ Missing stores:', missing);
      return false;
    }
    console.log('✅ All stores initialized');
    return true;
  },

  // Check for state conflicts
  checkStateConflicts() {
    console.group('🔍 Checking for state conflicts');
    
    // Check if both Context and Store are being used
    const contextUsage = document.querySelectorAll('[class*="Provider"]').length;
    const storeUsage = window.__APP_STATE__ ? Object.keys(window.__APP_STATE__.getAll()).length : 0;
    
    if (contextUsage > 0 && storeUsage > 0) {
      console.warn('⚠️ Both Contexts and Stores detected - verify compatibility layers');
    }
    
    console.groupEnd();
  },

  // Check for memory leaks
  checkMemoryLeaks() {
    if (window.__REALTIME_DEBUG__) {
      const stats = window.__REALTIME_DEBUG__.getStats();
      if (stats.possibleLeaks.length > 0) {
        console.error('❌ Memory leaks detected:', stats.possibleLeaks);
        return false;
      }
      console.log('✅ No memory leaks detected');
      return true;
    }
    console.warn('⚠️ Realtime debugger not available');
  },

  runAll() {
    console.log('🏥 Running Store Health Check...');
    this.checkStoresExist();
    this.checkStateConflicts();
    this.checkMemoryLeaks();
  }
};

// Make available globally
window.storeHealthCheck = storeHealthCheck;
```

---

## **Task 4: Error Boundary & Exception Check (30 minutes)**

### **4.1 Check Error Handling**

```javascript
// Error detection utility
const errorChecker = {
  errors: [],
  warnings: [],
  
  startMonitoring() {
    // Override console.error temporarily
    const originalError = console.error;
    console.error = (...args) => {
      this.errors.push({
        message: args.join(' '),
        timestamp: new Date().toISOString(),
        stack: new Error().stack
      });
      originalError.apply(console, args);
    };
    
    // Override console.warn
    const originalWarn = console.warn;
    console.warn = (...args) => {
      this.warnings.push({
        message: args.join(' '),
        timestamp: new Date().toISOString()
      });
      originalWarn.apply(console, args);
    };
    
    // Monitor unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.errors.push({
        message: `Unhandled Promise: ${event.reason}`,
        timestamp: new Date().toISOString(),
        promise: event.promise
      });
    });
    
    console.log('🔴 Error monitoring started');
  },
  
  getReport() {
    return {
      errorCount: this.errors.length,
      warningCount: this.warnings.length,
      errors: this.errors,
      warnings: this.warnings,
      critical: this.errors.filter(e => 
        e.message.includes('Cannot read') || 
        e.message.includes('undefined') ||
        e.message.includes('null')
      )
    };
  },
  
  clear() {
    this.errors = [];
    this.warnings = [];
  }
};

window.errorChecker = errorChecker;
```

---

## **Task 5: Form Validation Verification (30 minutes)**

### **5.1 Test Migrated Forms**

```javascript
// Form validation tester
const formTester = {
  async testAuthForm() {
    console.group('🧪 Testing AuthFormElite');
    
    // Test empty submission
    console.log('Testing empty form submission...');
    // Should show validation errors
    
    // Test invalid email
    console.log('Testing invalid email...');
    // Should show "Invalid email" error
    
    // Test weak password
    console.log('Testing weak password...');
    // Should show password requirements
    
    console.groupEnd();
  },
  
  checkZodSchemas() {
    // Verify all schemas are valid
    try {
      const schemas = ['loginSchema', 'signupSchema', 'documentSchema'];
      schemas.forEach(schema => {
        console.log(`✅ ${schema} is valid`);
      });
    } catch (e) {
      console.error('❌ Schema validation error:', e);
    }
  }
};
```

---

## **Task 6: Pre-Push Checklist Generator (15 minutes)**

### **6.1 Create Comprehensive Report**

```javascript
// Final pre-push verification
const prePushVerification = {
  async runAllChecks() {
    console.log('🚀 RUNNING PRE-PUSH VERIFICATION...\n');
    
    const report = {
      timestamp: new Date().toISOString(),
      checks: {
        stores: false,
        auth: false,
        forms: false,
        realtime: false,
        errors: false,
        performance: false
      },
      issues: [],
      warnings: [],
      readyToPush: false
    };
    
    // 1. Check stores
    console.log('1️⃣ Checking stores...');
    report.checks.stores = window.storeHealthCheck?.checkStoresExist() || false;
    
    // 2. Check auth
    console.log('2️⃣ Checking authentication...');
    report.checks.auth = !!window.__APP_STATE__?.get('auth');
    
    // 3. Check forms
    console.log('3️⃣ Checking forms...');
    const hasReactHookForm = !!window.React?.version && document.querySelector('[class*="form"]');
    report.checks.forms = hasReactHookForm;
    
    // 4. Check realtime
    console.log('4️⃣ Checking realtime subscriptions...');
    const realtimeStats = window.__REALTIME_DEBUG__?.getStats();
    report.checks.realtime = realtimeStats ? realtimeStats.possibleLeaks.length === 0 : false;
    
    // 5. Check for errors
    console.log('5️⃣ Checking for errors...');
    const errorReport = window.errorChecker?.getReport();
    report.checks.errors = !errorReport || errorReport.errorCount === 0;
    
    // 6. Performance check
    console.log('6️⃣ Checking performance...');
    report.checks.performance = performance.memory ? 
      performance.memory.usedJSHeapSize < performance.memory.jsHeapSizeLimit * 0.9 : true;
    
    // Generate summary
    const passedChecks = Object.values(report.checks).filter(Boolean).length;
    const totalChecks = Object.keys(report.checks).length;
    report.readyToPush = passedChecks === totalChecks;
    
    // Display report
    console.log('\n' + '='.repeat(50));
    console.log('📊 PRE-PUSH VERIFICATION REPORT');
    console.log('='.repeat(50));
    console.table(report.checks);
    console.log(`\nStatus: ${passedChecks}/${totalChecks} checks passed`);
    
    if (report.readyToPush) {
      console.log('✅ READY TO PUSH TO GITHUB');
    } else {
      console.log('❌ ISSUES FOUND - DO NOT PUSH YET');
      console.log('Fix the failed checks before pushing');
    }
    
    return report;
  },
  
  generateFixScript() {
    console.log('\n📝 FIX SCRIPT FOR COMMON ISSUES:\n');
    console.log(`
// Fix missing stores
import './stores';

// Fix auth issues  
window.__APP_STATE__.get('auth').reset();

// Clear memory leaks
window.__REALTIME_DEBUG__.reset();

// Clear errors
window.errorChecker.clear();
    `);
  }
};

// Make available globally
window.prePushVerification = prePushVerification;
```

---

## **Task 7: Quick Fix Guide**

### **Common Issues & Solutions**

```markdown
## Quick Fix Reference

### Issue: "Cannot read property of undefined"
**Check:** Missing optional chaining
**Fix:** Add ?. operator
```javascript
// Bad: user.profile.name
// Good: user?.profile?.name
```

### Issue: "Store not found"
**Check:** Store not imported
**Fix:** Import store in main.jsx
```javascript
import './stores/authStore';
```

### Issue: "Memory leak detected"
**Check:** Realtime subscriptions not cleaned
**Fix:** Check useEffect cleanup
```javascript
return () => {
  supabase.removeChannel(channel);
};
```

### Issue: "Form validation not working"
**Check:** Zod resolver not connected
**Fix:** Add resolver to useForm
```javascript
useForm({
  resolver: zodResolver(schema)
});
```
```

---

## **Deliverables for QA Assistant**

1. **Run all checks in order**
2. **Document any issues found**
3. **Provide specific file names and line numbers**
4. **Suggest fixes for each issue**
5. **Generate final GO/NO-GO recommendation**

## **Final Command to Run**

```javascript
// Run this for complete verification
async function runCompleteQACheck() {
  console.clear();
  console.log('🔍 STARTING COMPLETE QA CHECK...\n');
  
  // Start error monitoring
  window.errorChecker.startMonitoring();
  
  // Navigate through app to trigger any issues
  console.log('Please navigate through the app for 30 seconds...');
  
  // After navigation, run verification
  setTimeout(() => {
    window.prePushVerification.runAllChecks().then(report => {
      if (!report.readyToPush) {
        window.prePushVerification.generateFixScript();
      }
    });
  }, 30000);
}

runCompleteQACheck();
```

This comprehensive QA check will catch any issues before pushing to GitHub!