<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    
    <!-- Essential favicon formats for Google search results -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="48x48" href="/favicon-48x48.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png">
    
    <!-- Keep SVG for modern browsers -->
    <link rel="icon" type="image/svg+xml" href="/devlog-favicon.svg">
    
    <!-- Apple devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    
    <!-- Mobile specific meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    
    <title>Developer Knowledge Base - Capture Your Coding Journey | DevLog</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Build your personal developer knowledge base with DevLog. Save code snippets, track versions, preserve AI conversations, and never forget why your code works. Try free for 14 days.">
    <meta name="keywords" content="developer knowledge base, code snippet manager, developer documentation tool, personal wiki for developers, offline documentation, AI conversation saver, code version tracking, developer note taking app, programming knowledge management, developer second brain, block-based documentation">
    <meta name="author" content="Devlog Team">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.devlog.design/">
    
    <!-- Google Site Verification -->
    <meta name="google-site-verification" content="google75c2ec069aed359d" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="DevLog - Developer Knowledge Base That Remembers Why Your Code Works">
    <meta property="og:description" content="The only developer documentation tool with AI conversation preservation, code version tracking, and offline-first architecture. Build your second brain for coding.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.devlog.design/">
    <meta property="og:image" content="https://www.devlog.design/icon-512.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="DevLog">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@devlogapp">
    <meta name="twitter:title" content="DevLog - Developer Knowledge Base & Code Documentation">
    <meta name="twitter:description" content="Capture, organize, and retrieve your coding solutions with AI conversation preservation and offline-first architecture. Start free.">
    <meta name="twitter:image" content="https://www.devlog.design/icon-512.png">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#10b981">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts for enhanced typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Enhanced Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "DevLog",
      "description": "Developer knowledge base and documentation tool with AI conversation preservation, code version tracking, and offline-first architecture",
      "url": "https://www.devlog.design",
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Web Browser",
      "browserRequirements": "Requires JavaScript enabled",
      "softwareVersion": "2.0",
      "offers": {
        "@type": "AggregateOffer",
        "priceCurrency": "USD",
        "highPrice": "19",
        "lowPrice": "0",
        "offerCount": "3",
        "offers": [
          {
            "@type": "Offer",
            "name": "Starter",
            "price": "0",
            "priceCurrency": "USD",
            "description": "10 documents, Basic features"
          },
          {
            "@type": "Offer",
            "name": "Pro",
            "price": "9",
            "priceCurrency": "USD",
            "description": "Unlimited documents, All features"
          },
          {
            "@type": "Offer",
            "name": "Team",
            "price": "19",
            "priceCurrency": "USD",
            "description": "Everything in Pro + Collaboration"
          }
        ]
      },
      "creator": {
        "@type": "Organization",
        "name": "DevLog Team",
        "url": "https://www.devlog.design"
      },
      "featureList": [
        "AI Conversation Preservation",
        "Code Version Tracking",
        "Offline-First Architecture",
        "Block-Based Editor",
        "Wiki-Style Linking",
        "Personal Knowledge Graphs",
        "Syntax Highlighting",
        "Markdown Support",
        "Tag Organization",
        "Lightning-Fast Search"
      ],
      "screenshot": "https://www.devlog.design/icon-512.png",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "127"
      }
    }
    </script>
    
    <!-- FAQPage Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is a developer knowledge base?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "A developer knowledge base is a personal documentation system that helps programmers capture, organize, and retrieve their coding solutions, learning notes, and technical knowledge. DevLog specializes in this by offering features like code version tracking and AI conversation preservation."
          }
        },
        {
          "@type": "Question",
          "name": "How is DevLog different from Notion or Obsidian?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "DevLog is built specifically for developers with features like syntax highlighting, code version tracking, AI conversation blocks, and offline-first architecture. Unlike general-purpose tools, DevLog understands developer workflows and provides specialized features for code documentation."
          }
        },
        {
          "@type": "Question",
          "name": "Can I use DevLog offline?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes! DevLog features offline-first architecture with IndexedDB storage, allowing you to access and edit your documentation without internet connection. Changes sync automatically when you're back online."
          }
        },
        {
          "@type": "Question",
          "name": "What is AI conversation preservation?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "AI conversation preservation allows you to save and organize your ChatGPT, Claude, or other AI tool conversations directly in your documentation. This unique feature helps you retain valuable AI-generated solutions and explanations alongside your code."
          }
        }
      ]
    }
    </script>
    
    <!-- Organization Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "DevLog",
      "url": "https://www.devlog.design",
      "logo": "https://www.devlog.design/icon-512.png",
      "sameAs": [
        "https://github.com/devlog-app",
        "https://twitter.com/devlogapp"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer support"
      }
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <div id="portal-root"></div>
    <!-- GSAP for Quantum Documentation Field Animation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/CustomEase.min.js"></script>
    <script src="/manifest-loader.js"></script>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
