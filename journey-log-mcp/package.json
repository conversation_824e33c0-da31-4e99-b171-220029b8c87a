{"name": "@journey-log/mcp-server", "version": "1.0.0", "description": "MCP server for Journey Log - Document your AI-assisted coding journeys", "type": "module", "main": "src/server.js", "bin": {"journey-log-mcp": "./src/server.js"}, "scripts": {"start": "node src/server.js", "test": "node --test"}, "keywords": ["mcp", "model-context-protocol", "journey-log", "ai", "documentation", "claude", "cursor", "devlog"], "author": "Journey Log Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/journey-log/mcp-server.git"}, "homepage": "https://devlog.design", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["src/**/*", "README.md", "LICENSE"]}