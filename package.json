{"name": "journey-log-compass", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@gitgraph/react": "^1.6.0", "@honeycombio/opentelemetry-web": "^0.20.0", "@opentelemetry/auto-instrumentations-web": "~0.48.0", "@sentry/react": "^9.40.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.46.2", "framer-motion": "^12.23.6", "gsap": "^3.12.5", "isomorphic-dompurify": "^2.9.0", "katex": "^0.16.9", "lucide-react": "^0.513.0", "lz-string": "^1.5.0", "prism-react-renderer": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-katex": "^3.0.1", "react-router-dom": "^6.28.2", "zustand": "^4.5.0", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@sentry/vite-plugin": "^3.5.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "lightningcss": "^1.24.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}