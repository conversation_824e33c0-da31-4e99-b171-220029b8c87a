# Form Handling Guide - Phase 2 Implementation

This guide documents the new form handling system using React Hook Form + Zod validation implemented in Phase 2 of the debugging toolkit.

## Overview

The new form system provides:
- 🎯 **Type-safe validation** with Zod schemas
- 🔄 **Auto-save functionality** for better UX
- 🧩 **Reusable components** following DRY principles
- ⚡ **Performance optimized** with minimal re-renders
- ♿ **Accessible by default** with ARIA attributes

## Core Technologies

- **React Hook Form** - Declarative form state management
- **Zod** - Runtime type validation and schema definition
- **Custom hooks** - Reusable form logic

## Quick Start

### 1. Basic Form Example

```jsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormInput, FormTextarea, FormSelect } from '@/components/forms';
import { documentSchema } from '@/utils/forms/validationSchemas';

function DocumentForm() {
  const { 
    register, 
    handleSubmit, 
    formState: { errors, isSubmitting } 
  } = useForm({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      title: '',
      tags: [],
      folder_id: null
    }
  });

  const onSubmit = async (data) => {
    // Save to Supabase
    await saveDocument(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormInput
        {...register('title')}
        label="Document Title"
        error={errors.title}
        required
      />
      
      <FormTextarea
        {...register('description')}
        label="Description"
        error={errors.description}
        rows={4}
        autoResize
      />
      
      <button type="submit" disabled={isSubmitting}>
        Save Document
      </button>
    </form>
  );
}
```

### 2. Form with Auto-Save

```jsx
import { useFormAutoSave } from '@/hooks/useFormAutoSave';

function AutoSaveForm() {
  const form = useForm({
    resolver: zodResolver(profileSchema)
  });

  const { saving, lastSaved } = useFormAutoSave({
    form,
    onSave: async (data) => {
      await updateProfile(data);
    },
    delay: 3000 // 3 second debounce
  });

  return (
    <form>
      <FormInput {...form.register('username')} />
      <AutoSaveIndicator saving={saving} lastSaved={lastSaved} />
    </form>
  );
}
```

## Component Reference

### FormInput

Basic input field with validation support.

```jsx
<FormInput
  {...register('email')}
  label="Email Address"
  type="email"
  placeholder="<EMAIL>"
  error={errors.email}
  required
  autoComplete="email"
/>
```

**Props:**
- `label` - Field label text
- `type` - Input type (text, email, password, etc.)
- `error` - Error object from React Hook Form
- `required` - Shows required indicator
- All standard input attributes

### FormTextarea

Multi-line text input with auto-resize option.

```jsx
<FormTextarea
  {...register('content')}
  label="Content"
  rows={4}
  autoResize
  maxLength={1000}
  error={errors.content}
/>
```

**Props:**
- `rows` - Initial number of rows
- `autoResize` - Enable automatic height adjustment
- `maxLength` - Character limit with counter

### FormSelect

Dropdown selection field.

```jsx
<FormSelect
  {...register('category')}
  label="Category"
  options={[
    { value: 'bug', label: 'Bug Report' },
    { value: 'feature', label: 'Feature Request' }
  ]}
  error={errors.category}
/>
```

**Props:**
- `options` - Array of {value, label} objects
- `placeholder` - Default option text

### FormCheckbox

Single checkbox or checkbox groups.

```jsx
// Single checkbox
<FormCheckbox
  {...register('terms')}
  label="I agree to the terms"
  error={errors.terms}
/>

// Checkbox group
<FormCheckboxGroup
  name="permissions"
  label="Permissions"
  options={[
    { value: 'read', label: 'Read' },
    { value: 'write', label: 'Write' }
  ]}
  register={register}
  errors={errors}
/>
```

## Validation Schemas

Pre-built Zod schemas in `/utils/forms/validationSchemas.js`:

```js
// Authentication
export const signUpSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Documents
export const documentSchema = z.object({
  title: documentTitleSchema,
  tags: z.array(documentTagSchema).max(20),
  folder_id: z.string().uuid().nullable()
});

// Custom validation
const customSchema = z.object({
  age: z.number().min(18, 'Must be 18 or older'),
  website: z.string().url().optional()
});
```

## Hooks

### useFormAutoSave

Auto-saves form data with debouncing.

```jsx
const { saving, lastSaved, error, save } = useFormAutoSave({
  form,           // React Hook Form instance
  onSave,         // Save function
  delay: 3000,    // Debounce delay
  enabled: true,  // Enable/disable auto-save
  onSuccess,      // Success callback
  onError         // Error callback
});
```

### useDebounce

Debounces values or callbacks.

```jsx
// Debounce a value
const debouncedSearch = useDebounce(searchTerm, 500);

// Debounce a callback
const debouncedSave = useDebouncedCallback(
  (data) => saveData(data),
  1000
);
```

## Migration Guide

### Before (Old Pattern)

```jsx
function OldForm() {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  
  const validateEmail = (value) => {
    if (!value) {
      setEmailError('Email is required');
    } else if (!/\S+@\S+\.\S+/.test(value)) {
      setEmailError('Invalid email');
    } else {
      setEmailError('');
    }
  };
  
  return (
    <input
      value={email}
      onChange={(e) => {
        setEmail(e.target.value);
        validateEmail(e.target.value);
      }}
    />
  );
}
```

### After (New Pattern)

```jsx
function NewForm() {
  const { register, formState: { errors } } = useForm({
    resolver: zodResolver(signInSchema)
  });
  
  return (
    <FormInput
      {...register('email')}
      label="Email"
      error={errors.email}
    />
  );
}
```

## Best Practices

1. **Define schemas once, reuse everywhere**
   ```js
   // validationSchemas.js
   export const emailSchema = z.string().email();
   
   // Use in multiple forms
   const loginSchema = z.object({ email: emailSchema });
   const profileSchema = z.object({ email: emailSchema });
   ```

2. **Handle loading states properly**
   ```jsx
   const { isSubmitting } = formState;
   
   <button disabled={isSubmitting}>
     {isSubmitting ? 'Saving...' : 'Save'}
   </button>
   ```

3. **Show validation feedback immediately**
   ```jsx
   useForm({
     mode: 'onChange', // Validate on change
     reValidateMode: 'onChange'
   })
   ```

4. **Use auto-save for long forms**
   ```jsx
   // Good for: profiles, settings, drafts
   useFormAutoSave({ form, onSave, delay: 5000 });
   ```

5. **Provide helpful error messages**
   ```js
   z.string()
     .min(8, 'Password must be at least 8 characters')
     .regex(/[A-Z]/, 'Must contain uppercase letter')
   ```

## Testing

Example test for a form component:

```jsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

test('validates email format', async () => {
  render(<SignUpForm />);
  
  const emailInput = screen.getByLabelText('Email');
  await userEvent.type(emailInput, 'invalid-email');
  
  await waitFor(() => {
    expect(screen.getByText('Please enter a valid email')).toBeInTheDocument();
  });
});
```

## Troubleshooting

### Form not validating?
- Check that resolver is passed to useForm
- Ensure field names match schema keys
- Verify register() is spread correctly

### Auto-save not working?
- Check that form.watch() is available
- Ensure onSave is async
- Verify enabled prop is true

### Validation too aggressive?
- Change mode to 'onBlur' or 'onSubmit'
- Use reValidateMode: 'onSubmit'

## Next Steps

1. **Implement in all forms** - Migrate remaining forms to new pattern
2. **Add file upload** - Create FormFileUpload component
3. **Rich text editor** - Integrate with markdown editor
4. **Form arrays** - Handle dynamic field lists
5. **Conditional fields** - Show/hide based on other values

## Summary

The new form system provides a solid foundation for handling user input with:
- ✅ Consistent validation
- ✅ Better user experience
- ✅ Less boilerplate code
- ✅ Type safety
- ✅ Accessibility built-in

This completes Phase 2 of the debugging toolkit implementation!