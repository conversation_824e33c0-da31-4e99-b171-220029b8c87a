# Fixed Cloudflare Deployment Guide for Devlog MCP (2025)

This guide fixes the "No Durable Object found" error and includes all 2025 updates.

## 🚨 Critical Fix Summary

The main issue was using `new_classes` instead of `new_sqlite_classes` in the migrations. This has been fixed in the code.

## Prerequisites

1. You're logged into Cloudflare dashboard
2. You have Node.js 18+ installed
3. You have access to the terminal

## Step 1: Get KV Namespace ID from Dashboard

1. Go to **Workers & Pages** → **KV**
2. If you haven't created one yet:
   - Click **Create namespace**
   - Name: `devlog-mcp-cache`
   - Click **Add**
3. **Copy the ID** (looks like: `a1b2c3d4e5f6g7h8`)

## Step 2: Update Configuration

1. Open `/home/<USER>/devlog-/devlog-mcp-remote/wrangler.toml`
2. Replace `your-kv-namespace-id` with the actual ID from Step 1:
   ```toml
   [[kv_namespaces]]
   binding = "CACHE"
   id = "YOUR_ACTUAL_ID_HERE"  # Replace this
   ```

## Step 3: Install Dependencies

```bash
cd /home/<USER>/devlog-/devlog-mcp-remote
npm install
```

## Step 4: Generate TypeScript Types

```bash
npx wrangler types
```

This creates `worker-configuration.d.ts` with proper types for your Worker.

## Step 5: Deploy the Worker

```bash
# Deploy to production
npx wrangler deploy

# Or deploy to staging first
npx wrangler deploy --env staging
```

**What happens during deployment:**
1. Wrangler uploads your code
2. Creates the MCPSession Durable Object class
3. Runs the SQLite migration
4. Sets up all bindings automatically

## Step 6: Add Secret (Supabase Key)

After successful deployment:

```bash
npx wrangler secret put SUPABASE_ANON_KEY
```

When prompted, paste your Supabase anon key (already in `.dev.vars`).

## Step 7: Verify Deployment

1. **Check Worker URL**:
   ```bash
   # Your Worker URL will be shown after deployment
   # Format: https://devlog-mcp.YOUR-SUBDOMAIN.workers.dev
   ```

2. **Test endpoints**:
   ```bash
   # Health check
   curl https://devlog-mcp.YOUR-SUBDOMAIN.workers.dev/health

   # API info
   curl https://devlog-mcp.YOUR-SUBDOMAIN.workers.dev/api/info
   ```

3. **Monitor logs**:
   ```bash
   npx wrangler tail
   ```

## Step 8: Verify Durable Objects in Dashboard

1. Go to your Worker in Cloudflare dashboard
2. Click **Settings** → **Bindings**
3. You should now see:
   - KV namespace binding: `CACHE`
   - Durable Object binding: `SESSION` → `MCPSession`

## 🎉 Success!

Your MCP server is now deployed with:
- ✅ SQLite-backed Durable Objects (2025 pattern)
- ✅ 60-second heartbeats for SSE connections
- ✅ Proper TypeScript types
- ✅ All 11 Devlog block types supported
- ✅ Semantic snapshots for 90% data reduction

## Troubleshooting

### Still getting "No Durable Object found"?
- Make sure you deployed with `wrangler deploy` first
- Check that migration uses `new_sqlite_classes` not `new_classes`
- Verify the Durable Object class is exported in `src/index.ts`

### TypeScript errors?
- Run `npx wrangler types` to regenerate types
- Make sure `tsconfig.json` references `./worker-configuration.d.ts`

### Connection timeouts?
- The 60-second heartbeat prevents Cloudflare's 100-second timeout
- Check logs with `npx wrangler tail` for connection issues

## Next Steps

1. **Test with MCP client**: Update client to point to your Worker URL
2. **Add custom domain**: Configure `mcp.devlog.design` in dashboard
3. **Monitor usage**: Check Analytics tab for request metrics
4. **Publish NPX package**: Follow the npm publishing guide

## Local Development

```bash
# Start local dev server
npm run dev

# This runs at http://localhost:8787
# Uses .dev.vars for secrets
```

## Production Checklist

- [ ] KV namespace ID updated in wrangler.toml
- [ ] TypeScript types generated
- [ ] Worker deployed successfully
- [ ] SUPABASE_ANON_KEY secret added
- [ ] Health endpoint returns "OK"
- [ ] API info endpoint returns JSON
- [ ] Durable Objects visible in dashboard
- [ ] SSE connections stay alive (test with client)