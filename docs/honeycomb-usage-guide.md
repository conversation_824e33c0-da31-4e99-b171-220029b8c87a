# Honeycomb.io Usage Guide - How to Use the Platform

## Getting Started with Honey<PERSON> UI

### 1. First Login
When you log into Honeycomb (https://ui.honeycomb.io/), you'll see:
- **Home**: Overview of your environments and recent activity
- **Query**: Where you search and analyze your data
- **Boards**: Dashboards for visualization
- **Triggers**: Alerts and notifications
- **SLOs**: Service Level Objectives

### 2. Understanding the Query Builder

The Query Builder is where you'll spend most of your time. Here's how to use it:

#### Basic Query Structure
```
WHERE: [filters]
VISUALIZE: [what to show]
GROUP BY: [how to break down]
ORDER BY: [sorting]
LIMIT: [number of results]
```

#### Example Queries for Common Issues

**Find Your Slowest Operations:**
1. Go to Query tab
2. Set WHERE: `service_name = "devlog"`
3. VISUALIZE: `P95(duration_ms)`
4. GROUP BY: `name`
5. ORDER BY: `P95(duration_ms) desc`
6. Click "Run Query"

**Find Error Patterns:**
1. WHERE: `service_name = "devlog" error = true`
2. VISUALIZE: `COUNT`
3. GROUP BY: `error.message`
4. Time range: Last 1 hour

**Track Specific User Issues:**
1. WHERE: `user.id = "user-id-here"`
2. VISUALIZE: `HEATMAP(duration_ms)`
3. GROUP BY: `name`

### 3. Using BubbleUp (Honeycomb's Killer Feature)

BubbleUp automatically finds what's different about slow or error requests:

1. Run any query
2. Click and drag to select the "bad" area (slow requests, errors)
3. Click "BubbleUp" button
4. Honeycomb shows what attributes are different in that selection

Example: Your saves are slow
- Query: `name = "supabase.saveDocument"`
- Select the slow saves (>1000ms)
- BubbleUp shows: "90% have block.count > 50"
- Now you know large documents are the issue!

### 4. Trace View - Understanding Request Flow

Click any bar/dot in a query result to see the trace:

```
[Trace View]
├── browser.page_load (2000ms)
│   ├── fetch: GET /api/documents (500ms)
│   ├── supabase.getDocuments (480ms)
│   │   └── database.query (450ms)
│   └── react.render (800ms)
│       ├── block.render (50ms)
│       ├── block.render (120ms) ← Slow!
│       └── block.render (30ms)
```

**How to Read Traces:**
- **Waterfall view**: Shows timing relationships
- **Span details**: Click any span for attributes
- **Critical path**: Highlighted automatically
- **Errors**: Shown in red

### 5. Creating Dashboards (Boards)

#### Step 1: Create a New Board
1. Go to Boards → New Board
2. Name it (e.g., "User Experience Dashboard")

#### Step 2: Add Queries
Click "Add Query" and create tiles:

**Tile 1: Error Rate**
```
WHERE: service_name = "devlog"
VISUALIZE: COUNT
GROUP BY: error = true
Display as: Line graph
```

**Tile 2: P95 Latency**
```
WHERE: service_name = "devlog"
VISUALIZE: P95(duration_ms)
Display as: Line graph
```

**Tile 3: Top Slow Operations**
```
WHERE: service_name = "devlog" duration_ms > 500
VISUALIZE: COUNT
GROUP BY: name
Display as: Bar chart
```

### 6. Setting Up Alerts (Triggers)

#### Create Your First Alert:
1. Go to Triggers → New Trigger
2. Name: "High Error Rate"
3. Query:
   ```
   WHERE: service_name = "devlog" error = true
   QUERY: COUNT > 100
   WINDOW: 5 minutes
   ```
4. Notification: Email/Slack/PagerDuty
5. Click "Create Trigger"

#### Recommended Alerts for Devlog:

**1. Slow Save Operations**
```
Query: name = "supabase.saveDocument" P95(duration_ms) > 2000
Window: 5 minutes
Alert: When threshold exceeded
```

**2. Memory Issues**
```
Query: name = "browser.memory_check" memory.usage_percentage > 80
Window: 1 minute
Alert: Immediately
```

**3. JavaScript Errors Spike**
```
Query: error = true COUNT
Window: 5 minutes
Threshold: > 50 errors
```

### 7. Using the Data Explorer

#### Finding Specific Errors:
1. Use the time picker to select when error occurred
2. Add filters:
   - `error.message contains "Failed to execute"`
   - `user.id = "affected-user"`
3. Click on result to see full trace
4. Look at "Preceding spans" to see what user did before error

#### Performance Investigation:
1. Find slow operation in query
2. Click "Compare" to see fast vs slow
3. BubbleUp shows differences
4. Create hypothesis and test

### 8. Keyboard Shortcuts

- `⌘ + K`: Quick search
- `⌘ + Enter`: Run query
- `⌘ + Shift + Enter`: Run and keep history
- `Escape`: Clear selection
- `?`: Show all shortcuts

### 9. Query Examples for Your Specific Issues

#### "Failed to execute 'contains' on 'Node'" Error:
```
WHERE: 
  service_name = "devlog"
  error.message contains "contains"
VISUALIZE: COUNT
GROUP BY: error.stack
```
Then click on results to see exact user actions.

#### Find Memory Leaks:
```
WHERE:
  service_name = "devlog"
  name = "browser.memory_check"
VISUALIZE: MAX(memory.used_js_heap_size)
GROUP BY: BUCKET_MINUTES(5)
```
Look for steadily increasing memory over time.

#### Slow Block Renders:
```
WHERE:
  service_name = "devlog"
  name = "block.render.slow"
VISUALIZE: HEATMAP(render.duration_ms)
GROUP BY: block.type
```

### 10. Team Collaboration Features

#### Sharing Queries:
1. Run your query
2. Click "Share" button
3. Options:
   - Copy link (shares exact query)
   - Save to board (permanent)
   - Export to CSV

#### Annotations:
1. In any graph, right-click
2. Select "Add Marker"
3. Add note (e.g., "Deployed fix here")
4. Team sees marker on all graphs

#### Query History:
- Click clock icon to see all past queries
- Star queries to save as favorites
- See who ran what query when

### 11. Advanced Features

#### Derived Columns:
Create calculated fields:
```
DERIVE duration_seconds = DIV(duration_ms, 1000)
DERIVE is_slow = GT(duration_ms, 1000)
DERIVE error_rate = DIV(error_count, total_count)
```

#### SLOs (Service Level Objectives):
1. Go to SLOs → New SLO
2. Define "good" events: `duration_ms < 500 error = false`
3. Set target: 99.9%
4. Time window: 30 days
5. Get error budget tracking

#### Correlations:
Find what impacts performance:
1. Run base query
2. Click "Correlate"
3. Honeycomb finds what attributes correlate with slow/fast

### 12. Mobile App Usage

Honeycomb has iOS/Android apps for on-the-go monitoring:
1. Download from App Store/Play Store
2. Login with same credentials
3. Features:
   - View alerts
   - Run saved queries
   - See recent traces
   - Get push notifications

### 13. API and CLI Usage

#### Query via API:
```bash
curl https://api.honeycomb.io/1/queries/devlog \
  -H "X-Honeycomb-Team: YOUR_API_KEY" \
  -d '{
    "calculations": [{"op": "P95", "column": "duration_ms"}],
    "filters": [{"column": "service_name", "op": "=", "value": "devlog"}],
    "time_range": 3600
  }'
```

#### CLI Tool:
```bash
# Install
brew install honeycombio/tap/honeyctl

# Configure
export HONEYCOMB_API_KEY=your_key

# Query
honeyctl query create \
  --dataset devlog \
  --calculation "P95(duration_ms)" \
  --filter "error=true"
```

### 14. Cost Management

#### Monitor Your Usage:
1. Go to Team Settings → Usage
2. See events per day
3. Set up usage alerts
4. Optimize sampling if needed

#### Reduce Costs:
- Increase sampling rate (1:10 instead of 1:1)
- Filter out noise before sending
- Use shorter retention
- Archive old data

### 15. Common Workflows

#### Daily Standup Review:
1. Check error rate dashboard
2. Look at P95 latency trend
3. Review any triggered alerts
4. Check SLO burn rate

#### Investigating User Complaint:
1. Get user ID or time of issue
2. Query: `user.id = "xyz" time range around incident`
3. Find error or slow trace
4. Use trace view to understand issue
5. BubbleUp to find pattern

#### Post-Deploy Verification:
1. Add deployment marker
2. Compare before/after metrics
3. Watch error rate for 30 minutes
4. Check P95 latency didn't increase

### 16. Tips and Tricks

1. **Use Raw Data Mode**: Toggle to see actual data points
2. **Pin Time Range**: Lock time when comparing
3. **Use Templates**: Save common query patterns
4. **Keyboard Navigation**: Much faster than clicking
5. **Multi-Select**: Cmd+Click to compare multiple traces
6. **Quick Filters**: Click any attribute to filter by it
7. **Zoom**: Drag to zoom into time ranges
8. **Compare Periods**: Use "Compare to" for before/after
9. **Export**: Download data for external analysis
10. **Dark Mode**: Settings → Preferences → Theme

---

## Quick Reference Card

### Essential Queries to Bookmark

```sql
-- Overall Health
service_name = "devlog"
VISUALIZE: COUNT, P95(duration_ms), ERROR_RATE

-- Slow Operations
duration_ms > 1000
GROUP BY: name
ORDER BY: COUNT desc

-- Error Details
error = true
GROUP BY: error.message, error.stack

-- User Experience
name = "webvitals"
VISUALIZE: P95(lcp), P95(fid), P95(cls)

-- Memory Usage
name = "browser.memory_check"
VISUALIZE: MAX(memory.usage_percentage)
```

### When to Use Each View

- **Query**: Investigating specific issues
- **Boards**: Monitoring overall health
- **Traces**: Understanding single request
- **BubbleUp**: Finding patterns in problems
- **Triggers**: Getting alerted to issues
- **SLOs**: Tracking reliability goals

---

*Remember: Honeycomb is about asking questions of your data. Don't just monitor - investigate!*