index-BUOgG6nL.js:26 Using optimized Supabase client
index-BUOgG6nL.js:26 Global auto-save manager initialized with defensive wrappers
index-BUOgG6nL.js:26 IndexedDB initialized successfully
index-BUOgG6nL.js:26 Starting auto-save with interval: 1 seconds
index-BUOgG6nL.js:26 SW registered: ServiceWorkerRegistration
index-BUOgG6nL.js:26 [Supabase] Restored existing session: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 [Supabase] Auth event: INITIAL_SESSION
index-BUOgG6nL.js:26 [AuthContext] Auth state change received: INITIAL_SESSION Object
index-BUOgG6nL.js:26 Dashboard: Starting to load entries...
index-BUOgG6nL.js:26 Using Supabase for storage
index-BUOgG6nL.js:26 SupabaseAdapter: Init with provided userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 Dashboard: Storage initialized (46ms)
index-BUOgG6nL.js:26 SupabaseAdapter: getDocuments called
index-BUOgG6nL.js:26 SupabaseAdapter: Querying documents for user 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 SupabaseAdapter: Documents query completed in 347ms
index-BUOgG6nL.js:26 SupabaseAdapter: Found 76 documents
index-BUOgG6nL.js:26 SupabaseAdapter: Found 0 unsynced documents in IndexedDB
index-BUOgG6nL.js:26 SupabaseAdapter: Total documents after merge: 76
index-BUOgG6nL.js:26 SupabaseAdapter: Returning 76 documents
index-BUOgG6nL.js:26 Dashboard: Loaded 76 entries (371ms)
index-BUOgG6nL.js:26 Dashboard: Total load time: 420ms
index-BUOgG6nL.js:26 SupabaseAdapter: Getting projects...
index-BUOgG6nL.js:26 SupabaseAdapter: Found 3 projects
index-BUOgG6nL.js:26 Dashboard: Loaded 3 projects
index-BUOgG6nL.js:26 Dashboard: Setting isLoading to false
index-BUOgG6nL.js:26 Loading folders for user: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 VirtualizedGrid - scroll container: {"totalHeight":10020,"containerHeight":787,"hasOverflow":true,"parentIsCardsContainer":true}
index-BUOgG6nL.js:26 VirtualizedGrid - scroll container: {"totalHeight":3328,"containerHeight":787,"hasOverflow":true,"parentIsCardsContainer":true}
index-BUOgG6nL.js:26 Loaded 42 folders (19 root folders)
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 PaginatedBlockLoader: Loading page 0 (offset: 0, limit: 50) for document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 PaginatedBlockLoader: Loaded 5 blocks for page 0 of document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 ExpandedView: Initial load period complete, enabling saves
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 Loading folders for user: 8eac28e6-0127-40d1-ba55-c10cbe52a32b
index-BUOgG6nL.js:26 VirtualizedGrid - scroll container: {"totalHeight":10020,"containerHeight":787,"hasOverflow":true,"parentIsCardsContainer":true}
index-BUOgG6nL.js:26 VirtualizedGrid - scroll container: {"totalHeight":2800,"containerHeight":787,"hasOverflow":true,"parentIsCardsContainer":true}
index-BUOgG6nL.js:26 Loaded 42 folders (19 root folders)
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 PaginatedBlockLoader: Loading page 0 (offset: 0, limit: 50) for document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 PaginatedBlockLoader: Loaded 5 blocks for page 0 of document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 ExpandedView: Initial load period complete, enabling saves
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 SessionCache: Cached 6 blocks for document 5227b0da-8532-4307-a84a-3c01bab5e09a
index-BUOgG6nL.js:26 SupabaseAdapter: saveDocument called Object
index-BUOgG6nL.js:26 SupabaseAdapter: Using userId 8eac28e6-0127-40d1-ba55-c10cbe52a32b for save
index-BUOgG6nL.js:26 SupabaseAdapter: Saving document to Supabase: Object
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): Object
index-BUOgG6nL.js:26 SupabaseAdapter: Document saved successfully: Object
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): Object
index-BUOgG6nL.js:26 Dashboard: Showing ExpandedView instead of grid
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: Object
(anonymous) @ index-BUOgG6nL.js:26
index-BUOgG6nL.js:974 Uncaught TypeError: Failed to execute 'contains' on 'Node': parameter 1 is not of type 'Node'.
    at onMouseLeave (index-BUOgG6nL.js:974:2321)
    at g_ (index-BUOgG6nL.js:106:118054)
    at index-BUOgG6nL.js:106:123334
    at Ew (index-BUOgG6nL.js:106:9040)
    at py (index-BUOgG6nL.js:106:119290)
    at Ty (index-BUOgG6nL.js:107:26824)
    at oI (index-BUOgG6nL.js:107:26739)
    at HTMLDivElement.r (index-BUOgG6nL.js:26:9336)
onMouseLeave @ index-BUOgG6nL.js:974
g_ @ index-BUOgG6nL.js:106
(anonymous) @ index-BUOgG6nL.js:106
Ew @ index-BUOgG6nL.js:106
py @ index-BUOgG6nL.js:106
Ty @ index-BUOgG6nL.js:107
oI @ index-BUOgG6nL.js:107
r @ index-BUOgG6nL.js:26
index-BUOgG6nL.js:26 🔵 AI Block Load Debug (OptimizedBlockLoader): {blockId: '1d63b786-6591-415b-a14d-0d1a5a713ec3', metadataKeys: Array(1), hasMessagesInMetadata: true, messageCount: 2, hasMessagesInBaseBlock: true}
index-BUOgG6nL.js:26 updateBlock called with invalid parameters: {blockId: {…}, updates: undefined}
(anonymous) @ index-BUOgG6nL.js:26
_r @ index-BUOgG6nL.js:1080
(anonymous) @ index-BUOgG6nL.js:935
r @ index-BUOgG6nL.js:26
