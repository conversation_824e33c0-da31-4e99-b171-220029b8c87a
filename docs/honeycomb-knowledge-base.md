# Honeycomb.io Knowledge Base (August 2025)

## Latest Package Information (as of August 13, 2025)

### NPM Package
- **Package**: `@honeycombio/opentelemetry-web`
- **Latest Version**: 0.20.0 (Published August 4, 2025)
- **Status**: BETA (Data shapes stable for production)
- **Built with**: 
  - OpenTelemetry JS Stable v2.0.0
  - Experimental v0.202.0
  - API v1.9.0
  - Semantic Conventions v1.34.0
- **Compatible with**: OpenTelemetry Auto-Instrumentations for Web ~0.48.0

### Installation
```bash
npm install @honeycombio/opentelemetry-web @opentelemetry/auto-instrumentations-web
```

## Core Concepts

### What is Honeycomb?
Honeycomb.io is an observability platform that helps you understand what's happening in your production systems. Unlike traditional monitoring that tells you **what** is broken, <PERSON><PERSON> tells you **why** it's broken.

### Key Differentiators
1. **No Rate Limits** - Unlike Sentry which has 429 errors
2. **Full Trace Context** - See the complete journey of every request
3. **High Cardinality** - Query on any attribute without pre-indexing
4. **BubbleUp** - Automatically finds what's different about slow/error requests

## Implementation for React/Vite (2025)

### Basic Configuration
```javascript
import { HoneycombWebSDK, WebVitalsInstrumentation } from '@honeycombio/opentelemetry-web';
import { getWebAutoInstrumentations } from '@opentelemetry/auto-instrumentations-web';

const sdk = new HoneycombWebSDK({
  apiKey: 'your-api-key',
  serviceName: 'your-app-name',
  instrumentations: [
    getWebAutoInstrumentations(),
    new WebVitalsInstrumentation()
  ],
});
sdk.start();
```

### React-Specific Considerations

#### 1. Client-Side Only Execution
For Next.js or SSR apps, add the `'use client';` directive:
```javascript
'use client'; // Important for Next.js

// Honeycomb initialization code
```

#### 2. Handle Server-Side Rendering
```javascript
const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';

if (isBrowser) {
  // Initialize Honeycomb only in browser
  const sdk = new HoneycombWebSDK({...});
  sdk.start();
}
```

#### 3. Initialize Before React
In your `main.jsx` or `index.js`:
```javascript
// Initialize Honeycomb BEFORE React
import { initializeHoneycomb } from './utils/honeycomb';
const honeycombSDK = initializeHoneycomb();

// Then render React
const root = createRoot(document.getElementById('root'));
root.render(<App />);
```

## What Gets Tracked Automatically

### Auto-Instrumentations Include:
1. **Document Load**
   - Page load timing
   - Resource fetch timing
   - First paint metrics

2. **Fetch/XHR Instrumentation**
   - All HTTP requests
   - Request/response timing
   - Status codes
   - Request/response sizes

3. **User Interactions**
   - Click events with element context
   - Form submissions
   - Custom event tracking

4. **Core Web Vitals**
   - Largest Contentful Paint (LCP)
   - First Input Delay (FID)
   - Cumulative Layout Shift (CLS)
   - First Contentful Paint (FCP)
   - Time to First Byte (TTFB)
   - Interaction to Next Paint (INP)

## Configuration Options

### Development vs Production
```javascript
const sdk = new HoneycombWebSDK({
  apiKey: process.env.VITE_HONEYCOMB_API_KEY,
  serviceName: isProduction ? 'app-prod' : 'app-dev',
  
  // Sample rate: 100% in dev, 10% in production
  sampleRate: isDevelopment ? 1 : 0.1,
  
  // Debug logging in development
  debug: isDevelopment,
  
  // Show trace links in console (dev only)
  localVisualizations: isDevelopment,
  
  // Custom attributes for all traces
  resourceAttributes: {
    'environment': isProduction ? 'production' : 'development',
    'deployment.environment': 'vercel',
    'service.version': packageJson.version,
  },
});
```

### Advanced Instrumentation Options
```javascript
instrumentations: [
  getWebAutoInstrumentations({
    // Customize fetch instrumentation
    '@opentelemetry/instrumentation-fetch': {
      propagateTraceHeaderCorsUrls: [
        /.*supabase\.co.*/,
        /.*your-api\.com.*/,
      ],
      clearTimingResources: true,
      applyCustomAttributesOnSpan: (span, request, response) => {
        span.setAttribute('http.request.body.size', request.headers?.['content-length'] || 0);
      },
    },
    
    // Customize user interaction tracking
    '@opentelemetry/instrumentation-user-interaction': {
      eventNames: ['click', 'submit', 'change', 'dblclick'],
      shouldPreventSpanCreation: (event, element) => {
        // Skip tracking for certain elements
        return element.classList?.contains('no-track');
      },
    },
  }),
]
```

## Custom Instrumentation

### Creating Custom Spans
```javascript
export function createSpan(name, attributes = {}) {
  if (!window.__honeycombSDK?.tracer) return null;
  
  const span = window.__honeycombSDK.tracer.startSpan(name, {
    attributes: {
      'span.kind': 'internal',
      ...attributes,
    },
  });
  
  return span;
}

// Usage
const span = createSpan('database.query', {
  'query.type': 'SELECT',
  'query.table': 'users',
});
// ... perform operation
span.end();
```

### Recording Exceptions
```javascript
export function recordException(error, context = {}) {
  if (!window.__honeycombSDK) return;
  
  window.__honeycombSDK.recordException(error, {
    'error.context': JSON.stringify(context),
    'error.stack': error.stack,
    'error.name': error.name,
    'error.message': error.message,
    ...context,
  });
}
```

### Setting User Context
```javascript
export function setUserContext(userId, userEmail, userTier) {
  if (!window.__honeycombSDK) return;
  
  window.__honeycombSDK.setGlobalAttributes({
    'user.id': userId,
    'user.email': userEmail,
    'user.tier': userTier,
    'session.id': generateSessionId(),
  });
}
```

## Querying in Honeycomb UI

### Basic Query Syntax

#### Find Slow Operations
```
service_name = "your-app"
name = "database.query"
duration_ms > 500
```

#### Track Error Rate
```
service_name = "your-app"
error = true
COUNT
GROUP BY error.message
```

#### Monitor Memory Usage
```
service_name = "your-app"
name = "browser.memory_check"
HEATMAP(memory.usage_percentage)
```

### Advanced Queries

#### P95 Latency by Operation
```
service_name = "your-app"
name exists
P95(duration_ms)
GROUP BY name
```

#### User Experience Score
```
service_name = "your-app"
name = "webvitals"
AVG(lcp) as avg_lcp
AVG(fid) as avg_fid
AVG(cls) as avg_cls
```

## Troubleshooting Common Issues

### Issue: No Data in Honeycomb
**Solutions:**
1. Check API key is set correctly
2. Verify API key has "Send Events" permission
3. Check browser console for `[Honeycomb] SDK initialized successfully`
4. Ensure not blocking tracking in browser/ad-blocker
5. For Vercel: Ensure environment variable starts with `VITE_` or `NEXT_PUBLIC_`

### Issue: Missing User Context
**Solutions:**
1. Ensure `setUserContext` is called after login
2. Check that user object has expected properties
3. Verify global attributes are being set

### Issue: Too Much Data (High Costs)
**Solutions:**
1. Reduce sample rate in production
2. Filter out health check endpoints
3. Use `shouldPreventSpanCreation` to skip certain operations
4. Set shorter data retention periods

### Issue: CORS Errors
**Solutions:**
1. Honeycomb endpoints don't require CORS setup
2. For trace propagation to your API, add CORS headers
3. Use `propagateTraceHeaderCorsUrls` configuration

## Best Practices

### 1. Sampling Strategy
- Development: 100% sampling for debugging
- Staging: 50% sampling
- Production: 1-10% based on traffic volume
- Always sample errors at 100%

### 2. Attribute Naming
Follow OpenTelemetry semantic conventions:
- `http.*` for HTTP attributes
- `db.*` for database operations
- `user.*` for user context
- `app.*` for application-specific

### 3. Sensitive Data
Never send:
- Passwords
- API keys
- Personal information (SSN, credit cards)
- Session tokens

### 4. Performance Impact
- Honeycomb adds <5ms overhead
- Use sampling to reduce data volume
- Batch spans before sending
- Avoid creating spans in tight loops

## Integration with Other Tools

### Sentry (Keeping Both)
```javascript
// In error boundary
componentDidCatch(error, errorInfo) {
  // Send to Honeycomb
  recordException(error, errorInfo);
  
  // Also send to Sentry
  if (window.Sentry) {
    window.Sentry.captureException(error);
  }
}
```

### GitHub Actions
```yaml
- name: Send deployment marker to Honeycomb
  run: |
    curl -X POST https://api.honeycomb.io/1/markers/${{ secrets.HONEYCOMB_DATASET }} \
      -H "X-Honeycomb-Team: ${{ secrets.HONEYCOMB_API_KEY }}" \
      -d '{"message":"Deploy ${{ github.sha }}", "type":"deploy"}'
```

## Pricing Considerations (2025)

### Free Tier Includes:
- 20 million events/month
- 60-day retention
- Unlimited users
- All features

### When You'll Need to Pay:
- Over 20M events/month
- Need longer retention
- Need SLAs
- Want dedicated support

### Cost Optimization:
1. Use sampling (reduces events by 90%+)
2. Filter out noise (health checks, etc.)
3. Set appropriate retention
4. Use derived columns instead of sending duplicate data

## Resources & Support

### Official Documentation
- Main Docs: https://docs.honeycomb.io/
- Web SDK: https://docs.honeycomb.io/send-data/javascript-browser/honeycomb-distribution/
- API Reference: https://docs.honeycomb.io/api/

### Community
- Honeycomb Pollinators Slack: https://www.honeycomb.io/slack/
- GitHub: https://github.com/honeycombio/honeycomb-opentelemetry-web
- Stack Overflow: Tag `honeycomb-io`

### Learning Resources
- Observability Engineering Book: https://www.honeycomb.io/ebook/
- Blog: https://www.honeycomb.io/blog/
- Webinars: https://www.honeycomb.io/webinars/

## Migration from Other Tools

### From Sentry
```javascript
// Before (Sentry)
Sentry.captureException(error);
Sentry.captureMessage('Something happened');

// After (Honeycomb)
recordException(error, context);
createSpan('app.event', { message: 'Something happened' });
```

### From Google Analytics
```javascript
// Before (GA)
gtag('event', 'purchase', { value: 100 });

// After (Honeycomb)
const span = createSpan('user.purchase', { 
  'purchase.value': 100,
  'purchase.currency': 'USD'
});
span.end();
```

## Future Considerations (What's Coming)

### OpenTelemetry Roadmap
- Better React Native support
- Automatic React component instrumentation
- Session replay integration
- Real User Monitoring (RUM) enhancements

### Honeycomb Features in Development
- AI-powered anomaly detection
- Automatic SLO recommendations
- Cost analysis per trace
- Native mobile SDKs improvements

---

*Last Updated: August 13, 2025*
*Based on @honeycombio/opentelemetry-web v0.20.0*