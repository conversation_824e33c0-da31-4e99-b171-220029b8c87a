# TextBlock Migration Pattern - Block Component State Management

## Overview

This document outlines the pattern used to migrate TextBlock.jsx from 9 useState calls to 1, using the blockEditorStore. This pattern can be applied to all block components (CodeBlock, TableBlock, HeadingBlock, etc.).

## Migration Summary

**Before**: 9 useState calls
**After**: 1 useState call (content only - temporary)
**Reduction**: 89%

## Step-by-Step Migration Guide

### 1. Backup Original Component
```bash
cp src/components/blocks/ComponentName.jsx src/components/blocks/ComponentName.backup.jsx
```

### 2. Import useBlockEditor Hook
```javascript
import { useBlockEditor } from '../../hooks/useBlockEditor';
```

### 3. Replace useState Calls

**Before:**
```javascript
const [isEditing, setIsEditing] = useState(block.isNew && !block.content);
const [showToolbar, setShowToolbar] = useState(false);
const [toolbarPosition, setToolbarPosition] = useState(null);
const [selectedText, setSelectedText] = useState('');
const [isCollapsed, setIsCollapsed] = useState(block.metadata?.isCollapsed || false);
const [slashHint, setSlashHint] = useState('');
const [slashHintPosition, setSlashHintPosition] = useState(null);
```

**After:**
```javascript
const {
  isEditing,
  setIsEditing,
  showToolbar,
  toolbarPosition,
  setShowToolbar,
  selectedText,
  setSelectedText,
  isCollapsed,
  setIsCollapsed,
  slashHint,
  slashHintPosition,
  setSlashHint,
  setSlashHintPosition
} = useBlockEditor(block.id, {
  isEditing: block.isNew && !block.content,
  isCollapsed: block.metadata?.isCollapsed || false
});
```

### 4. Update State Setters

The main changes needed:

1. **Toolbar Position**: Instead of separate `setToolbarPosition`, use `setShowToolbar(visible, position)`
   ```javascript
   // Before
   setToolbarPosition({ top: 100, left: 200 });
   setShowToolbar(true);
   
   // After
   setShowToolbar(true, { top: 100, left: 200 });
   ```

2. **Clear Multiple States**: Remove redundant state clearing
   ```javascript
   // Before
   setShowToolbar(false);
   setSelectedText('');
   setToolbarPosition(null);
   
   // After
   setShowToolbar(false);
   setSelectedText('');
   ```

3. **Slash Hint Position**: Update hint and position together
   ```javascript
   // Before
   setSlashHint(hint);
   setSlashHintPosition(position);
   
   // After
   setSlashHint(hint);
   setSlashHintPosition(position);
   ```

### 5. Keep Content State Local (Temporary)

For now, keep content as local state. This will be migrated to document store later:
```javascript
const [content, setContent] = useState(block.content || '');
```

## Testing the Migration

### 1. Add Test File to main.jsx
```javascript
if (import.meta.env.DEV) {
  import('./utils/testComponentNameMigration');
}
```

### 2. Run Migration Tests
```javascript
// In browser console
window.testTextBlockMigration.runAllTests()
```

### 3. Verify useState Count
```bash
grep -n "useState" src/components/blocks/ComponentName.jsx
# Should show only 1 useState for content
```

## Common Patterns for Other Block Components

### CodeBlock Specific States
- `language` → Store in blockEditorStore
- `lineNumbers` → Store in blockEditorStore
- `fileName` → Store in blockEditorStore
- `copySuccess` → Store in blockEditorStore

### TableBlock Specific States
- `rows` → Keep local (data state)
- `columns` → Keep local (data state)
- `editingCell` → Store in blockEditorStore
- `selectedCells` → Store in blockEditorStore

### ImageBlock Specific States
- `images` → Keep local (data state)
- `selectedImage` → Store in blockEditorStore
- `isUploading` → Store in blockEditorStore
- `uploadProgress` → Store in blockEditorStore

## Benefits of This Pattern

1. **Centralized State**: All UI state in one place
2. **Redux DevTools**: Full visibility into state changes
3. **Automatic Cleanup**: State cleared when component unmounts
4. **Performance**: Selective subscriptions prevent unnecessary renders
5. **Consistency**: Same pattern across all block components

## Next Steps

1. Apply this pattern to CodeBlock (12 useState → 1)
2. Apply to TableBlock (8 useState → 2-3)
3. Apply to HeadingBlock (5 useState → 1)
4. Continue with other block components

## Example Migration Timeline

- TextBlock: 30 minutes (includes testing)
- CodeBlock: 20 minutes (similar pattern)
- TableBlock: 25 minutes (more complex state)
- HeadingBlock: 15 minutes (simpler component)

Total estimated time for all block components: 2-3 hours