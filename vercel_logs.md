[19:33:49.293] Running build in Washington, D.C., USA (East) – iad1
[19:33:49.294] Build machine configuration: 2 cores, 8 GB
[19:33:49.309] Cloning github.com/ALPHAbilal/devlog- (Branch: main, Commit: c0771e7)
[19:33:50.283] Cloning completed: 974.000ms
[19:33:50.577] Restored build cache from previous deployment (obYwnxN5J5amvmQVTkpbsX3eUfkW)
[19:33:53.865] Running "vercel build"
[19:33:54.339] Vercel CLI 44.7.3
[19:33:55.158] Installing dependencies...
[19:34:02.095] 
[19:34:02.096] up to date in 7s
[19:34:02.097] 
[19:34:02.097] 78 packages are looking for funding
[19:34:02.097]   run `npm fund` for details
[19:34:02.247] 
[19:34:02.248] > journey-log-compass@0.0.0 build
[19:34:02.248] > vite build
[19:34:02.248] 
[19:34:02.951] [36mvite v6.3.5 [32mbuilding for production...[36m[39m
[19:34:02.983] [sentry-vite-plugin] Info: Sending telemetry data on issues and performance to Sentry. To disable telemetry, set `options.telemetry` to `false`.
[19:34:03.398] transforming...
[19:34:14.837] [32m✓[39m 2898 modules transformed.
[19:34:16.348] rendering chunks...
[19:34:17.071] [33m[esbuild css minify]
[19:34:17.071] ▲ [WARNING] Expected "{" but found "," [css-syntax-error]
[19:34:17.071] 
[19:34:17.071]     <stdin>:3171:23:
[19:34:17.072]       3171 │   @keyframes code-float,
[19:34:17.072]            │                        ^
[19:34:17.072]            ╵                        {
[19:34:17.072] 
[19:34:17.072] 
[19:34:17.072] ▲ [WARNING] Expected "{" but found "," [css-syntax-error]
[19:34:17.072] 
[19:34:17.072]     <stdin>:18992:24:
[19:34:17.073]       18992 │   @keyframes orb-float-1,
[19:34:17.073]             │                         ^
[19:34:17.073]             ╵                         {
[19:34:17.073] 
[19:34:17.073] [39m
[19:34:17.613] [33m[plugin vite:reporter] 
[19:34:17.615] (!) /vercel/path0/src/utils/globalAutoSave.js is dynamically imported by /vercel/path0/src/App.jsx, /vercel/path0/src/App.jsx but also statically imported by /vercel/path0/src/hooks/useAutoSave.js, /vercel/path0/src/main.jsx, dynamic import will not move module into another chunk.
[19:34:17.616] [39m
[19:34:17.617] [33m[plugin vite:reporter] 
[19:34:17.617] (!) /vercel/path0/src/utils/storage/IndexedDBAdapter.js is dynamically imported by /vercel/path0/src/utils/storage/SupabaseAdapter.js, /vercel/path0/src/utils/storage/SupabaseAdapter.js but also statically imported by /vercel/path0/src/pages/Dashboard.jsx, /vercel/path0/src/utils/storage/storageWrapper.js, dynamic import will not move module into another chunk.
[19:34:17.618] [39m
[19:34:19.407] computing gzip size...
[19:34:19.657] [sentry-vite-plugin] Error: An error occurred. Couldn't finish all operations: Error: Command failed: /vercel/path0/node_modules/@sentry/cli-linux-x64/bin/sentry-cli releases new c0771e74cadc822474c05c32e03942731597d553
[19:34:19.659] error: API request failed
[19:34:19.659] 
[19:34:19.660] Caused by:
[19:34:19.660]     sentry reported an error: You do not have permission to perform this action. (http status: 403)
[19:34:19.660] 
[19:34:19.660] Add --log-level=[info|debug] or export SENTRY_LOG_LEVEL=[info|debug] to see more output.
[19:34:19.661] Please attach the full debug log to all bug reports.
[19:34:19.661] 
[19:34:19.661]     at genericNodeError (node:internal/errors:983:15)
[19:34:19.661]     at wrappedFn (node:internal/errors:537:14)
[19:34:19.662]     at ChildProcess.exithandler (node:child_process:417:12)
[19:34:19.662]     at ChildProcess.emit (node:events:518:28)
[19:34:19.662]     at maybeClose (node:internal/child_process:1101:16)
[19:34:19.663]     at Socket.<anonymous> (node:internal/child_process:456:11)
[19:34:19.663]     at Socket.emit (node:events:518:28)
[19:34:19.663]     at Pipe.<anonymous> (node:net:346:12) {
[19:34:19.663]   code: 1,
[19:34:19.664]   killed: false,
[19:34:19.664]   signal: null,
[19:34:19.664]   cmd: '/vercel/path0/node_modules/@sentry/cli-linux-x64/bin/sentry-cli releases new c0771e74cadc822474c05c32e03942731597d553'
[19:34:19.664] }
[19:34:19.725] [2mdist/[22m[32mindex.html                                   [39m[1m[2m    9.62 kB[22m[1m[22m[2m │ gzip:   2.80 kB[22m
[19:34:19.725] [2mdist/[22m[2massets/[22m[35mindex-d4hME_fg.css                    [39m[1m[2m  394.59 kB[22m[1m[22m[2m │ gzip:  52.16 kB[22m
[19:34:19.726] [2mdist/[22m[2massets/[22m[36musers-AVsG1297.js                     [39m[1m[2m    0.85 kB[22m[1m[22m[2m │ gzip:   0.52 kB[22m[2m │ map:     1.21 kB[22m
[19:34:19.727] [2mdist/[22m[2massets/[22m[36mzustand-uGa_8v0B.js                   [39m[1m[2m    4.13 kB[22m[1m[22m[2m │ gzip:   1.84 kB[22m[2m │ map:    14.52 kB[22m
[19:34:19.727] [2mdist/[22m[2massets/[22m[36mAIConversationSaver-CYce2iCg.js       [39m[1m[2m   10.37 kB[22m[1m[22m[2m │ gzip:   2.65 kB[22m[2m │ map:    17.94 kB[22m
[19:34:19.727] [2mdist/[22m[2massets/[22m[36mPricingSection-BQbTJD_i.js            [39m[1m[2m   10.42 kB[22m[1m[22m[2m │ gzip:   3.77 kB[22m[2m │ map:    25.55 kB[22m
[19:34:19.727] [2mdist/[22m[2massets/[22m[36mNotionAlternative-BhXmhI4u.js         [39m[1m[2m   12.19 kB[22m[1m[22m[2m │ gzip:   3.27 kB[22m[2m │ map:    23.40 kB[22m
[19:34:19.727] [2mdist/[22m[2massets/[22m[36mAIConversationManagement-BsUS1mp-.js  [39m[1m[2m   21.39 kB[22m[1m[22m[2m │ gzip:   4.76 kB[22m[2m │ map:    39.47 kB[22m
[19:34:19.727] [2mdist/[22m[2massets/[22m[36mDevLogVsNotion-DpYgyXlK.js            [39m[1m[2m   26.16 kB[22m[1m[22m[2m │ gzip:   5.10 kB[22m[2m │ map:    51.93 kB[22m
[19:34:19.728] [2mdist/[22m[2massets/[22m[36mapi-BS4DfZQt.js                       [39m[1m[2m   26.17 kB[22m[1m[22m[2m │ gzip:   5.89 kB[22m[2m │ map:    48.95 kB[22m
[19:34:19.728] [2mdist/[22m[2massets/[22m[36mvendor-CtMG6Qqv.js                    [39m[1m[2m   35.83 kB[22m[1m[22m[2m │ gzip:  12.66 kB[22m[2m │ map:   402.91 kB[22m
[19:34:19.730] [2mdist/[22m[2massets/[22m[36msupabase-C3JVCGQq.js                  [39m[1m[2m  123.38 kB[22m[1m[22m[2m │ gzip:  34.32 kB[22m[2m │ map:   515.57 kB[22m
[19:34:19.731] [2mdist/[22m[2massets/[22m[36mindex-DqnUkSG4.js                     [39m[1m[33m1,622.09 kB[39m[22m[2m │ gzip: 482.09 kB[22m[2m │ map: 7,126.39 kB[22m
[19:34:19.731] [33m
[19:34:19.731] (!) Some chunks are larger than 500 kB after minification. Consider:
[19:34:19.731] - Using dynamic import() to code-split the application
[19:34:19.731] - Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
[19:34:19.731] - Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.[39m
[19:34:19.859] > Found 22 files
[19:34:19.862] > Analyzing 22 sources
[19:34:19.882] > Adding source map references
[19:34:20.462] > Bundled 22 files for upload
[19:34:20.463] > Bundle ID: 905c73c7-6f41-5389-ae71-cfdc707c39bd
[19:34:20.579] error: API request failed
[19:34:20.579] 
[19:34:20.579] Caused by:
[19:34:20.579]     sentry reported an error: You do not have permission to perform this action. (http status: 403)
[19:34:20.579] 
[19:34:20.580] Add --log-level=[info|debug] or export SENTRY_LOG_LEVEL=[info|debug] to see more output.
[19:34:20.580] Please attach the full debug log to all bug reports.
[19:34:20.584] [sentry-vite-plugin] Error: An error occurred. Couldn't finish all operations: Error: Command --header sentry-trace:334fa3b4219e4e75b8b8e7864848e663-9b3c591dce7dcf3a-1 --header baggage:sentry-environment=production,sentry-release=3.6.1,sentry-public_key=4c2bae7d9fbc413e8f7385f55c515d51,sentry-trace_id=334fa3b4219e4e75b8b8e7864848e663,sentry-sample_rate=1,sentry-transaction=Sentry%20Bundler%20Plugin%20execution,sentry-sampled=true sourcemaps upload --release c0771e74cadc822474c05c32e03942731597d553 /tmp/sentry-bundler-plugin-upload-fCo2j1 --ignore node_modules --no-rewrite failed with exit code 1
[19:34:20.584]     at ChildProcess.<anonymous> (/vercel/path0/node_modules/@sentry/cli/js/helper.js:343:18)
[19:34:20.586]     at ChildProcess.emit (node:events:518:28)
[19:34:20.586]     at ChildProcess._handle.onexit (node:internal/child_process:293:12)
[19:34:20.595] [32m✓ built in 17.62s[39m
[19:34:22.939] Build Completed in /vercel/output [28s]
[19:34:23.145] Deploying outputs...
[19:34:29.892] Deployment completed
[19:34:30.901] Creating build cache...
[19:35:09.890] Created build cache: 38.988s
[19:35:09.891] Uploading build cache [57.21 MB]
[19:35:11.063] Build cache uploaded: 1.173s